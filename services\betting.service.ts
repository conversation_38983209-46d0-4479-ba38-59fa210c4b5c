import { createClient } from '@/lib/supabase/client';
import { BettingOdds, CreateBettingOddsData, UpdateBettingOddsData } from '@/types/betting.types';
import { chatService } from './chat.service';

export class BettingService {
    private supabase;

    constructor(supabaseClient: any) {
        this.supabase = supabaseClient;
    }

    // Static method to create instance with default client for backward compatibility
    static createWithDefaultClient() {
        return new BettingService(createClient());
    }

    // Get all betting odds
    async getBettingOdds(): Promise<{ odds: BettingOdds[]; error: Error | null }> {
        try {
            const { data, error } = await this.supabase
                .from('betting_odds')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;

            return { odds: data || [], error: null };
        } catch (error) {
            console.error('Error fetching betting odds:', error);
            return { odds: [], error: error as Error };
        }
    }

    // Get betting odds by ID
    async getBettingOddsById(id: string): Promise<{ odds: BettingOdds | null; error: Error | null }> {
        try {
            const { data, error } = await this.supabase
                .from('betting_odds')
                .select('*')
                .eq('id', id)
                .single();

            if (error) throw error;

            return { odds: data, error: null };
        } catch (error) {
            console.error('Error fetching betting odds by ID:', error);
            return { odds: null, error: error as Error };
        }
    }

    // Create new betting odds
    async createBettingOdds(oddsData: CreateBettingOddsData): Promise<{ odds: BettingOdds | null; error: Error | null }> {
        try {

            // Use the expert data from the form (can be string or ExpertInfo object)
            const bettingData = {
                ...oddsData
            };

            const { data, error } = await this.supabase
                .from('betting_odds')
                .insert([bettingData])
                .select()
                .single();

            if (error) throw error;

            return { odds: data, error: null };
        } catch (error) {
            console.error('Error creating betting odds:', error);
            return { odds: null, error: error as Error };
        }
    }

    // Update betting odds
    async updateBettingOdds(id: string, oddsData: UpdateBettingOddsData): Promise<{ odds: BettingOdds | null; error: Error | null }> {
        try {

            // If status is being updated from null to win/lose, clear sent_at to allow resending
            const updateData = {
                ...oddsData,
                ...(oddsData.status && { sent_at: null })
            };

            const { data, error } = await this.supabase
                .from('betting_odds')
                .update(updateData)
                .eq('id', id)
                .select()
                .single();

            if (error) throw error;

            return { odds: data, error: null };
        } catch (error) {
            console.error('Error updating betting odds:', error);
            return { odds: null, error: error as Error };
        }
    }

    // Send betting odds (mark as sent)
    async sendBettingOdds(id: string): Promise<{ odds: BettingOdds | null; error: Error | null }> {
        try {
            console.log('📡 Sending betting odds for ID:', id);
            const sentAt = new Date().toISOString();
            console.log('📅 Setting sent_at to:', sentAt);

            // First, get the betting odds data
            const { data: oddsData, error: fetchError } = await this.supabase
                .from('betting_odds')
                .select('*')
                .eq('id', id)
                .single();

            if (fetchError) {
                console.error('❌ Error fetching betting odds:', fetchError);
                throw fetchError;
            }

            // Update sent_at timestamp
            const { data, error } = await this.supabase
                .from('betting_odds')
                .update({ sent_at: sentAt })
                .eq('id', id)
                .select()
                .single();

            if (error) {
                console.error('❌ Supabase error:', error);
                throw error;
            }

            // Send notification to group chat
            const groupChatId = '22dbb62a-8ff0-4095-bc8a-574106709db3';
            const { data: { user } } = await this.supabase.auth.getUser();

            if (user) {
                console.log('📤 Sending notification to group chat:', groupChatId);
                const { error: chatError } = await chatService.sendBettingOddsNotification(
                    groupChatId,
                    user.id,
                    oddsData
                );

                if (chatError) {
                    console.error('❌ Error sending chat notification:', chatError);
                    // Don't throw error here, just log it
                } else {
                    console.log('✅ Successfully sent chat notification');
                }
            }

            console.log('✅ Successfully sent betting odds:', data);
            return { odds: data, error: null };
        } catch (error) {
            console.error('❌ Error sending betting odds:', error);
            return { odds: null, error: error as Error };
        }
    }

    // Delete betting odds
    async deleteBettingOdds(id: string): Promise<{ error: Error | null }> {
        try {
            const { error } = await this.supabase
                .from('betting_odds')
                .delete()
                .eq('id', id);

            if (error) throw error;

            return { error: null };
        } catch (error) {
            console.error('Error deleting betting odds:', error);
            return { error: error as Error };
        }
    }

}

export const bettingService = BettingService.createWithDefaultClient();
