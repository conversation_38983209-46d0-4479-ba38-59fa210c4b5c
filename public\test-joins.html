<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Joins</title>
</head>
<body>
    <h1>Test Joins</h1>
    <div id="results"></div>
    
    <script>
        // Test script for joins in browser context
        console.log("🔗 Testing Joins in Browser Context...");

        // Test matches API with joins
        async function testMatchesWithJoins() {
            try {
                console.log("⚽ Testing matches API with joins...");
                
                // Test GET /api/matches with joins
                const response = await fetch("/api/matches?limit=3");
                if (response.ok) {
                    const matches = await response.json();
                    console.log("✅ Matches API with joins success");
                    console.log("📋 Sample match data:", matches[0]);
                    
                    // Check if we have team and league names
                    if (matches[0]) {
                        console.log("🏠 Home team:", matches[0].home_team);
                        console.log("✈️ Away team:", matches[0].away_team);
                        console.log("🏆 Competition:", matches[0].competition);
                        console.log("🏟️ Venue:", matches[0].venue);
                        console.log("🏠 Home team logo:", matches[0].home_team_logo);
                        console.log("✈️ Away team logo:", matches[0].away_team_logo);
                        console.log("🏆 League country:", matches[0].league_country);
                    }
                } else {
                    console.error("❌ Matches API with joins failed:", response.status);
                    const errorText = await response.text();
                    console.error("Error details:", errorText);
                }
            } catch (error) {
                console.error("❌ Matches API with joins test failed:", error);
            }
        }

        // Test direct Supabase query with joins
        async function testDirectSupabaseJoins() {
            try {
                console.log("🗄️ Testing direct Supabase joins...");
                
                // Test the exact query that our service uses
                const url = "https://service.ngoaihangtv.live/rest/v1/matches?select=*,home_team:teams!home_team_id(name,logo_url),away_team:teams!away_team_id(name,logo_url),league:leagues!league_id(name,country,logo_url)&limit=1";
                
                console.log("Testing URL:", url);
                
                const response = await fetch(url, {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5nb2FpaGFuZ3R2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU5MDA2NDgsImV4cCI6MjA1MTQ3NjY0OH0.8Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5nb2FpaGFuZ3R2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU5MDA2NDgsImV4cCI6MjA1MTQ3NjY0OH0.8Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q5Q'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    console.log("✅ Direct Supabase joins work");
                    console.log("📋 Raw joined data:", JSON.stringify(data[0], null, 2));
                    
                    // Display results in HTML
                    document.getElementById('results').innerHTML = `
                        <h2>✅ Direct Supabase joins work</h2>
                        <pre>${JSON.stringify(data[0], null, 2)}</pre>
                    `;
                } else {
                    console.error("❌ Direct Supabase joins failed:", response.status);
                    const errorText = await response.text();
                    console.error("Error details:", errorText);
                    
                    // Display error in HTML
                    document.getElementById('results').innerHTML = `
                        <h2>❌ Direct Supabase joins failed</h2>
                        <p>Status: ${response.status}</p>
                        <pre>${errorText}</pre>
                    `;
                }
            } catch (error) {
                console.error("❌ Direct Supabase joins test failed:", error);
                
                // Display error in HTML
                document.getElementById('results').innerHTML = `
                    <h2>❌ Direct Supabase joins test failed</h2>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // Run tests
        testDirectSupabaseJoins();
        testMatchesWithJoins();
    </script>
</body>
</html>
