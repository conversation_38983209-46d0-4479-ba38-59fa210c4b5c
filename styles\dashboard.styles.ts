import styled from "styled-components";
import { Layout } from "antd";

const { Header, Sider, Content } = Layout;

export const DashboardContainer = styled(Layout)`
  min-height: 100vh;
  background: #f5f5f5;
`;

export const StyledHeader = styled(Header)`
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
`;

export const StyledSider = styled(Sider)`
  background: #fff;
  border-right: 1px solid #f0f0f0;

  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
`;

export const MenuContainer = styled.div`
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .ant-menu {
    border: none;
    background: transparent;
  }

  .ant-menu-item {
    margin: 4px 0;
    border-radius: 8px;
    height: 48px;
    line-height: 48px;

    &.ant-menu-item-selected {
      background: #e6f7ff;
      color: #1890ff;
    }
  }
`;

export const BettingContainer = styled.div`
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
`;

export const BettingCard = styled.div`
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
`;

export const ChatContainer = styled.div`
  display: flex;
  height: calc(100vh - 64px);
`;

export const ChatList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
`;

export const ChatItem = styled.div<{ $active?: boolean }>`
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  background: ${props => (props.$active ? "#e6f7ff" : "transparent")};

  &:hover {
    background: #f5f5f5;
  }

  .chat-info {
    margin-left: 12px;
  }

  .chat-name {
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .last-message {
    color: #8c8c8c;
    font-size: 12px;
  }
`;

export const StyledContent = styled(Content)`
  background: #fff;
  margin: 0;
  display: flex;
  flex-direction: column;
`;

export const WelcomeContainer = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: #8c8c8c;

  .welcome-icon {
    font-size: 64px;
    margin-bottom: 16px;
    color: #d9d9d9;
  }
`;

export const ShareSection = styled.div`
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
`;
