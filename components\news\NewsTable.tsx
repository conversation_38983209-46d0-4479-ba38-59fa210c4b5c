'use client';

import React from 'react';
import {
  Table,
  Tag,
  Button,
  Space,
  Popconfirm,
  Tooltip,
  Image,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { PostWithSeo } from '@/types/news.types';
import type { ColumnsType } from 'antd/es/table';

interface NewsTableProps {
  data: PostWithSeo[];
  loading?: boolean;
  selectedRowKeys?: React.Key[];
  onSelectionChange?: (selectedRowKeys: React.Key[], selectedRows: PostWithSeo[]) => void;
  onEdit?: (record: PostWithSeo) => void;
  onDelete?: (id: string) => void;
  onView?: (record: PostWithSeo) => void;
  onPublish?: (id: string, isPublished: boolean) => void;
}

export const NewsTable: React.FC<NewsTableProps> = ({
  data,
  loading = false,
  selectedRowKeys = [],
  onSelectionChange,
  onEdit,
  onDelete,
  onView,
  onPublish,
}) => {
  const columns: ColumnsType<PostWithSeo> = [
    {
      title: 'Ảnh',
      dataIndex: 'thumbnail',
      key: 'thumbnail',
      width: 80,
      render: (thumbnail: string) => (
        thumbnail ? (
          <Image
            width={60}
            height={40}
            src={thumbnail}
            alt="Thumbnail"
            style={{ objectFit: 'cover', borderRadius: 4 }}
          />
        ) : (
          <div
            style={{
              width: 60,
              height: 40,
              backgroundColor: '#f5f5f5',
              borderRadius: 4,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              color: '#999'
            }}
          >
            Không có ảnh
          </div>
        )
      ),
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      render: (text: string, record: PostWithSeo) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
          </div>
        </div>
      ),
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string) => (
        <div style={{
          fontSize: '12px',
          color: '#666',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical'
        }}>
          {text || 'Chưa có mô tả'}
        </div>
      ),
    },
    {
      title: 'Tác giả',
      dataIndex: ['author', 'full_name'],
      key: 'author',
      width: 150,
      render: (text: string) => text || 'Không xác định',
    },
    {
      title: 'Trạng thái',
      key: 'status',
      width: 120,
      render: (_, record: PostWithSeo) => {
        const isPublished = record.published_at !== null;
        const config = isPublished
          ? { color: 'green', text: 'Đã xuất bản' }
          : { color: 'orange', text: 'Bản nháp' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
    },
    {
      title: 'Ngày xuất bản',
      dataIndex: 'published_at',
      key: 'published_at',
      width: 150,
      render: (date: string | null) =>
        date ? new Date(date).toLocaleDateString('vi-VN') : '-',
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record: PostWithSeo) => (
        <Space size="small">
          {onView && (
            <Tooltip title="Xem chi tiết">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => onView(record)}
              />
            </Tooltip>
          )}

          {onEdit && (
            <Tooltip title="Chỉnh sửa">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => onEdit(record)}
              />
            </Tooltip>
          )}

          {onPublish && (
            <Button
              type="text"
              onClick={() => onPublish(record.id, record.published_at !== null)}
              title={record.published_at !== null ? 'Hủy xuất bản' : 'Xuất bản'}
            >
              {record.published_at !== null ? 'Hủy XB' : 'Xuất bản'}
            </Button>
          )}

          {onDelete && (
            <Popconfirm
              title="Xóa bài viết"
              description="Bạn có chắc chắn muốn xóa bài viết này?"
              onConfirm={() => onDelete(record.id)}
              okText="Xóa"
              cancelText="Hủy"
              okType="danger"
            >
              <Tooltip title="Xóa">
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const rowSelection = onSelectionChange ? {
    selectedRowKeys,
    onChange: onSelectionChange,
    getCheckboxProps: (record: PostWithSeo) => ({
      name: record.title,
    }),
  } : undefined;

  return (
    <Table<PostWithSeo>
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      rowSelection={rowSelection}
      scroll={{ x: 1200 }}
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} của ${total} bài viết`,
        pageSizeOptions: ['10', '20', '50', '100'],
        defaultPageSize: 20,
      }}
    />
  );
};

export default NewsTable;