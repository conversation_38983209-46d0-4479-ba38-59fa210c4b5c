// Suppress Ant Design React compatibility warnings
// This is a workaround for the intermittent warning:
// "[antd: compatible] antd v5 support React is 16 ~ 18"

if (typeof window !== 'undefined') {
  const originalConsoleWarn = console.warn;
  
  console.warn = function(...args: any[]) {
    // Filter out antd compatibility warnings
    const message = args[0];
    if (typeof message === 'string' && message.includes('[antd: compatible]')) {
      return;
    }
    
    // Allow all other warnings to pass through
    originalConsoleWarn.apply(console, args);
  };
}

// Also suppress on server side if needed
if (typeof global !== 'undefined' && global.console) {
  const originalConsoleWarn = global.console.warn;
  
  global.console.warn = function(...args: any[]) {
    const message = args[0];
    if (typeof message === 'string' && message.includes('[antd: compatible]')) {
      return;
    }
    
    originalConsoleWarn.apply(global.console, args);
  };
}

export {};