"use client";

import React, { useState } from "react";
import {
  Avatar,
  Form,
  Button,
  Input,
  Space,
  Typography,
  Tooltip,
  message,
  Divider,
  Tag,
} from "antd";
import {
  LikeOutlined,
  LikeFilled,
  MessageOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  StopOutlined,
} from "@ant-design/icons";
import { useForumService } from "@/hooks/useForumService";
import type { ForumComment as ForumCommentType } from "@/types/forum.types";
import dayjs from "dayjs";

const { Text } = Typography;
const { TextArea } = Input;

interface ForumCommentProps {
  comment: ForumCommentType;
  postId: string;
  currentUserId?: string;
  level?: number;
  maxLevel?: number;
  onReply?: (comment: ForumCommentType, content: string) => void;
  onUpdate?: (commentId: string, content: string) => void;
  onDelete?: (commentId: string) => void;
  onApprove?: (commentId: string) => void;
  onReject?: (commentId: string) => void;
  onToggleLike?: (commentId: string) => void;
}

export default function ForumComment({
  comment,
  postId,
  currentUserId,
  level = 1,
  maxLevel = 3,
  onReply,
  onUpdate,
  onDelete,
  onApprove,
  onReject,
  onToggleLike,
}: ForumCommentProps) {
  const { updateComment, deleteComment, toggleCommentLike } = useForumService();
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [replyContent, setReplyContent] = useState("");
  const [editContent, setEditContent] = useState(comment.content);
  const [submitting, setSubmitting] = useState(false);
  const [liked, setLiked] = useState(false); // This should be checked from backend

  const isAuthor = currentUserId === comment.author_id;
  const canReply = level < maxLevel;

  const handleReply = async () => {
    if (!replyContent.trim()) return;

    setSubmitting(true);
    try {
      if (onReply) {
        await onReply(comment, replyContent);
      } else {
        // Use service directly if no callback provided
        await updateComment(comment.id, replyContent);
      }
      setReplyContent("");
      setShowReplyForm(false);
      message.success("Phản hồi thành công!");
    } catch (error) {
      message.error("Không thể phản hồi bình luận!");
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = async () => {
    if (!editContent.trim() || editContent === comment.content) return;

    setSubmitting(true);
    try {
      if (onUpdate) {
        await onUpdate(comment.id, editContent);
      } else {
        await updateComment(comment.id, editContent);
      }
      setShowEditForm(false);
      message.success("Cập nhật bình luận thành công!");
    } catch (error) {
      message.error("Không thể cập nhật bình luận!");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async () => {
    try {
      if (onDelete) {
        await onDelete(comment.id);
      } else {
        await deleteComment(comment.id);
      }
      message.success("Xóa bình luận thành công!");
    } catch (error) {
      message.error("Không thể xóa bình luận!");
    }
  };

  const handleToggleLikeClick = async () => {
    try {
      if (onToggleLike) {
        await onToggleLike(comment.id);
      } else {
        await toggleCommentLike(comment.id, currentUserId || "");
      }
      setLiked(!liked);
    } catch (error) {
      message.error("Không thể thích bình luận!");
    }
  };

  const getCommentLevelColor = (level: number) => {
    const colors = ["", "#f6ffed", "#fff7e6", "#fff1f0"];
    return colors[level] || colors[0];
  };

  const getCommentLevelBorder = (level: number) => {
    const borders = [
      "",
      "1px solid #b7eb8f",
      "1px solid #ffd591",
      "1px solid #ffccc7",
    ];
    return borders[level] || borders[0];
  };

  return (
    <div
      style={{
        marginLeft: level > 1 ? `${(level - 1) * 20}px` : 0,
        backgroundColor: getCommentLevelColor(level),
        border: getCommentLevelBorder(level),
        borderRadius: "8px",
        padding: level > 1 ? "12px" : "0",
        marginBottom: "12px",
      }}
    >
      <div style={{ display: "flex", gap: "12px" }}>
        {/* Avatar */}
        <Avatar src={comment.author?.avatar_url} size="large">
          {comment.author?.full_name?.[0]?.toUpperCase() || "U"}
        </Avatar>

        {/* Comment content */}
        <div style={{ flex: 1 }}>
          {/* Author and metadata */}
          <div style={{ marginBottom: "8px" }}>
            <Space>
              <Text strong>{comment.author?.full_name || "Người dùng"}</Text>
              {level > 1 && <Tag color="blue">Cấp {level}</Tag>}
              {comment.is_edited && (
                <Text type="secondary" style={{ fontSize: "12px" }}>
                  (Đã chỉnh sửa)
                </Text>
              )}
            </Space>
          </div>

          {/* Comment content */}
          {showEditForm ? (
            <div style={{ marginBottom: "12px" }}>
              <TextArea
                value={editContent}
                onChange={e => setEditContent(e.target.value)}
                rows={3}
                maxLength={2000}
              />
              <Space style={{ marginTop: 8 }}>
                <Button
                  size="small"
                  type="primary"
                  onClick={handleEdit}
                  loading={submitting}
                >
                  Cập nhật
                </Button>
                <Button
                  size="small"
                  onClick={() => {
                    setShowEditForm(false);
                    setEditContent(comment.content);
                  }}
                >
                  Hủy
                </Button>
              </Space>
            </div>
          ) : (
            <div style={{ marginBottom: "12px" }}>
              <div style={{ whiteSpace: "pre-wrap", marginBottom: "8px" }}>
                {comment.content}
              </div>

              {/* Admin actions for pending comments */}
              {comment.status === "pending" && (onApprove || onReject) && (
                <div style={{ marginBottom: 8 }}>
                  <Space size="small">
                    {onApprove && (
                      <Button
                        size="small"
                        type="primary"
                        icon={<CheckCircleOutlined />}
                        onClick={() => onApprove(comment.id)}
                      >
                        Duyệt
                      </Button>
                    )}
                    {onReject && (
                      <Button
                        size="small"
                        danger
                        icon={<StopOutlined />}
                        onClick={() => onReject(comment.id)}
                      >
                        Từ chối
                      </Button>
                    )}
                  </Space>
                </div>
              )}
            </div>
          )}

          {/* Actions and datetime */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Space>
              <Text type="secondary" style={{ fontSize: "12px" }}>
                {dayjs(comment.created_at).format("DD/MM/YYYY HH:mm")}
                {comment.edited_at && (
                  <span>
                    {" • "}Sửa{" "}
                    {dayjs(comment.edited_at).format("DD/MM/YYYY HH:mm")}
                  </span>
                )}
              </Text>

              {/* Like button */}
              <Button
                type="text"
                size="small"
                icon={liked ? <LikeFilled /> : <LikeOutlined />}
                onClick={handleToggleLikeClick}
              >
                {comment.like_count > 0 ? comment.like_count : ""}
              </Button>

              {/* Reply button */}
              {canReply && !showReplyForm && !showEditForm && (
                <Button
                  type="text"
                  size="small"
                  icon={<MessageOutlined />}
                  onClick={() => setShowReplyForm(true)}
                >
                  Phản hồi
                </Button>
              )}

              {/* Edit/Delete buttons for author */}
              {isAuthor && !showReplyForm && !showEditForm && (
                <Space>
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => setShowEditForm(true)}
                  >
                    Sửa
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleDelete}
                  >
                    Xóa
                  </Button>
                </Space>
              )}
            </Space>
          </div>

          {/* Reply form */}
          {showReplyForm && (
            <div style={{ marginTop: 12 }}>
              <TextArea
                placeholder="Viết phản hồi..."
                value={replyContent}
                onChange={e => setReplyContent(e.target.value)}
                rows={2}
                maxLength={2000}
                style={{ marginBottom: 8 }}
              />
              <Space>
                <Button
                  size="small"
                  type="primary"
                  onClick={handleReply}
                  loading={submitting}
                >
                  Phản hồi
                </Button>
                <Button
                  size="small"
                  onClick={() => {
                    setShowReplyForm(false);
                    setReplyContent("");
                  }}
                >
                  Hủy
                </Button>
              </Space>
            </div>
          )}
        </div>
      </div>

      {/* Nested comments would be rendered here by parent component */}
    </div>
  );
}
