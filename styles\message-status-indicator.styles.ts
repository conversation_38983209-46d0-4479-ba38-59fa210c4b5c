import { CheckOutlined } from "@ant-design/icons";
import styled from "styled-components";

export const StatusContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: 4px;
`;

export const StatusIcon = styled(CheckOutlined)<{ $status: string }>`
  font-size: 12px;
  color: ${props => {
    switch (props.$status) {
      case "sent":
        return "#d9d9d9";
      case "delivered":
        return "#52c41a";
      case "read":
        return "#1890ff";
      default:
        return "#d9d9d9";
    }
  }};
`;
