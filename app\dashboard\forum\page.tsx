"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  message,
  Popconfirm,
  Typography,
  Card,
  Row,
  Col,
  Badge,
  Tooltip,
  Statistic,
  Divider,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
  PushpinOutlined,
  PushpinFilled,
  CheckCircleOutlined,
  StopOutlined,
  CommentOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useForumService } from "@/hooks/useForumService";
import type {
  ForumPost,
  ForumPostFilters,
  PostStatus,
} from "@/types/forum.types";
import dayjs from "dayjs";

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

export default function ForumManagementPage() {
  const router = useRouter();
  const {
    posts,
    categories,
    stats,
    loading,
    error,
    fetchPosts,
    fetchCategories,
    fetchStats,
    deletePost,
    approvePost,
    rejectPost,
    togglePinPost,
  } = useForumService();

  const [filters, setFilters] = useState<ForumPostFilters>({
    status: undefined,
    search: "",
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  useEffect(() => {
    fetchPosts(filters);
    fetchCategories();
    fetchStats();
  }, [fetchPosts, fetchCategories, fetchStats, filters]);

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  const handleStatusFilter = (status: PostStatus | undefined) => {
    setFilters(prev => ({ ...prev, status }));
  };

  const handleCategoryFilter = (categoryId: string | undefined) => {
    setFilters(prev => ({ ...prev, category_id: categoryId }));
  };

  const handleDelete = async (id: string) => {
    try {
      const success = await deletePost(id);
      if (success) {
        message.success("Xóa bài viết thành công!");
        fetchPosts(filters);
        fetchStats();
      } else {
        message.error("Không thể xóa bài viết!");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi xóa bài viết!");
    }
  };

  const handleApprove = async (id: string) => {
    try {
      const result = await approvePost(id);
      if (result) {
        message.success("Duyệt bài viết thành công!");
        fetchPosts(filters);
        fetchStats();
      } else {
        message.error("Không thể duyệt bài viết!");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi duyệt bài viết!");
    }
  };

  const handleReject = async (id: string) => {
    try {
      const result = await rejectPost(id);
      if (result) {
        message.success("Từ chối bài viết thành công!");
        fetchPosts(filters);
        fetchStats();
      } else {
        message.error("Không thể từ chối bài viết!");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi từ chối bài viết!");
    }
  };

  const handleTogglePin = async (id: string, isPinned: boolean) => {
    try {
      const result = await togglePinPost(id, !isPinned);
      if (result) {
        message.success(
          `${isPinned ? "Bỏ ghim" : "Ghim"} bài viết thành công!`
        );
        fetchPosts(filters);
      } else {
        message.error(`Không thể ${isPinned ? "bỏ ghim" : "ghim"} bài viết!`);
      }
    } catch (error) {
      message.error("Có lỗi xảy ra!");
    }
  };

  const handleRefresh = () => {
    fetchPosts(filters);
    fetchCategories();
    fetchStats();
  };

  const getStatusTag = (status: PostStatus, isPinned: boolean) => {
    const statusConfig = {
      pending: { color: "orange", text: "Chờ duyệt" },
      approved: { color: "green", text: "Đã duyệt" },
      rejected: { color: "red", text: "Từ chối" },
      draft: { color: "blue", text: "Bản nháp" },
    };

    const config = statusConfig[status] || { color: "default", text: status };

    return (
      <Space direction="vertical" size={0}>
        <Tag color={config.color}>{config.text}</Tag>
        {isPinned && (
          <Tag color="purple" icon={<PushpinOutlined />}>
            Đã ghim
          </Tag>
        )}
      </Space>
    );
  };

  const columns = [
    {
      title: "Tiêu đề",
      dataIndex: "title",
      key: "title",
      width: 300,
      ellipsis: true,
      render: (text: string, record: ForumPost) => (
        <div>
          <div style={{ fontWeight: "bold", marginBottom: 4 }}>{text}</div>
          <div style={{ fontSize: "12px", color: "#666" }}>
            Slug: {record.slug}
          </div>
          {record.excerpt && (
            <div style={{ fontSize: "11px", color: "#999", marginTop: 2 }}>
              {record.excerpt.substring(0, 50)}...
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Danh mục",
      dataIndex: "category",
      key: "category",
      width: 150,
      render: (category: any) => category?.name || "Chưa phân loại",
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status: PostStatus, record: ForumPost) =>
        getStatusTag(status, record.is_pinned),
    },
    {
      title: "Tác giả",
      dataIndex: "author",
      key: "author",
      width: 150,
      render: (author: any) => author?.full_name || "Không xác định",
    },
    {
      title: "Thống kê",
      key: "stats",
      width: 150,
      render: (record: ForumPost) => (
        <Space direction="vertical" size={0}>
          <div style={{ fontSize: "12px" }}>
            <EyeOutlined style={{ marginRight: 4 }} />
            {record.view_count}
          </div>
          <div style={{ fontSize: "12px" }}>
            <CommentOutlined style={{ marginRight: 4 }} />
            {record.comment_count}
          </div>
        </Space>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "created_at",
      key: "created_at",
      width: 150,
      render: (date: string) => dayjs(date).format("DD/MM/YYYY HH:mm"),
      sorter: (a: ForumPost, b: ForumPost) =>
        dayjs(a.created_at).unix() - dayjs(b.created_at).unix(),
    },
    {
      title: "Ngày cập nhật",
      dataIndex: "updated_at",
      key: "updated_at",
      width: 150,
      render: (date: string) => dayjs(date).format("DD/MM/YYYY HH:mm"),
    },
    {
      title: "Thao tác",
      key: "actions",
      width: 250,
      render: (record: ForumPost) => (
        <Space size="small" wrap>
          <Tooltip title="Xem chi tiết">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => router.push(`/dashboard/forum/${record.id}`)}
            />
          </Tooltip>

          <Tooltip title="Chỉnh sửa">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => router.push(`/dashboard/forum/edit/${record.id}`)}
            />
          </Tooltip>

          <Tooltip title={record.is_pinned ? "Bỏ ghim" : "Ghim bài viết"}>
            <Button
              type="text"
              icon={record.is_pinned ? <PushpinFilled /> : <PushpinOutlined />}
              onClick={() => handleTogglePin(record.id, record.is_pinned)}
            />
          </Tooltip>

          {record.status === "pending" && (
            <>
              <Tooltip title="Duyệt bài viết">
                <Button
                  type="text"
                  icon={<CheckCircleOutlined />}
                  style={{ color: "#52c41a" }}
                  onClick={() => handleApprove(record.id)}
                />
              </Tooltip>

              <Tooltip title="Từ chối bài viết">
                <Button
                  type="text"
                  icon={<StopOutlined />}
                  style={{ color: "#ff4d4f" }}
                  onClick={() => handleReject(record.id)}
                />
              </Tooltip>
            </>
          )}

          <Popconfirm
            title="Bạn có chắc chắn muốn xóa bài viết này?"
            description="Hành động này không thể hoàn tác và sẽ xóa tất cả bình luận liên quan."
            onConfirm={() => handleDelete(record.id)}
            okText="Xóa"
            cancelText="Hủy"
            okType="danger"
          >
            <Tooltip title="Xóa bài viết">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
  };

  return (
    <div style={{ padding: "24px" }}>
      <Card>
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: 16 }}
        >
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              Quản lý Diễn đàn
            </Title>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => router.push("/dashboard/forum/create")}
              >
                Tạo bài viết mới
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                Làm mới
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Statistics Cards */}
        {stats && (
          <>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="Tổng bài viết"
                    value={stats.total_posts}
                    prefix={<EditOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="Tổng bình luận"
                    value={stats.total_comments}
                    prefix={<CommentOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="Bài viết hôm nay"
                    value={stats.posts_today}
                    prefix={<PlusOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="Chờ duyệt"
                    value={stats.pending_posts}
                    valueStyle={{
                      color: stats.pending_posts > 0 ? "#faad14" : "#3f8600",
                    }}
                    prefix={<EyeInvisibleOutlined />}
                  />
                </Card>
              </Col>
            </Row>
            <Divider style={{ margin: "16px 0" }} />
          </>
        )}

        {/* Filters */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Search
              placeholder="Tìm kiếm theo tiêu đề, nội dung..."
              allowClear
              onSearch={handleSearch}
              style={{ width: "100%" }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Trạng thái"
              allowClear
              style={{ width: "100%" }}
              onChange={handleStatusFilter}
            >
              <Option value="pending">Chờ duyệt</Option>
              <Option value="approved">Đã duyệt</Option>
              <Option value="rejected">Từ chối</Option>
              <Option value="draft">Bản nháp</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Danh mục"
              allowClear
              style={{ width: "100%" }}
              onChange={handleCategoryFilter}
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Bài viết ghim"
              allowClear
              style={{ width: "100%" }}
              onChange={value =>
                setFilters(prev => ({ ...prev, is_pinned: value }))
              }
            >
              <Option value={true}>Đã ghim</Option>
              <Option value={false}>Chưa ghim</Option>
            </Select>
          </Col>
        </Row>

        {/* Bulk Actions */}
        {selectedRowKeys.length > 0 && (
          <Row style={{ marginBottom: 16 }}>
            <Col>
              <Space>
                <span>Đã chọn {selectedRowKeys.length} bài viết</span>
                <Button size="small" type="primary">
                  Duyệt hàng loạt
                </Button>
                <Button size="small">Từ chối hàng loạt</Button>
                <Button size="small">Ghim hàng loạt</Button>
                <Button size="small" danger>
                  Xóa hàng loạt
                </Button>
              </Space>
            </Col>
          </Row>
        )}

        {/* Error Display */}
        {error && (
          <div style={{ marginBottom: 16, color: "red" }}>Lỗi: {error}</div>
        )}

        {/* Table */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={posts}
          rowKey="id"
          loading={loading}
          pagination={{
            total: posts.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} bài viết`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>
    </div>
  );
}
