import { Button, Input, Typography } from "antd";
import styled from "styled-components";

const { Text } = Typography;

export const ChatWrapper = styled.div`
  display: flex;
  height: calc(100vh - 64px);
  background: #f8fafc;
`;

export const ChatListPanel = styled.div`
  width: 320px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
`;

export const ChatListHeader = styled.div`
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
`;

export const ChatTitleContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const SearchContainer = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: white;

  .ant-input-search {
    .ant-input {
      border: 1px solid #e5e7eb;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
      }
    }

    .ant-input-search-button {
      border-radius: 0 20px 20px 0;
      border-left: none;

      &:hover {
        border-color: #1890ff;
      }
    }
  }
`;

export const ChatListContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
`;

export const SectionHeader = styled.div`
  padding: 12px 20px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

export const ChatItem = styled.div<{ $active?: boolean }>`
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  background: ${props => (props.$active ? "#eff6ff" : "transparent")};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;

  &:hover {
    background: ${props => (props.$active ? "#eff6ff" : "#f9fafb")};
  }

  &:last-child {
    border-bottom: none;
  }
`;

export const ChatInfo = styled.div`
  flex: 1;
  min-width: 0;

  .chat-name {
    font-weight: 500;
    color: #111827;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .last-message {
    color: #6b7280;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

export const OnlineIndicator = styled.div<{ $online?: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => (props.$online ? "#10b981" : "#d1d5db")};
  border: 2px solid white;
  position: absolute;
  bottom: 0;
  right: 0;
`;

export const ConversationPanel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
`;

export const ConversationHeader = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const UserDetails = styled.div`
  .user-name {
    font-weight: 600;
    color: #111827;
    margin-bottom: 2px;
  }

  .user-status {
    font-size: 12px;
    color: #10b981;
  }
`;

export const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  background: #f9f9f9;
  max-height: calc(100vh - 200px);
  scroll-behavior: smooth;
  position: relative;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

export const MessageBubble = styled.div<{ $isOwn?: boolean }>`
  display: flex;
  justify-content: ${props => (props.$isOwn ? "flex-end" : "flex-start")};
  margin-bottom: 8px;
`;

export const MessageContent = styled.div<{ $isOwn?: boolean }>`
  max-width: 70%;
  padding: 6px 6px;
  border-radius: 18px;
  color: #111827;
  position: relative;

  .message-text {
    margin: 0;
    line-height: 1.4;
    word-wrap: break-word;
    color: #111827;
  }

  .message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
    text-align: ${props => (props.$isOwn ? "right" : "left")};
    color: #111827;
  }
`;

export const MessageStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  margin-top: 4px;
  justify-content: flex-end;
`;

export const TickIcon = styled.span<{ $read?: boolean }>`
  color: ${props => (props.$read ? "#0084ff" : "#999")};
  font-size: 12px;
  transform: ${props => (props.$read ? "translateX(-1px)" : "none")};
`;

export const InputContainer = styled.div`
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: white;
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const MessageInput = styled(Input.TextArea)`
  flex: 1;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  resize: none;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
`;

export const SendButton = styled(Button)`
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  background: #1890ff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #2563eb;
  }

  &:disabled {
    background: #d1d5db;
  }
`;

export const ScrollToBottomButton = styled(Button)`
  position: absolute;
  bottom: 80px;
  right: 20px;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  border-radius: 50% !important;
  background: #1890ff !important;
  border: none !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;

  &:hover {
    background: #2563eb !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &:focus {
    background: #2563eb !important;
  }
`;

export const ChatPreview = styled(Text)`
  max-width: 200px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const AvatarContainer = styled.div`
  position: relative;
  flex-shrink: 0;
`;

export const GroupAvatarContainer = styled.div`
  position: relative;
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
`;
