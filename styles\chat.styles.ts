import styled from "styled-components";
import { Card } from "antd";

export const ChatContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

export const ChatCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  height: 600px;
  display: flex;
  flex-direction: column;

  .ant-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0;
  }
`;

export const ChatHeader = styled.div`
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
`;

export const MessagesArea = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

export const MessageBubble = styled.div<{ $isOwn: boolean }>`
  display: flex;
  flex-direction: ${props => (props.$isOwn ? "row-reverse" : "row")};
  gap: 8px;
  align-items: flex-end;
`;

export const MessageContent = styled.div<{ $isOwn: boolean }>`
  max-width: 70%;
  padding: 10px 14px;
  border-radius: 16px;
  background: ${props => (props.$isOwn ? "#1677ff" : "#f5f5f5")};
  color: ${props => (props.$isOwn ? "#ffffff" : "#000000")};
`;

export const InputArea = styled.div`
  padding: 16px;
  border-top: 1px solid #f0f0f0;
`;

export const NameInputArea = styled.div`
  padding: 20px;
  text-align: center;
`;
