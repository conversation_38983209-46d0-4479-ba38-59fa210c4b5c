"use client";

import { useChatService, useTabVisibility } from "@/hooks";
import {
  DeleteOutlined,
  DownOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  PlusOutlined,
  SearchOutlined,
  SendOutlined,
  TeamOutlined
} from "@ant-design/icons";
import type { User } from "@supabase/supabase-js";
import type { Profile } from "@/types";
import {
  App,
  Avatar,
  Button,
  Dropdown,
  Input,
  message,
  Skeleton,
  Typography,
} from "antd";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  AvatarContainer,
  ChatInfo,
  ChatContainerChatItem as ChatItem,
  ChatListContent,
  ChatListHeader,
  ChatListPanel,
  ChatPreview,
  ChatTitleContainer,
  ChatWrapper,
  ConversationHeader,
  ConversationPanel,
  GroupAvatarContainer,
  HeaderLeft,
  HeaderRight,
  ChatContainerInputContainer as InputContainer,
  ChatContainerMessageBubble as MessageBubble,
  ChatContainerMessageContent as MessageContent,
  MessageInput,
  ChatContainerMessagesContainer as MessagesContainer,
  OnlineIndicator,
  ScrollToBottomButton,
  SearchContainer,
  SectionHeader,
  SendButton,
  UserDetails,
  UserInfo,
} from "../styles";
import CreateChatModal from "./CreateChatModal";
import SendingStatusIndicator from "./SendingStatusIndicator";

const { Title, Text } = Typography;

interface ChatContainerProps {
  user: User;
  profile: Profile;
}

export default function ChatContainer({ user, profile }: ChatContainerProps) {
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [messageText, setMessageText] = useState("");
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [subscriptionCleanup, setSubscriptionCleanup] = useState<(() => void) | null>(null);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [lastReconnectTime, setLastReconnectTime] = useState(0);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [lastSendTime, setLastSendTime] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const isSubscribingRef = useRef<boolean>(false);
  const currentRoomRef = useRef<string | null>(null);
  const subscriptionActiveRef = useRef<boolean>(false);
  const { modal, message } = App.useApp();
  const { isVisible, isOnline } = useTabVisibility();

  const {
    chatRooms,
    messages,
    setMessages,
    loading,
    messagesLoading,
    loadChatRooms,
    loadMessages,
    sendMessage,
    deleteChat,
    subscribeToMessages,
  } = useChatService(user.id, profile);

  useEffect(() => {
    loadChatRooms();
  }, [user]); // Remove loadChatRooms from dependencies

  useEffect(() => {
    if (selectedChat && selectedChat !== currentRoomRef.current) {
      // Clean up previous subscription
      if (subscriptionCleanup) {
        subscriptionCleanup();
        setSubscriptionCleanup(null);
        subscriptionActiveRef.current = false;
      }

      // Update current room ref
      currentRoomRef.current = selectedChat;

      // Reset messages first
      setMessages([]);

      // Load messages and subscribe
      loadMessages(selectedChat).then(() => {
        // Double check if we still need this subscription (prevent race condition)
        if (currentRoomRef.current === selectedChat) {
          const cleanup = subscribeToMessages(selectedChat, () => {
            // Handle connection lost - reset subscription state
            subscriptionActiveRef.current = false;
            setSubscriptionCleanup(null);
          });
          setSubscriptionCleanup(() => cleanup);
          subscriptionActiveRef.current = true;
        }
      }).catch((error) => {
        console.error('Error loading messages for chat:', selectedChat, error);
        subscriptionActiveRef.current = false;
        setSubscriptionCleanup(null);
      });
    } else if (selectedChat && selectedChat === currentRoomRef.current) {
      // If same chat but no subscription, create one
      if (!subscriptionActiveRef.current || !subscriptionCleanup) {
        loadMessages(selectedChat).then(() => {
          // Double check if we still need this subscription (prevent race condition)
          if (currentRoomRef.current === selectedChat) {
            const cleanup = subscribeToMessages(selectedChat, () => {
              // Handle connection lost - reset subscription state
              subscriptionActiveRef.current = false;
              setSubscriptionCleanup(null);
            });
            setSubscriptionCleanup(() => cleanup);
            subscriptionActiveRef.current = true;
          }
        }).catch((error) => {
          console.error('Error creating subscription for same chat:', selectedChat, error);
        });
      }
    }

    return () => {
      if (subscriptionCleanup) {
        subscriptionCleanup();
        subscriptionActiveRef.current = false;
      }
    };
  }, [selectedChat]); // Remove loadMessages, subscribeToMessages to prevent constant re-renders

  useEffect(() => {
    scrollToBottom();
  }, [messages, sendingMessage]);

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      const handleScroll = () => {
        const { scrollTop, scrollHeight, clientHeight } = container;
        const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
        setShowScrollButton(!isNearBottom);
      };

      container.addEventListener("scroll", handleScroll);
      return () => container.removeEventListener("scroll", handleScroll);
    }
  }, []);


  const handleSendMessage = async () => {
    if (!messageText.trim() || !selectedChat) return;

    // Prevent duplicate sends
    if (sendingMessage) return;

    // Throttle message sending (minimum 500ms between messages)
    const now = Date.now();
    if (now - lastSendTime < 500) {
      return;
    }

    setSendingMessage(true);
    setLastSendTime(now);

    try {
      const { error } = await sendMessage(selectedChat, messageText.trim());
      if (error) {
        console.error("Error sending message:", error);
        // Show user-friendly error message
        message.error('Không thể gửi tin nhắn. Vui lòng thử lại.');
        setSendingMessage(false);
        return;
      }
      setMessageText("");
    } catch (error) {
      console.error("Unexpected error sending message:", error);
      message.error('Có lỗi xảy ra khi gửi tin nhắn.');
      setSendingMessage(false);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleChatSelect = useCallback(
    (roomId: string) => {
      // Force refresh when selecting a chat
      setRefreshKey(prev => prev + 1);
      setSelectedChat(roomId);
    },
    [selectedChat]
  );

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleDeleteChat = async (roomId: string, roomName: string) => {
    modal.confirm({
      title: "Xóa Chat",
      content: `Bạn có chắc chắn muốn xóa "${roomName}"? Hành động này không thể hoàn tác.`,
      okText: "Xóa",
      okType: "danger",
      cancelText: "Hủy",
      onOk: async () => {
        const { error } = await deleteChat(roomId);

        if (error) {
          console.error("Error deleting chat:", error);
          message.error("Không thể xóa chat. Vui lòng thử lại.");
          return;
        }

        message.success("Chat đã được xóa thành công!");

        if (selectedChat === roomId) {
          setSelectedChat(null);
        }
      },
    });
  };

  const handleCreateChatSuccess = () => {
    loadChatRooms();
    setShowCreateModal(false);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (subscriptionCleanup) {
        subscriptionCleanup();
      }
    };
  }, [subscriptionCleanup]);

  // Handle external tab switching - only reload chat rooms, don't interfere with chat selection
  useEffect(() => {
    if (isVisible && isOnline) {
      const now = Date.now();
      const timeSinceLastReconnect = now - lastReconnectTime;

      // Debounce: only reconnect if it's been more than 2 seconds since last reconnect
      if (timeSinceLastReconnect < 2000) {
        return;
      }

      setLastReconnectTime(now);

      // Only reload chat rooms, don't interfere with current chat selection
      loadChatRooms();
    }
  }, [isVisible, isOnline, loadChatRooms, lastReconnectTime]);

  // Handle offline status
  useEffect(() => {
    if (!isOnline && subscriptionCleanup) {
      subscriptionCleanup();
      setSubscriptionCleanup(null);
      subscriptionActiveRef.current = false;
    }
  }, [isOnline]);

  // Handle subscription reconnection when tab becomes visible and we have a selected chat
  useEffect(() => {
    if (isVisible && isOnline && selectedChat && !subscriptionActiveRef.current && !subscriptionCleanup && !isReconnecting) {
      // Prevent multiple reconnection attempts
      setIsReconnecting(true);

      // Debounce reconnection
      const reconnectTimer = setTimeout(() => {
        // Double check conditions before reconnecting
        if (selectedChat && !subscriptionActiveRef.current && !subscriptionCleanup && currentRoomRef.current === selectedChat) {
          loadMessages(selectedChat).then(() => {
            // Triple check if we still need this subscription (prevent race condition)
            if (selectedChat && currentRoomRef.current === selectedChat && !subscriptionActiveRef.current) {
              const cleanup = subscribeToMessages(selectedChat, () => {
                // Handle connection lost - reset subscription state
                subscriptionActiveRef.current = false;
                setSubscriptionCleanup(null);
              });
              setSubscriptionCleanup(() => cleanup);
              subscriptionActiveRef.current = true;
              setIsReconnecting(false);
            } else {
              setIsReconnecting(false);
            }
          }).catch((error) => {
            console.error('Error reconnecting subscription:', error);
            subscriptionActiveRef.current = false;
            setSubscriptionCleanup(null);
            setIsReconnecting(false);
          });
        } else {
          setIsReconnecting(false);
        }
      }, 500); // 500ms debounce

      return () => {
        clearTimeout(reconnectTimer);
        setIsReconnecting(false);
      };
    } else if (isVisible && isOnline && selectedChat && subscriptionActiveRef.current) {
      // Reload messages to ensure we have the latest data when tab becomes visible
      loadMessages(selectedChat).catch((error) => {
        console.error('Error reloading messages on tab focus:', error);
      });
    }
  }, [isVisible, isOnline, selectedChat]); // Remove function dependencies to prevent constant re-renders


  const selectedRoom = chatRooms.find(room => room.id === selectedChat);

  const directChats = chatRooms.filter(room => room.type === "direct");

  const groupChats = chatRooms.filter(
    room => room.type === "general" || room.type === "group"
  );

  return (
    <>
      <ChatWrapper>
        <ChatListPanel>
          <ChatListHeader>
            <ChatTitleContainer>
              <Title level={3} style={{ margin: 0 }}>
                Chat
              </Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setShowCreateModal(true)}
                size="small"
              >
                Tạo Chat
              </Button>
            </ChatTitleContainer>
          </ChatListHeader>

          <SearchContainer>
            <Input.Search
              placeholder="Tìm kiếm cuộc trò chuyện..."
              prefix={<SearchOutlined />}
              allowClear
            />
          </SearchContainer>

          <ChatListContent>
            {loading ? (
              <div style={{ padding: "16px" }}>
                <Skeleton.Input active style={{ width: "40%", marginBottom: "16px" }} />
                <Skeleton avatar active paragraph={{ rows: 1 }} style={{ marginBottom: "12px" }} />
                <Skeleton avatar active paragraph={{ rows: 1 }} style={{ marginBottom: "12px" }} />
                <Skeleton avatar active paragraph={{ rows: 1 }} style={{ marginBottom: "16px" }} />
                <Skeleton.Input active style={{ width: "30%", marginBottom: "16px" }} />
                <Skeleton avatar active paragraph={{ rows: 1 }} style={{ marginBottom: "12px" }} />
                <Skeleton avatar active paragraph={{ rows: 1 }} />
              </div>
            ) : (
              <>
                {/* Direct Messages */}
                <SectionHeader>
                  <div className="section-title">
                    <TeamOutlined />
                    <Text strong>Direct Messages</Text>
                  </div>
                </SectionHeader>

                {directChats.map(room => (
                  <ChatItem
                    key={`${room.id}-${refreshKey}`}
                    $active={selectedChat === room.id}
                    onClick={() => handleChatSelect(room.id)}
                  >
                    <AvatarContainer>
                      <Avatar
                        src={room.other_user?.avatar_url}
                        icon={<TeamOutlined />}
                        size={48}
                      />
                      <OnlineIndicator $online={true} />
                    </AvatarContainer>
                    <ChatInfo>
                      <Text className="chat-name">
                        {room.other_user?.full_name || room.name}
                      </Text>
                      <ChatPreview className="last-message">
                        {room.last_message || "Chưa có tin nhắn"}
                      </ChatPreview>
                    </ChatInfo>
                    {room.type !== "general" && (
                      <Dropdown
                        menu={{
                          items: [
                            {
                              key: "delete",
                              label: "Xóa chat",
                              icon: <DeleteOutlined />,
                              danger: true,
                              onClick: () => handleDeleteChat(room.id, room.name),
                            },
                          ],
                        }}
                        trigger={["click"]}
                      >
                        <Button
                          type="text"
                          icon={<MoreOutlined />}
                          size="small"
                          onClick={e => e.stopPropagation()}
                        />
                      </Dropdown>
                    )}
                  </ChatItem>
                ))}

                {/* Channels */}
                <SectionHeader>
                  <div className="section-title">
                    <TeamOutlined />
                    <Text strong>Channels</Text>
                  </div>
                </SectionHeader>

                {groupChats.map(room => (
                  <ChatItem
                    key={`${room.id}-${refreshKey}`}
                    $active={selectedChat === room.id}
                    onClick={() => handleChatSelect(room.id)}
                  >
                    <GroupAvatarContainer>
                      <TeamOutlined />
                    </GroupAvatarContainer>
                    <ChatInfo>
                      <Text className="chat-name">{room.name}</Text>
                      <ChatPreview className="last-message">
                        {room.last_message || "Chưa có tin nhắn"}
                      </ChatPreview>
                    </ChatInfo>
                    {room.type !== "general" && (
                      <Dropdown
                        menu={{
                          items: [
                            {
                              key: "delete",
                              label: "Xóa chat",
                              icon: <DeleteOutlined />,
                              danger: true,
                              onClick: () => handleDeleteChat(room.id, room.name),
                            },
                          ],
                        }}
                        trigger={["click"]}
                      >
                        <Button
                          type="text"
                          icon={<MoreOutlined />}
                          size="small"
                          onClick={e => e.stopPropagation()}
                        />
                      </Dropdown>
                    )}
                  </ChatItem>
                ))}
              </>
            )}
          </ChatListContent>
        </ChatListPanel>

        <ConversationPanel>
          {selectedRoom ? (
            <>
              <ConversationHeader>
                <HeaderLeft>
                  <UserInfo>
                    {selectedRoom.type === "direct" ? (
                      <Avatar
                        src={selectedRoom.other_user?.avatar_url}
                        icon={<TeamOutlined />}
                        size={40}
                      />
                    ) : (
                      <GroupAvatarContainer
                        style={{ width: 40, height: 40, fontSize: 16 }}
                      >
                        <TeamOutlined />
                      </GroupAvatarContainer>
                    )}
                    <UserDetails>
                      <Text className="user-name" >
                        {selectedRoom.type === "direct"
                          ? selectedRoom.other_user?.full_name || selectedRoom.name
                          : selectedRoom.name}
                      </Text>
                      <br></br>
                      <Text className="user-status">
                        {isReconnecting ? 'Đang kết nối lại...' : 'Online'}
                      </Text>
                    </UserDetails>
                  </UserInfo>
                </HeaderLeft>
                <HeaderRight>
                  <Button
                    type="text"
                    icon={<InfoCircleOutlined />}
                    size="small"
                  />
                </HeaderRight>
              </ConversationHeader>

              <MessagesContainer ref={messagesContainerRef}>
                {messagesLoading || isReconnecting ? (
                  <div style={{ padding: "16px" }}>
                    <Skeleton.Input active style={{ width: "60%", marginBottom: "12px" }} />
                    <Skeleton.Input active style={{ width: "80%", marginBottom: "12px" }} />
                    <Skeleton.Input active style={{ width: "45%", marginBottom: "12px" }} />
                    <Skeleton.Input active style={{ width: "70%", marginBottom: "12px" }} />
                    <Skeleton.Input active style={{ width: "55%" }} />
                  </div>
                ) : (
                  messages.map(message => {
                    const isOwn = message.user_id === user.id;
                    return (
                      <MessageBubble key={message.id} $isOwn={isOwn}>
                        {!isOwn && (
                          <div style={{ marginBottom: '4px', width: '100%' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                              {message.user?.avatar_url ? (
                                <Avatar
                                  src={message.user.avatar_url}
                                  size={32}
                                  style={{ marginRight: '8px' }}
                                />
                              ) : (
                                <Avatar
                                  size={32}
                                  style={{
                                    marginRight: '8px',
                                    backgroundColor: '#1890ff',
                                    color: 'white',
                                    fontWeight: 'bold'
                                  }}
                                >
                                  {message.user?.full_name?.charAt(0)?.toUpperCase() || 'U'}
                                </Avatar>
                              )}
                              <Text style={{ fontSize: '14px', color: '#333', fontWeight: '600' }}>
                                {message.user?.full_name || 'Unknown User'}
                              </Text>
                            </div>
                            <div style={{ marginLeft: '40px' }}>
                              <MessageContent $isOwn={isOwn}>
                                <Text className="message-text">{message.content}</Text>
                              </MessageContent>
                            </div>
                          </div>
                        )}
                        {isOwn && (
                          <MessageContent $isOwn={isOwn}>
                            <Text className="message-text">{message.content}</Text>
                          </MessageContent>
                        )}
                      </MessageBubble>
                    );
                  })
                )}

                {/* Hiển thị status đang gửi riêng biệt */}
                <SendingStatusIndicator
                  isVisible={sendingMessage}
                  message="Đang gửi tin nhắn"
                />

                <div ref={messagesEndRef} />
              </MessagesContainer>

              {showScrollButton && (
                <ScrollToBottomButton
                  type="primary"
                  icon={<DownOutlined />}
                  onClick={scrollToBottom}
                />
              )}

              <InputContainer>
                <MessageInput
                  value={messageText}
                  onChange={e => setMessageText(e.target.value)}
                  placeholder="Nhập tin nhắn..."
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  onPressEnter={e => {
                    if (!e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
                <SendButton
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleSendMessage}
                  disabled={!messageText.trim() || sendingMessage}
                  loading={sendingMessage}
                />
              </InputContainer>
            </>
          ) : (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "100%",
                color: "#999",
                flexDirection: "column",
                gap: "16px",
              }}
            >
              <TeamOutlined style={{ fontSize: "64px", color: "#d9d9d9" }} />
              <Text style={{ fontSize: "18px", color: "#666" }}>
                Chọn một cuộc trò chuyện để bắt đầu
              </Text>
            </div>
          )}
        </ConversationPanel>
      </ChatWrapper>

      <CreateChatModal
        visible={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        onSuccess={handleCreateChatSuccess}
        currentUser={user}
        profile={profile}
      />
    </>
  );
}
