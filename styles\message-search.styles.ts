import { Input } from "antd";
import styled from "styled-components";

export const SearchContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .dark & {
    border-bottom-color: #303030;
    background: #1a1a1a;
  }
`;

export const SearchInput = styled(Input)`
  flex: 1;

  .ant-input {
    border-radius: 20px;
  }
`;

export const SearchStats = styled.div`
  font-size: 12px;
  color: #666;
  white-space: nowrap;
`;
