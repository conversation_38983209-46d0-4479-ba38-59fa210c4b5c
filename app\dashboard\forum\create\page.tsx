"use client";

import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Switch,
  message,
  Card,
  Space,
  Typography,
  Row,
  Col,
  Upload,
  Tag,
  Divider,
} from "antd";
import {
  SaveOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useForumService } from "@/hooks/useForumService";
import { useAuth } from "@/hooks/useAuth";
import type { CreateForumPostDto, ForumCategory } from "@/types/forum.types";

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function CreateForumPostPage() {
  const router = useRouter();
  const { createPost, categories, fetchCategories, loading } =
    useForumService();
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleSubmit = async (values: any) => {
    if (!user) {
      message.error("Bạn cần đăng nhập để tạo bài viết!");
      return;
    }

    setSubmitting(true);
    try {
      const postData: CreateForumPostDto = {
        title: values.title,
        content: values.content,
        excerpt: values.excerpt,
        category_id: values.category_id,
        author_id: user.id,
        tags: tags.length > 0 ? tags : undefined,
        status: values.status || "draft",
        image_urls: values.image_urls,
        video_urls: values.video_urls,
      };

      // Generate slug from title if not provided
      if (!values.slug && values.title) {
        postData.slug = generateSlug(values.title);
      } else {
        postData.slug = values.slug;
      }

      const newPost = await createPost(postData);
      if (newPost) {
        message.success("Tạo bài viết thành công!");
        router.push("/dashboard/forum");
      } else {
        message.error("Không thể tạo bài viết!");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi tạo bài viết!");
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, "")
      .replace(/[\s_-]+/g, "-")
      .replace(/^-+|-+$/g, "");
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    const slug = generateSlug(title);
    form.setFieldsValue({ slug });
  };

  return (
    <div style={{ padding: "24px" }}>
      <Card>
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: 24 }}
        >
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => router.push("/dashboard/forum")}
              >
                Quay lại
              </Button>
              <Title level={3} style={{ margin: 0 }}>
                Tạo bài viết mới
              </Title>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button onClick={() => form.resetFields()} disabled={submitting}>
                Đặt lại
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={submitting}
                onClick={() => form.submit()}
              >
                Tạo bài viết
              </Button>
            </Space>
          </Col>
        </Row>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: "draft",
            is_featured: false,
          }}
        >
          <Row gutter={24}>
            <Col span={16}>
              <Card title="Thông tin cơ bản" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="Tiêu đề"
                  name="title"
                  rules={[
                    { required: true, message: "Vui lòng nhập tiêu đề!" },
                    { min: 5, message: "Tiêu đề phải có ít nhất 5 ký tự!" },
                    {
                      max: 500,
                      message: "Tiêu đề không được vượt quá 500 ký tự!",
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập tiêu đề bài viết..."
                    onChange={handleTitleChange}
                    showCount
                    maxLength={500}
                  />
                </Form.Item>

                <Form.Item
                  label="Slug (URL)"
                  name="slug"
                  rules={[
                    { required: true, message: "Vui lòng nhập slug!" },
                    {
                      pattern: /^[a-z0-9-]+$/,
                      message:
                        "Slug chỉ được chứa chữ thường, số và dấu gạch ngang!",
                    },
                  ]}
                >
                  <Input placeholder="url-friendly-slug" />
                </Form.Item>

                <Form.Item
                  label="Mô tả ngắn (Excerpt)"
                  name="excerpt"
                  rules={[
                    {
                      max: 1000,
                      message: "Mô tả không được vượt quá 1000 ký tự!",
                    },
                  ]}
                >
                  <TextArea
                    placeholder="Mô tả ngắn gọn về bài viết..."
                    rows={3}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>

                <Form.Item
                  label="Nội dung"
                  name="content"
                  rules={[
                    { required: true, message: "Vui lòng nhập nội dung!" },
                    { min: 10, message: "Nội dung phải có ít nhất 10 ký tự!" },
                  ]}
                >
                  <TextArea
                    placeholder="Nhập nội dung bài viết..."
                    rows={12}
                    showCount
                    maxLength={50000}
                  />
                </Form.Item>
              </Card>

              <Card title="Hình ảnh và Video" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="URLs hình ảnh (mỗi URL một dòng)"
                  name="image_urls"
                >
                  <div>
                    <TextArea
                      placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"
                      rows={3}
                    />
                    <div
                      style={{ fontSize: "12px", color: "#666", marginTop: 4 }}
                    >
                      Các URL hình ảnh sẽ được lưu trữ tại
                      service.ngoaihangtv.live
                    </div>
                  </div>
                </Form.Item>

                <Form.Item
                  label="URLs video (mỗi URL một dòng)"
                  name="video_urls"
                >
                  <div>
                    <TextArea
                      placeholder="https://example.com/video1.mp4&#10;https://example.com/video2.mp4"
                      rows={2}
                    />
                    <div
                      style={{ fontSize: "12px", color: "#666", marginTop: 4 }}
                    >
                      Các URL video sẽ được lưu trữ tại service.ngoaihangtv.live
                    </div>
                  </div>
                </Form.Item>
              </Card>
            </Col>

            <Col span={8}>
              <Card title="Cài đặt" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="Danh mục"
                  name="category_id"
                  rules={[
                    { required: true, message: "Vui lòng chọn danh mục!" },
                  ]}
                >
                  <Select placeholder="Chọn danh mục">
                    {categories.map(category => (
                      <Option key={category.id} value={category.id}>
                        <Space>
                          <div
                            style={{
                              width: 12,
                              height: 12,
                              backgroundColor: category.color || "#1890ff",
                              borderRadius: "50%",
                            }}
                          />
                          {category.name}
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item label="Trạng thái" name="status">
                  <Select>
                    <Option value="draft">Bản nháp</Option>
                    <Option value="pending">Chờ duyệt</Option>
                    <Option value="approved">Đã duyệt</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  label="Bài viết nổi bật"
                  name="is_featured"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Card>

              <Card title="Tags" style={{ marginBottom: 16 }}>
                <Space style={{ marginBottom: 16 }}>
                  <Input
                    placeholder="Thêm tag..."
                    value={tagInput}
                    onChange={e => setTagInput(e.target.value)}
                    onPressEnter={e => {
                      e.preventDefault();
                      handleAddTag();
                    }}
                    style={{ width: 150 }}
                  />
                  <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={handleAddTag}
                  >
                    Thêm
                  </Button>
                </Space>

                <div style={{ minHeight: 32 }}>
                  {tags.map(tag => (
                    <Tag
                      key={tag}
                      closable
                      onClose={() => handleRemoveTag(tag)}
                      style={{ marginBottom: 4 }}
                    >
                      {tag}
                    </Tag>
                  ))}
                </div>
              </Card>

              <Card title="Thông tin bổ sung">
                <div style={{ color: "#666", fontSize: "12px" }}>
                  <p>• Bài viết sẽ được lưu tại cơ sở dữ liệu Supabase</p>
                  <p>
                    • Hình ảnh và video sẽ được tải lên service.ngoaihangtv.live
                  </p>
                  <p>• Bài viết cần được duyệt trước khi hiển thị công khai</p>
                  <p>• Có thể ghim bài viết lên đầu trang</p>
                </div>
              </Card>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
}
