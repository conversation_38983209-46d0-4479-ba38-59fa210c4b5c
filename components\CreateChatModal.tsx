"use client";

import { useSupabaseClient } from "@/contexts/SupabaseContext";
import { ChatService } from "@/services/chat.service";
import type { CreateChatData } from "@/types/chat.types";
import type { User } from "@supabase/supabase-js";
import {
  App,
  Button,
  Form,
  Input,
  Modal,
  Radio,
  Select,
} from "antd";
import { useState } from "react";

interface CreateChatModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  currentUser: User;
  profile: any;
}

interface CreateChatFormData {
  name: string;
  type: "direct" | "group" | "private";
  members?: string[];
}

export default function CreateChatModal({
  visible,
  onCancel,
  onSuccess,
  currentUser,
  profile,
}: CreateChatModalProps) {
  const { message } = App.useApp();
  const [form] = Form.useForm<CreateChatFormData>();
  const [loading, setLoading] = useState(false);
  const supabaseClient = useSupabaseClient();
  const chatService = new ChatService(supabaseClient);
  const [chatType, setChatType] = useState<"direct" | "group" | "private">("group");

  const handleSubmit = async (values: CreateChatFormData) => {
    setLoading(true);
    try {
      const chatData: CreateChatData = {
        name: values.name,
        type: values.type,
        members: values.members || [],
      };

      const response = await chatService.createChat(chatData, currentUser.id);
      
      if (response.error) {
        message.error(`Lỗi tạo chat: ${response.error.message}`);
        return;
      }

      message.success("Tạo chat thành công!");
      form.resetFields();
      onSuccess();
    } catch (error) {
      message.error("Có lỗi xảy ra khi tạo chat");
      console.error("Create chat error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setChatType("group");
    onCancel();
  };

  return (
    <Modal
      title="Tạo Chat Mới"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{ type: "group" }}
      >
        <Form.Item
          name="name"
          label="Tên Chat"
          rules={[
            { required: true, message: "Vui lòng nhập tên chat" },
            { min: 2, message: "Tên chat phải có ít nhất 2 ký tự" },
            { max: 50, message: "Tên chat không được quá 50 ký tự" },
          ]}
        >
          <Input placeholder="Nhập tên chat" />
        </Form.Item>

        <Form.Item
          name="type"
          label="Loại Chat"
          rules={[{ required: true, message: "Vui lòng chọn loại chat" }]}
        >
          <Radio.Group
            value={chatType}
            onChange={(e) => setChatType(e.target.value)}
          >
            <Radio value="group">Nhóm công khai</Radio>
            <Radio value="private">Nhóm riêng tư</Radio>
            <Radio value="direct">Chat trực tiếp</Radio>
          </Radio.Group>
        </Form.Item>

        {chatType !== "direct" && (
          <Form.Item
            name="members"
            label="Thành viên (tùy chọn)"
            help="Bạn có thể thêm thành viên sau khi tạo chat"
          >
            <Select
              mode="multiple"
              placeholder="Chọn thành viên"
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
              }
              options={[
                // Placeholder options - trong thực tế sẽ load từ API
                { value: "user1", label: "User 1" },
                { value: "user2", label: "User 2" },
              ]}
            />
          </Form.Item>
        )}

        <Form.Item className="mb-0">
          <div className="flex justify-end gap-2">
            <Button onClick={handleCancel}>
              Hủy
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
            >
              Tạo Chat
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
}