import { createClient } from "@/lib/supabase/client";
import type {
  Profile,
  UpdateProfileData,
  ProfileResponse,
  ProfilesResponse,
} from "@/types/profile.types";

class ProfileService {
  private supabase;

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }

  // Static method to create instance with default client for backward compatibility
  static createWithDefaultClient() {
    return new ProfileService(createClient());
  }

  async getProfile(userId: string): Promise<ProfileResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      return {
        profile: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async updateProfile(
    userId: string,
    updates: UpdateProfileData
  ): Promise<ProfileResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", userId)
        .select()
        .single();

      return {
        profile: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getAllProfiles(): Promise<ProfilesResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .select("*")
        .order("created_at", { ascending: false });

      return {
        profiles: data || [],
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profiles: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getProfilesByRole(role: string): Promise<ProfilesResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("role", role)
        .order("created_at", { ascending: false });

      return {
        profiles: data || [],
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profiles: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async searchProfiles(query: string): Promise<ProfilesResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .select("*")
        .or(`full_name.ilike.%${query}%,email.ilike.%${query}%`)
        .order("created_at", { ascending: false });

      return {
        profiles: data || [],
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profiles: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async deleteProfile(userId: string): Promise<{ error: Error | null }> {
    try {
      const { error } = await this.supabase
        .from("profiles")
        .delete()
        .eq("id", userId);

      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async uploadAvatar(
    userId: string,
    file: File
  ): Promise<{ url: string | null; error: Error | null }> {
    try {
      const fileExt = file.name.split(".").pop();
      const fileName = `${userId}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error: uploadError } = await this.supabase.storage
        .from("avatars")
        .upload(filePath, file);

      if (uploadError) {
        return {
          url: null,
          error: new Error(uploadError.message),
        };
      }

      const { data } = this.supabase.storage
        .from("avatars")
        .getPublicUrl(filePath);

      return {
        url: data.publicUrl,
        error: null,
      };
    } catch (error) {
      return {
        url: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }
}

export const profileService = ProfileService.createWithDefaultClient();
