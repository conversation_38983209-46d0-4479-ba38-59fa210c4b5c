"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { App, <PERSON>, Button, Spin, Typography, Space, Avatar } from "antd";
import { UserOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import { createClient } from "@/lib/supabase/client";
import type { User } from "@supabase/supabase-js";
import styled from "styled-components";

const { Title, Text } = Typography;

const JoinContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const JoinCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
`;

interface ChatRoom {
  id: string;
  name: string;
  type: string;
  created_by: string;
  created_at: string;
}

interface UserProfile {
  id: string;
  full_name: string;
  email: string;
  role: string;
  avatar_url?: string;
}

export default function JoinChatPage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [joining, setJoining] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [room, setRoom] = useState<ChatRoom | null>(null);
  const [creator, setCreator] = useState<UserProfile | null>(null);
  const [isMember, setIsMember] = useState(false);
  const supabase = createClient();
  const { message } = App.useApp();

  const roomId = params.roomId as string;

  useEffect(() => {
    checkUser();
  }, []);

  useEffect(() => {
    if (user && roomId) {
      loadRoomInfo();
    }
  }, [user, roomId]);

  const checkUser = async () => {
    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error || !user) {
        router.push("/auth/login");
        return;
      }

      setUser(user);
    } catch (error) {
      console.error("Error checking user:", error);
      router.push("/auth/login");
    }
  };

  const loadRoomInfo = async () => {
    try {
      // Load room info
      const { data: roomData, error: roomError } = await supabase
        .from("chat_rooms")
        .select("*")
        .eq("id", roomId)
        .single();

      if (roomError || !roomData) {
        message.error("Không tìm thấy chat room");
        router.push("/dashboard");
        return;
      }

      setRoom(roomData);

      // Load creator info
      const { data: creatorData } = await supabase
        .from("profiles")
        .select("id, full_name, email, role, avatar_url")
        .eq("id", roomData.created_by)
        .single();

      if (creatorData) {
        setCreator(creatorData);
      }

      // Check if user is already a member
      const { data: memberData } = await supabase
        .from("chat_room_members")
        .select("user_id")
        .eq("room_id", roomId)
        .eq("user_id", user?.id)
        .single();

      setIsMember(!!memberData);
    } catch (error) {
      console.error("Error loading room info:", error);
      message.error("Lỗi khi tải thông tin chat");
    } finally {
      setLoading(false);
    }
  };

  const handleJoinChat = async () => {
    if (!user || !room || !creator) return;

    setJoining(true);
    try {
      let directRoomId: string;

      if (room.type === "direct") {
        // For direct chat, redirect to the original room
        // The user should join the existing direct chat room
        directRoomId = roomId;

        // Add current user to the direct chat room
        const { error: memberError } = await supabase
          .from("chat_room_members")
          .insert({
            room_id: directRoomId,
            user_id: user.id,
            joined_at: new Date().toISOString(),
          });

        if (memberError) {
          // If user is already a member, that's fine
          if (memberError.code !== "23505") {
            // Not a duplicate key error
            message.error("Lỗi khi tham gia chat");
            return;
          }
        }

        message.success("Đã tham gia chat riêng thành công!");
      } else {
        // Logic for group chat: Join existing group
        directRoomId = roomId;

        const { error: memberError } = await supabase
          .from("chat_room_members")
          .insert({
            room_id: roomId,
            user_id: user.id,
            joined_at: new Date().toISOString(),
          });

        if (memberError) {
          // If user is already a member, that's fine
          if (memberError.code !== "23505") {
            // Not a duplicate key error
            message.error("Lỗi khi tham gia nhóm chat");
            return;
          }
        }

        message.success("Tham gia nhóm chat thành công!");
      }

      router.push("/dashboard");
    } catch (error) {
      console.error("Error joining chat:", error);
      message.error("Lỗi khi tham gia chat");
    } finally {
      setJoining(false);
    }
  };

  if (loading) {
    return (
      <JoinContainer>
        <JoinCard>
          <div style={{ textAlign: "center", padding: "40px" }}>
            <Spin size="large" />
            <div style={{ marginTop: "16px" }}>
              <Text>Đang tải thông tin chat...</Text>
            </div>
          </div>
        </JoinCard>
      </JoinContainer>
    );
  }

  if (!room) {
    return (
      <JoinContainer>
        <JoinCard>
          <div style={{ textAlign: "center", padding: "40px" }}>
            <Title level={3}>Không tìm thấy chat</Title>
            <Text type="secondary">Chat room không tồn tại hoặc đã bị xóa</Text>
            <div style={{ marginTop: "24px" }}>
              <Button type="primary" onClick={() => router.push("/dashboard")}>
                Về trang chủ
              </Button>
            </div>
          </div>
        </JoinCard>
      </JoinContainer>
    );
  }

  return (
    <JoinContainer>
      <JoinCard>
        <div style={{ textAlign: "center", padding: "20px" }}>
          <Title level={2}>Tham gia Chat</Title>

          <div style={{ marginTop: "24px" }}>
            <Title level={4}>
              {room.type === "direct" ? "Tạo Chat Riêng" : room.name}
            </Title>
            <Text type="secondary">
              {room.type === "direct"
                ? "Bạn sẽ tạo một chat riêng 1-1 với người tạo link"
                : "Bạn sẽ tham gia vào nhóm chat này"}
            </Text>
          </div>

          {creator && (
            <div style={{ marginTop: "24px" }}>
              <Text strong>Tạo bởi:</Text>
              <div style={{ marginTop: "8px" }}>
                <Space>
                  <Avatar
                    src={creator.avatar_url}
                    icon={<UserOutlined />}
                    size={40}
                  />
                  <div style={{ textAlign: "left" }}>
                    <div>{creator.full_name}</div>
                    <Text type="secondary" style={{ fontSize: "12px" }}>
                      {creator.role} • {creator.email}
                    </Text>
                  </div>
                </Space>
              </div>
            </div>
          )}

          <div style={{ marginTop: "32px" }}>
            {isMember ? (
              <div>
                <Text type="success">Bạn đã là thành viên của chat này</Text>
                <div style={{ marginTop: "16px" }}>
                  <Button
                    type="primary"
                    size="large"
                    onClick={() => router.push("/dashboard")}
                  >
                    Vào Chat
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <Text>
                  {room.type === "direct"
                    ? `Bạn có muốn tạo chat riêng với ${creator?.full_name} không?`
                    : `Bạn có muốn tham gia nhóm "${room.name}" không?`}
                </Text>
                <div style={{ marginTop: "16px" }}>
                  <Space>
                    <Button
                      onClick={() => router.push("/dashboard")}
                      icon={<ArrowLeftOutlined />}
                    >
                      Quay lại
                    </Button>
                    <Button
                      type="primary"
                      size="large"
                      loading={joining}
                      onClick={handleJoinChat}
                    >
                      {room.type === "direct"
                        ? "Tạo Chat Riêng"
                        : "Tham gia Nhóm"}
                    </Button>
                  </Space>
                </div>
              </div>
            )}
          </div>
        </div>
      </JoinCard>
    </JoinContainer>
  );
}
