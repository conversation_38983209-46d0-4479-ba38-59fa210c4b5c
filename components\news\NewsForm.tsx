'use client';

import React, { useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Space,
  Switch,
  Card,
  Row,
  Col,
  Divider,
} from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import { PostWithSeo, CreatePostRequest, UpdatePostRequest } from '@/types/news.types';

const { TextArea } = Input;

interface NewsFormProps {
  post?: PostWithSeo;
  loading?: boolean;
  onSubmit: (values: any) => Promise<void>;
  onCancel: () => void;
  showPublishToggle?: boolean;
}

export const NewsForm: React.FC<NewsFormProps> = ({
  post,
  loading = false,
  onSubmit,
  onCancel,
  showPublishToggle = true,
}) => {
  const [form] = Form.useForm();
  const isEditing = !!post;

  useEffect(() => {
    if (post) {
      form.setFieldsValue({
        title: post.title,
        description: post.description || '',
        content: post.content,
        thumbnail: post.thumbnail || '',
        slug: post.slug || '',
    meta_title: post.meta_title || '',
    meta_description: post.meta_description || '',
    meta_keywords: post.meta_keywords || '',
        publishNow: post.published_at !== null,
      });
    }
  }, [post, form]);

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    if (!isEditing) {
      const slug = generateSlug(title);
      form.setFieldsValue({ slug, meta_title: title });
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onSubmit}
      initialValues={{
        publishNow: false,
      }}
    >
      <Row gutter={24}>
        <Col span={16}>
          <Card title="Nội dung bài viết" style={{ marginBottom: '24px' }}>
            <Form.Item
              name="title"
              label="Tiêu đề"
              rules={[
                { required: true, message: 'Vui lòng nhập tiêu đề' },
                { min: 5, message: 'Tiêu đề phải có ít nhất 5 ký tự' },
                { max: 200, message: 'Tiêu đề không được quá 200 ký tự' },
              ]}
            >
              <Input
                placeholder="Nhập tiêu đề bài viết"
                onChange={handleTitleChange}
              />
            </Form.Item>

            <Form.Item
              name="description"
              label="Mô tả ngắn"
              rules={[
                { max: 500, message: 'Mô tả không được quá 500 ký tự' },
              ]}
            >
              <TextArea
                rows={3}
                placeholder="Nhập mô tả ngắn cho bài viết (tối đa 500 ký tự)"
                showCount
                maxLength={500}
              />
            </Form.Item>

            <Form.Item
              name="thumbnail"
              label="Ảnh đại diện"
              rules={[
                { type: 'url', message: 'Vui lòng nhập URL hợp lệ' },
              ]}
            >
              <Input
                placeholder="Nhập URL ảnh đại diện (ví dụ: https://example.com/image.jpg)"
              />
            </Form.Item>

            <Form.Item
              name="content"
              label="Nội dung"
              rules={[
                { required: true, message: 'Vui lòng nhập nội dung' },
                { min: 50, message: 'Nội dung phải có ít nhất 50 ký tự' },
              ]}
            >
              <TextArea
                rows={12}
                placeholder="Nhập nội dung bài viết (hỗ trợ Markdown)"
              />
            </Form.Item>
          </Card>
        </Col>

        <Col span={8}>
          {showPublishToggle && (
            <Card title="Cài đặt xuất bản" style={{ marginBottom: '24px' }}>
              <Form.Item
                name="publishNow"
                valuePropName="checked"
                label="Trạng thái"
              >
                <Switch
                  checkedChildren="Xuất bản ngay"
                  unCheckedChildren="Lưu nháp"
                />
              </Form.Item>
              
              {isEditing && post?.published_at && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
                  Xuất bản lúc: {new Date(post.published_at).toLocaleString('vi-VN')}
                </div>
              )}
            </Card>
          )}

          <Card title="SEO Settings">
            <Form.Item
              name="slug"
              label="Slug (URL)"
              rules={[
                { pattern: /^[a-z0-9-]*$/, message: 'Slug chỉ chứa chữ thường, số và dấu gạch ngang' },
                { max: 100, message: 'Slug không được quá 100 ký tự' },
              ]}
            >
              <Input 
                placeholder="auto-generated-from-title" 
                addonBefore="/news/"
              />
            </Form.Item>

            <Form.Item
              name="meta_title"
              label="Meta Title"
              rules={[
                { max: 60, message: 'Meta title không được quá 60 ký tự' },
              ]}
            >
              <Input placeholder="Tiêu đề SEO" />
            </Form.Item>

            <Form.Item
              name="meta_description"
              label="Meta Description"
              rules={[
                { max: 160, message: 'Meta description không được quá 160 ký tự' },
              ]}
            >
              <TextArea
                rows={3}
                placeholder="Mô tả ngắn cho SEO (tối đa 160 ký tự)"
                showCount
                maxLength={160}
              />
            </Form.Item>

            <Form.Item
              name="meta_keywords"
              label="Meta Keywords"
              rules={[
                { max: 200, message: 'Meta keywords không được quá 200 ký tự' },
              ]}
            >
              <Input placeholder="keyword1, keyword2, keyword3" />
            </Form.Item>
          </Card>
        </Col>
      </Row>

      <Divider />

      <div style={{ textAlign: 'right' }}>
        <Space>
          <Button onClick={onCancel}>
            Hủy
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<SaveOutlined />}
          >
            {isEditing ? 'Cập nhật' : 'Tạo bài viết'}
          </Button>
        </Space>
      </div>
    </Form>
  );
};

export default NewsForm;