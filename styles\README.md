# Styles Directory

Th<PERSON> mục này chứa tất cả các styled components đã được tách ra từ các file component chính để giảm độ dài code và tổ chức tốt hơn.

## Cấu trúc

### `dashboard.styles.ts`

Chứa styled components cho trang dashboard:

- `DashboardContainer`
- `StyledHeader`
- `StyledSider`
- `MenuContainer`
- `BettingContainer`
- `BettingCard`
- `ChatContainer`
- `ChatList`
- `ChatItem`
- `StyledContent`
- `WelcomeContainer`
- `ShareSection`

### `chat.styles.ts`

Chứa styled components cho trang chat công khai:

- `ChatContainer`
- `ChatCard`
- `ChatHeader`
- `MessagesArea`
- `MessageBubble`
- `MessageContent`
- `InputArea`
- `NameInputArea`

### `auth.styles.ts`

Chứa styled components cho các trang authentication:

- `AuthContainer`
- `AuthCard`
- `RegisterCard`
- `StyledForm`
- `AuthButton`
- `GoogleButton`

### `chat-interface.styles.ts`

Chứa styled components cho chat interface trong dashboard:

- `ChatContainer`
- `ChatHeader`
- `MessagesContainer`
- `MessageBubble`
- `MessageContent`
- `MessageTime`
- `InputContainer`
- `MessageInput`
- `FileUploadContainer`
- `HighlightedText`

### `main-chat.styles.ts`

Chứa styled components cho main chat interface:

- `ChatContainer`
- `ChatHeader`
- `MessagesContainer`
- `MessageItem`
- `InputContainer`

## Cách sử dụng

```typescript
// Import từ file cụ thể
import { DashboardContainer, StyledHeader } from "@/styles/dashboard.styles";

// Hoặc import từ index file
import { DashboardContainer, StyledHeader } from "@/styles";
```

## Lợi ích

1. **Giảm độ dài file**: Mỗi file component giờ đây có ít hơn 300 dòng code
2. **Tổ chức tốt hơn**: Styled components được nhóm theo chức năng
3. **Tái sử dụng**: Có thể dễ dàng import và sử dụng lại styled components
4. **Bảo trì dễ dàng**: Dễ dàng tìm và chỉnh sửa styles
5. **Tách biệt concerns**: Logic component và styling được tách riêng
