'use client';

import React, { useState, useEffect } from 'react';
import { useParams, notFound } from 'next/navigation';
import { Card, Typography, Tag, Spin, Image, Divider } from 'antd';
import { CalendarOutlined, UserOutlined, EyeOutlined } from '@ant-design/icons';
import { useNewsService } from '@/hooks/useNewsService';
import { PostWithSeo } from '@/types/news.types';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;

export default function NewsPublicPage() {
  const params = useParams();
  const newsService = useNewsService();
  const [loading, setLoading] = useState(true);
  const [post, setPost] = useState<PostWithSeo | null>(null);

  const slug = params.slug as string;

  useEffect(() => {
    if (slug) {
      fetchPost();
    }
  }, [slug]);

  const fetchPost = async () => {
    try {
      setLoading(true);
      const postData = await newsService.fetchPostBySlug(slug);
      if (postData && postData.published_at) {
        setPost(postData);
      } else {
        notFound();
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      notFound();
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{ textAlign: 'center' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', color: '#666' }}>Đang tải bài viết...</div>
        </div>
      </div>
    );
  }

  if (!post) {
    notFound();
  }

  return (
    <>
      {/* SEO Meta Tags */}
      <head>
        <title>{post.meta_title || post.title}</title>
        <meta name="description" content={post.meta_description || post.description || ''} />
        <meta name="keywords" content={post.meta_keywords || ''} />
        <meta property="og:title" content={post.meta_title || post.title} />
        <meta property="og:description" content={post.meta_description || post.description || ''} />
        <meta property="og:image" content={post.thumbnail || ''} />
        <meta property="og:type" content="article" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={post.meta_title || post.title} />
        <meta name="twitter:description" content={post.meta_description || post.description || ''} />
        <meta name="twitter:image" content={post.thumbnail || ''} />
      </head>

      <div style={{ 
        minHeight: '100vh', 
        backgroundColor: '#f5f5f5',
        padding: '24px 0'
      }}>
        <div style={{ 
          maxWidth: '800px', 
          margin: '0 auto',
          padding: '0 24px'
        }}>
          <Card style={{ marginBottom: '24px' }}>
            {/* Article Header */}
            <div style={{ marginBottom: '24px' }}>
              <Tag color="blue" style={{ marginBottom: '16px' }}>
                <EyeOutlined style={{ marginRight: '4px' }} />
                Tin tức
              </Tag>
              
              <Title level={1} style={{ 
                marginBottom: '16px',
                fontSize: '32px',
                lineHeight: '1.3',
                color: '#262626'
              }}>
                {post.title}
              </Title>

              {post.description && (
                <Paragraph style={{ 
                  fontSize: '18px', 
                  color: '#666',
                  marginBottom: '24px',
                  fontStyle: 'italic'
                }}>
                  {post.description}
                </Paragraph>
              )}

              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '24px',
                paddingBottom: '16px',
                borderBottom: '1px solid #f0f0f0',
                color: '#666'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <UserOutlined />
                  <Text style={{ color: '#666' }}>
                    {post.author?.full_name || 'Tác giả'}
                  </Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <CalendarOutlined />
                  <Text style={{ color: '#666' }}>
                    {dayjs(post.published_at).format('DD/MM/YYYY HH:mm')}
                  </Text>
                </div>
              </div>
            </div>

            {/* Featured Image */}
            {post.thumbnail && (
              <div style={{ 
                marginBottom: '32px',
                textAlign: 'center'
              }}>
                <Image
                  src={post.thumbnail}
                  alt={post.title}
                  style={{ 
                    maxWidth: '100%', 
                    maxHeight: '500px',
                    borderRadius: '8px'
                  }}
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
              </div>
            )}

            {/* Article Content */}
            <article 
              className="news-content"
              style={{ 
                fontSize: '16px', 
                lineHeight: '1.8',
                color: '#333'
              }}
              dangerouslySetInnerHTML={{ __html: post.content }}
            />

            {/* Article Footer */}
            <Divider style={{ margin: '32px 0' }} />
            
            <div style={{ 
              textAlign: 'center',
              color: '#666',
              fontSize: '14px'
            }}>
              <Text>Bài viết được xuất bản vào {dayjs(post.published_at).format('DD/MM/YYYY lúc HH:mm')}</Text>
            </div>
          </Card>
        </div>
      </div>

      {/* CSS Styles for Content */}
      <style jsx>{`
        .news-content :global(h1) {
          font-size: 28px;
          font-weight: bold;
          margin: 32px 0 16px 0;
          color: #262626;
          line-height: 1.3;
        }
        .news-content :global(h2) {
          font-size: 24px;
          font-weight: bold;
          margin: 28px 0 14px 0;
          color: #262626;
          line-height: 1.3;
        }
        .news-content :global(h3) {
          font-size: 20px;
          font-weight: bold;
          margin: 24px 0 12px 0;
          color: #262626;
          line-height: 1.4;
        }
        .news-content :global(h4),
        .news-content :global(h5),
        .news-content :global(h6) {
          font-size: 18px;
          font-weight: bold;
          margin: 20px 0 10px 0;
          color: #262626;
          line-height: 1.4;
        }
        .news-content :global(p) {
          margin: 16px 0;
          line-height: 1.8;
          text-align: justify;
        }
        .news-content :global(ul),
        .news-content :global(ol) {
          margin: 16px 0;
          padding-left: 28px;
        }
        .news-content :global(li) {
          margin: 8px 0;
          line-height: 1.7;
        }
        .news-content :global(blockquote) {
          margin: 24px 0;
          padding: 16px 20px;
          border-left: 4px solid #1890ff;
          background-color: #f6f8fa;
          font-style: italic;
          border-radius: 0 6px 6px 0;
        }
        .news-content :global(a) {
          color: #1890ff;
          text-decoration: none;
          border-bottom: 1px solid transparent;
          transition: all 0.3s ease;
        }
        .news-content :global(a:hover) {
          border-bottom-color: #1890ff;
        }
        .news-content :global(img) {
          max-width: 100%;
          height: auto;
          margin: 20px 0;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .news-content :global(code) {
          background-color: #f5f5f5;
          padding: 3px 8px;
          border-radius: 4px;
          font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
          font-size: 14px;
          color: #d63384;
        }
        .news-content :global(pre) {
          background-color: #f8f9fa;
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 20px 0;
          border: 1px solid #e9ecef;
        }
        .news-content :global(pre code) {
          background: none;
          padding: 0;
          color: #333;
        }
        .news-content :global(table) {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          border-radius: 6px;
          overflow: hidden;
        }
        .news-content :global(th),
        .news-content :global(td) {
          border: 1px solid #e9ecef;
          padding: 12px 16px;
          text-align: left;
        }
        .news-content :global(th) {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #495057;
        }
        .news-content :global(tr:nth-child(even)) {
          background-color: #f8f9fa;
        }
        .news-content :global(strong) {
          font-weight: 600;
          color: #262626;
        }
        .news-content :global(em) {
          font-style: italic;
          color: #666;
        }
        .news-content :global(hr) {
          margin: 32px 0;
          border: none;
          border-top: 1px solid #e9ecef;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
          .news-content :global(h1) {
            font-size: 24px;
          }
          .news-content :global(h2) {
            font-size: 20px;
          }
          .news-content :global(h3) {
            font-size: 18px;
          }
          .news-content :global(table) {
            font-size: 14px;
          }
          .news-content :global(th),
          .news-content :global(td) {
            padding: 8px 12px;
          }
        }
      `}</style>
    </>
  );
}