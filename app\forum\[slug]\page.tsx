"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Space,
  Tag,
  Avatar,
  Button,
  Divider,
  Spin,
  Empty,
  Row,
  Col,
  Badge,
} from "antd";
import {
  PushpinOutlined,
  EyeOutlined,
  CommentOutlined,
  LikeOutlined,
  LikeFilled,
  EditOutlined,
  DeleteOutlined,
  Arrow<PERSON>eftOutlined,
  CalendarOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { useRouter, useParams } from "next/navigation";
import { useForumService } from "@/hooks/useForumService";
import ForumCommentList from "@/components/forum/ForumCommentList";
import type { ForumPost } from "@/types/forum.types";
import dayjs from "dayjs";

const { Title, Text, Paragraph } = Typography;

export default function ForumPostDetailPage() {
  const router = useRouter();
  const params = useParams();
  const slug = params.slug as string;

  const { getPostBySlug, togglePostLike, loading, error } = useForumService();

  const [post, setPost] = useState<ForumPost | null>(null);
  const [liked, setLiked] = useState(false);
  const [currentUserId] = useState("current-user-id"); // This should come from auth context

  useEffect(() => {
    if (slug) {
      loadPost();
    }
  }, [slug]);

  const loadPost = async () => {
    try {
      const postData = await getPostBySlug(slug);
      if (postData) {
        setPost(postData);
      } else {
        console.error("Post not found");
      }
    } catch (error) {
      console.error("Error loading post:", error);
    }
  };

  const handleLike = async () => {
    if (!post) return;

    try {
      const success = await togglePostLike(post.id, currentUserId);
      if (success) {
        setLiked(!liked);
        // Reload post to update like count
        loadPost();
      }
    } catch (error) {
      console.error("Error toggling like:", error);
    }
  };

  const handleEdit = () => {
    if (post) {
      router.push(`/forum/edit/${post.id}`);
    }
  };

  const handleDelete = async () => {
    // This would need a delete function in the service
    console.log("Delete post:", post?.id);
  };

  if (loading) {
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <Spin size="large" />
        <div style={{ marginTop: "16px" }}>
          <Text type="secondary">Đang tải bài viết...</Text>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <Empty description="Không tìm thấy bài viết" />
        <Button
          style={{ marginTop: "16px" }}
          onClick={() => router.push("/forum")}
        >
          Quay lại diễn đàn
        </Button>
      </div>
    );
  }

  return (
    <div style={{ padding: "24px", maxWidth: "1000px", margin: "0 auto" }}>
      {/* Back button */}
      <Button
        icon={<ArrowLeftOutlined />}
        onClick={() => router.push("/forum")}
        style={{ marginBottom: "24px" }}
      >
        Quay lại diễn đàn
      </Button>

      <Row gutter={[24, 24]}>
        {/* Main content */}
        <Col xs={24} lg={16}>
          <Card>
            {/* Post header */}
            <div style={{ marginBottom: "24px" }}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  marginBottom: "16px",
                }}
              >
                <Avatar src={post.author?.avatar_url} size="large">
                  {post.author?.full_name?.[0]?.toUpperCase() || "U"}
                </Avatar>
                <div style={{ flex: 1 }}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                    }}
                  >
                    <Text strong style={{ fontSize: "16px" }}>
                      {post.author?.full_name || "Người dùng"}
                    </Text>
                    {post.is_pinned && (
                      <Badge
                        count="Ghim"
                        style={{ backgroundColor: "#1890ff" }}
                      />
                    )}
                  </div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "12px",
                      marginTop: "4px",
                    }}
                  >
                    <Space size="small">
                      <CalendarOutlined style={{ color: "#666" }} />
                      <Text type="secondary" style={{ fontSize: "12px" }}>
                        {dayjs(post.created_at).format("DD/MM/YYYY HH:mm")}
                      </Text>
                    </Space>
                    <Space size="small">
                      <EyeOutlined style={{ color: "#666" }} />
                      <Text type="secondary" style={{ fontSize: "12px" }}>
                        {post.view_count} lượt xem
                      </Text>
                    </Space>
                  </div>
                </div>
                <Space>
                  <Button
                    type="text"
                    icon={liked ? <LikeFilled /> : <LikeOutlined />}
                    onClick={handleLike}
                  >
                    {post.like_count}
                  </Button>
                  {post.author_id === currentUserId && (
                    <>
                      <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={handleEdit}
                      >
                        Sửa
                      </Button>
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={handleDelete}
                      >
                        Xóa
                      </Button>
                    </>
                  )}
                </Space>
              </div>

              {/* Category */}
              {post.category && (
                <div style={{ marginBottom: "16px" }}>
                  <Tag
                    color={post.category.color}
                    style={{ fontSize: "14px", padding: "4px 12px" }}
                  >
                    {post.category.icon && (
                      <span style={{ marginRight: 8 }}>
                        {post.category.icon}
                      </span>
                    )}
                    {post.category.name}
                  </Tag>
                </div>
              )}

              {/* Title */}
              <Title level={2} style={{ marginBottom: "16px" }}>
                {post.title}
              </Title>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div style={{ marginBottom: "16px" }}>
                  <Space size={[0, 8]} wrap>
                    {post.tags.map(tag => (
                      <Tag key={tag} color="blue">
                        #{tag}
                      </Tag>
                    ))}
                  </Space>
                </div>
              )}
            </div>

            <Divider />

            {/* Post content */}
            <div style={{ marginBottom: "32px" }}>
              <div
                style={{
                  fontSize: "16px",
                  lineHeight: 1.8,
                  whiteSpace: "pre-wrap",
                  color: "#333",
                }}
              >
                {post.content}
              </div>
            </div>

            {/* Images */}
            {post.image_urls && post.image_urls.length > 0 && (
              <div style={{ marginBottom: "24px" }}>
                <Row gutter={[8, 8]}>
                  {post.image_urls.map((imageUrl, index) => (
                    <Col xs={24} sm={12} key={index}>
                      <img
                        src={imageUrl}
                        alt={`Post image ${index + 1}`}
                        style={{
                          width: "100%",
                          height: "200px",
                          objectFit: "cover",
                          borderRadius: "8px",
                        }}
                      />
                    </Col>
                  ))}
                </Row>
              </div>
            )}

            {/* Videos */}
            {post.video_urls && post.video_urls.length > 0 && (
              <div style={{ marginBottom: "24px" }}>
                {post.video_urls.map((videoUrl, index) => (
                  <video
                    key={index}
                    controls
                    style={{
                      width: "100%",
                      maxHeight: "400px",
                      borderRadius: "8px",
                      marginBottom: "8px",
                    }}
                  >
                    <source src={videoUrl} />
                    Trình duyệt của bạn không hỗ trợ video.
                  </video>
                ))}
              </div>
            )}

            {/* Post stats */}
            <div
              style={{
                padding: "16px",
                backgroundColor: "#f5f5f5",
                borderRadius: "8px",
              }}
            >
              <Row justify="space-around">
                <Col>
                  <Space>
                    <EyeOutlined />
                    <Text strong>{post.view_count} lượt xem</Text>
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <LikeOutlined />
                    <Text strong>{post.like_count} lượt thích</Text>
                  </Space>
                </Col>
                <Col>
                  <Space>
                    <CommentOutlined />
                    <Text strong>{post.comment_count} bình luận</Text>
                  </Space>
                </Col>
              </Row>
            </div>
          </Card>
        </Col>

        {/* Sidebar */}
        <Col xs={24} lg={8}>
          <Card title="Thông tin bài viết" style={{ marginBottom: "16px" }}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Text strong>Tác giả: </Text>
                <Text>{post.author?.full_name || "Không xác định"}</Text>
              </div>
              <div>
                <Text strong>Ngày đăng: </Text>
                <Text>{dayjs(post.created_at).format("DD/MM/YYYY")}</Text>
              </div>
              <div>
                <Text strong>Cập nhật: </Text>
                <Text>{dayjs(post.updated_at).format("DD/MM/YYYY")}</Text>
              </div>
              <div>
                <Text strong>Trạng thái: </Text>
                <Tag
                  color={
                    post.status === "approved"
                      ? "green"
                      : post.status === "pending"
                        ? "orange"
                        : post.status === "rejected"
                          ? "red"
                          : "blue"
                  }
                >
                  {post.status === "approved"
                    ? "Đã duyệt"
                    : post.status === "pending"
                      ? "Chờ duyệt"
                      : post.status === "rejected"
                        ? "Từ chối"
                        : "Bản nháp"}
                </Tag>
              </div>
            </Space>
          </Card>

          <Card title="Danh mục liên quan">
            <Space wrap>
              <Tag color="blue">Thảo luận chung</Tag>
              <Tag color="green">Tin tức</Tag>
              <Tag color="orange">Kinh nghiệm</Tag>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Comments section */}
      <div style={{ marginTop: "32px" }}>
        <ForumCommentList
          postId={post.id}
          currentUserId={currentUserId}
          showCommentForm={true}
        />
      </div>
    </div>
  );
}
