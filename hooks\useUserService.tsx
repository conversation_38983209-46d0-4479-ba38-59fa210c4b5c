import { useState, useCallback, useEffect } from 'react';
import { UserService } from '@/services/userService';
import { Profile } from '@/types/profile.types';
import { useSupabaseClient } from '@/contexts/SupabaseContext';

interface UseUserServiceReturn {
  users: Profile[];
  loading: boolean;
  error: string | null;
  totalUsers: number;
  currentPage: number;
  totalPages: number;
  stats: {
    totalUsers: number;
    bettingUsers: number;
    adminUsers: number;
  } | null;
  
  // Actions
  updateBettingPermission: (userId: string, canAccess: boolean) => Promise<boolean>;
  updateMultipleBettingPermissions: (userIds: string[], canAccess: boolean) => Promise<boolean>;
  getUserById: (userId: string) => Promise<Profile | null>;
  fetchUsers: (params?: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    can_access_betting?: boolean;
  }) => Promise<void>;
  fetchUserStats: () => Promise<void>;
  refreshUsers: () => Promise<void>;
  setFilters: (filters: {
    search?: string;
    role?: string;
    can_access_betting?: boolean;
  }) => void;
  resetFilters: () => void;
  setPage: (page: number) => void;
}

interface UseUserServiceProps {
  limit?: number;
}

export const useUserService = (props: UseUserServiceProps = {}): UseUserServiceReturn => {
  const { limit = 10 } = props;
  const supabase = useSupabaseClient();
  const userService = new UserService(supabase);
  
  const [users, setUsers] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalUsers, setTotalUsers] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [filters, setFiltersState] = useState<{
    search?: string;
    role?: string;
    can_access_betting?: boolean;
  }>({});
  const [stats, setStats] = useState<{
    totalUsers: number;
    bettingUsers: number;
    adminUsers: number;
  } | null>(null);

  const fetchUsers = useCallback(async (params: {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
    can_access_betting?: boolean;
  } = {}) => {
    try {
      setLoading(true);
      setError(null);
      
      const finalParams = {
        page: currentPage,
        limit,
        ...filters,
        ...params,
      };
      
      const response = await userService.getUsers(finalParams);
      
      setUsers(response.users);
      setTotalUsers(response.total);
      setCurrentPage(response.page);
      setTotalPages(Math.ceil(response.total / limit));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải danh sách users';
      setError(errorMessage);
      setUsers([]);
      setTotalUsers(0);
    } finally {
      setLoading(false);
    }
  }, [userService, currentPage, limit, filters]);

  const fetchUserStats = useCallback(async () => {
    try {
      const statsData = await userService.getUserStats();
      setStats({
        totalUsers: statsData.totalUsers,
        bettingUsers: statsData.bettingUsers,
        adminUsers: statsData.adminUsers,
      });
    } catch (err) {
      console.error('Error fetching user stats:', err);
      // Don't set error for stats as it's not critical
    }
  }, [userService]);

  const refreshUsers = useCallback(async () => {
    await Promise.all([
      fetchUsers(),
      fetchUserStats()
    ]);
  }, [fetchUsers, fetchUserStats]);

  const updateBettingPermission = useCallback(async (userId: string, canAccess: boolean): Promise<boolean> => {
    try {
      setLoading(true);
      const success = await userService.updateBettingPermission(userId, canAccess);
      if (success) {
        await refreshUsers();
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi cập nhật quyền soi kèo';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [userService, refreshUsers]);



  const updateMultipleBettingPermissions = useCallback(async (userIds: string[], canAccess: boolean): Promise<boolean> => {
    try {
      setLoading(true);
      const success = await userService.updateMultipleBettingPermissions(userIds, canAccess);
      if (success) {
        await refreshUsers();
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi cập nhật quyền soi kèo hàng loạt';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [userService, refreshUsers]);

  const getUserById = useCallback(async (userId: string): Promise<Profile | null> => {
    try {
      const user = await userService.getUserById(userId);
      return user;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải thông tin user';
      setError(errorMessage);
      return null;
    }
  }, [userService]);

  const setFilters = useCallback((newFilters: {
    search?: string;
    role?: string;
    can_access_betting?: boolean;
  }) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState({});
    setCurrentPage(1);
  }, []);

  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Initial load
  useEffect(() => {
    fetchUsers();
    fetchUserStats();
  }, []);

  // Refetch when filters or page change
  useEffect(() => {
    fetchUsers();
  }, [filters, currentPage]);

  return {
    users,
    loading,
    error,
    totalUsers,
    currentPage,
    totalPages,
    stats,
    updateBettingPermission,
    updateMultipleBettingPermissions,
    getUserById,
    fetchUsers,
    fetchUserStats,
    refreshUsers,
    setFilters,
    resetFilters,
    setPage,
  };
};