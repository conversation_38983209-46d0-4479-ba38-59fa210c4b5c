"use client";

import { useAuthService } from "@/hooks";
import ChatContainer from "@/components/ChatContainer";
import { Spin } from "antd";

export default function ChatPage() {
    const { user, profile, loading: authLoading } = useAuthService();

    if (authLoading) {
        return (
            <div
                style={{
                    height: "100vh",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <Spin size="large" />
            </div>
        );
    }

    if (!user || !profile) {
        return null;
    }

    return (
        <ChatContainer
            user={user}
            profile={profile}
        />
    );
}
