import { createClient } from '../supabase/client';
import type {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryFilters,
  CategoryWithPostCount,
} from '../../types';

// Create supabase client instance
const supabase = createClient();

// Fetch all categories with optional filters
export async function fetchCategories(filters?: CategoryFilters) {
  try {
    let query = supabase
      .from('categories')
      .select('*')
      .order('sort_order', { ascending: true })
      .order('name', { ascending: true });

    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }

    return data as Category[];
  } catch (error) {
    console.error('Error in fetchCategories:', error);
    throw error;
  }
}

// Fetch categories with post count
export async function fetchCategoriesWithPostCount(filters?: CategoryFilters) {
  try {
    let query = supabase
      .from('categories')
      .select(`
        *,
        post_categories(count)
      `)
      .order('sort_order', { ascending: true })
      .order('name', { ascending: true });

    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active);
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching categories with post count:', error);
      throw error;
    }

    // Transform data to include post_count
    const categoriesWithCount = data?.map((category: any) => ({
      ...category,
      post_count: category.post_categories?.[0]?.count || 0
    })) as CategoryWithPostCount[];

    return categoriesWithCount;
  } catch (error) {
    console.error('Error in fetchCategoriesWithPostCount:', error);
    throw error;
  }
}

// Fetch single category by ID
export async function fetchCategoryById(id: string) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      throw error;
    }

    return data as Category;
  } catch (error) {
    console.error('Error in fetchCategoryById:', error);
    throw error;
  }
}

// Create new category
export async function createCategory(categoryData: CreateCategoryRequest) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .insert([
        {
          ...categoryData,
          is_active: categoryData.is_active ?? true,
          sort_order: categoryData.sort_order ?? 0,
        }
      ])
      .select()
      .single();

    if (error) {
      console.error('Error creating category:', error);
      throw error;
    }

    return data as Category;
  } catch (error) {
    console.error('Error in createCategory:', error);
    throw error;
  }
}

// Update category
export async function updateCategory(id: string, categoryData: UpdateCategoryRequest) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .update(categoryData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating category:', error);
      throw error;
    }

    return data as Category;
  } catch (error) {
    console.error('Error in updateCategory:', error);
    throw error;
  }
}

// Delete category
export async function deleteCategory(id: string) {
  try {
    // First check if category has any posts
    const { data: postCategories, error: checkError } = await supabase
      .from('post_categories')
      .select('id')
      .eq('category_id', id)
      .limit(1);

    if (checkError) {
      console.error('Error checking category usage:', checkError);
      throw checkError;
    }

    if (postCategories && postCategories.length > 0) {
      throw new Error('Cannot delete category that is being used by posts');
    }

    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting category:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteCategory:', error);
    throw error;
  }
}

// Toggle category active status
export async function toggleCategoryStatus(id: string, isActive: boolean) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .update({ is_active: isActive })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error toggling category status:', error);
      throw error;
    }

    return data as Category;
  } catch (error) {
    console.error('Error in toggleCategoryStatus:', error);
    throw error;
  }
}

// Update category sort order
export async function updateCategorySortOrder(id: string, sortOrder: number) {
  try {
    const { data, error } = await supabase
      .from('categories')
      .update({ sort_order: sortOrder })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating category sort order:', error);
      throw error;
    }

    return data as Category;
  } catch (error) {
    console.error('Error in updateCategorySortOrder:', error);
    throw error;
  }
}

// Check if slug is available
export async function checkSlugAvailability(slug: string, excludeId?: string) {
  try {
    let query = supabase
      .from('categories')
      .select('id')
      .eq('slug', slug);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error checking slug availability:', error);
      throw error;
    }

    return data.length === 0;
  } catch (error) {
    console.error('Error in checkSlugAvailability:', error);
    throw error;
  }
}