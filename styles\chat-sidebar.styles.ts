import { Button, Layout } from "antd";
import { styled } from "styled-components";

export const SidebarContainer = styled(Layout.Sider)`
  background-color: #fff;
  border-right: 1px solid #e8e8e8;

  .ant-layout-sider-trigger {
    background-color: #f0f0f0;
    color: #1890ff;
    border-top: 1px solid #e8e8e8;
  }

  .ant-layout-sider-trigger:hover {
    background-color: #e6f7ff;
  }
`;

export const SearchContainer = styled.div`
  padding: 16px;
`;

export const ChatListContainer = styled.div`
  padding: 16px;
`;

export const ChatItem = styled.div<{ $active: boolean }>`
  padding: 8px 16px;
  cursor: pointer;
  background-color: ${({ $active }) => ($active ? "#f0f0f0" : "transparent")};
  border-radius: 4px;
  margin-bottom: 8px;
`;

export const ButtonStyled = styled(Button)`
  fontSize: "16px",
  width: "100%",
  height: "100%",
  border: "none",
  boxShadow: "none",
`;
