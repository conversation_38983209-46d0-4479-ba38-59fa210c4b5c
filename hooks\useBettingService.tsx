"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '@/contexts/SupabaseContext';
import { BettingService } from '@/services/betting.service';
import { BettingOdds, CreateBettingOddsData, UpdateBettingOddsData } from '@/types/betting.types';

export function useBettingService() {
    const [bettingOdds, setBettingOdds] = useState<BettingOdds[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const supabaseClient = useSupabaseClient();
    const bettingService = new BettingService(supabaseClient);

    // Load betting odds
    const loadBettingOdds = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const { odds, error } = await bettingService.getBettingOdds();
            if (error) throw error;

            setBettingOdds(odds);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load betting odds');
        } finally {
            setLoading(false);
        }
    }, []);

    // Create betting odds
    const createBettingOdds = useCallback(async (oddsData: CreateBettingOddsData) => {
        setLoading(true);
        setError(null);

        try {
            const { odds, error } = await bettingService.createBettingOdds(oddsData);
            if (error) throw error;

            if (odds) {
                setBettingOdds(prev => [odds, ...prev]);
            }

            return { success: true, data: odds };
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to create betting odds';
            setError(errorMessage);
            return { success: false, error: errorMessage };
        } finally {
            setLoading(false);
        }
    }, []);

    // Update betting odds
    const updateBettingOdds = useCallback(async (id: string, oddsData: UpdateBettingOddsData) => {
        setLoading(true);
        setError(null);

        try {
            const { odds, error } = await bettingService.updateBettingOdds(id, oddsData);
            if (error) throw error;

            if (odds) {
                setBettingOdds(prev =>
                    prev.map(odd => odd.id === id ? odds : odd)
                );
            }

            return { success: true, data: odds };
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to update betting odds';
            setError(errorMessage);
            return { success: false, error: errorMessage };
        } finally {
            setLoading(false);
        }
    }, []);

    // Send betting odds
    const sendBettingOdds = useCallback(async (id: string) => {
        setLoading(true);
        setError(null);

        try {
            const { odds, error } = await bettingService.sendBettingOdds(id);
            if (error) throw error;

            if (odds) {
                setBettingOdds(prev =>
                    prev.map(odd => odd.id === id ? odds : odd)
                );
            }

            return { success: true, data: odds };
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to send betting odds';
            setError(errorMessage);
            return { success: false, error: errorMessage };
        } finally {
            setLoading(false);
        }
    }, []);

    // Delete betting odds
    const deleteBettingOdds = useCallback(async (id: string) => {
        setLoading(true);
        setError(null);

        try {
            const { error } = await bettingService.deleteBettingOdds(id);
            if (error) throw error;

            setBettingOdds(prev => prev.filter(odd => odd.id !== id));

            return { success: true };
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to delete betting odds';
            setError(errorMessage);
            return { success: false, error: errorMessage };
        } finally {
            setLoading(false);
        }
    }, []);

    // Load betting odds on mount
    useEffect(() => {
        loadBettingOdds();
    }, [loadBettingOdds]);

    return {
        bettingOdds,
        loading,
        error,
        loadBettingOdds,
        createBettingOdds,
        updateBettingOdds,
        sendBettingOdds,
        deleteBettingOdds,
    };
}
