"use client";

import { <PERSON>, Button, Result } from "antd";
import { MailOutlined } from "@ant-design/icons";
import Link from "next/link";
import styled from "styled-components";

const VerifyContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const VerifyCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  text-align: center;
`;

export default function VerifyEmailPage() {
  return (
    <VerifyContainer>
      <VerifyCard>
        <Result
          icon={<MailOutlined style={{ color: "#667eea", fontSize: "72px" }} />}
          title="Kiểm tra email của bạn"
          subTitle="Chúng tôi đã gửi một liên kết x<PERSON>c nhận đến email của bạn. <PERSON>ui lòng kiểm tra hộp thư và nhấp vào liên kết để kích hoạt tài khoản."
          extra={[
            <Link href="/auth/login" key="login">
              <Button type="primary" size="large">
                Quay lại đăng nhập
              </Button>
            </Link>,
          ]}
        />
      </VerifyCard>
    </VerifyContainer>
  );
}
