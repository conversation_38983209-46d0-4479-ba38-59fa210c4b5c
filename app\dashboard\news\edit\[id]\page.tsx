'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  message,
  Switch,
  Divider,
  Row,
  Col,
  Spin,
  Select,
} from 'antd';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import RichTextEditor from '@/components/RichTextEditor';
import { useNewsService } from '@/hooks/useNewsService';
import { UpdatePostRequest, PostWithSeo } from '@/types/news.types';
import { Category } from '@/types/category.types';
import { useAuth } from '@/hooks/useAuth';
import { fetchCategories } from '@/lib/api/categories';
import { createClient } from '@/lib/supabase/client';

const { TextArea } = Input;

export default function EditNewsPage() {
  const [form] = Form.useForm();
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const newsService = useNewsService();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [post, setPost] = useState<PostWithSeo | null>(null);
  const [isPublished, setIsPublished] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [content, setContent] = useState('');
  const supabase = createClient();

  const postId = params.id as string;

  useEffect(() => {
    if (postId) {
      fetchPost();
      loadCategories();
    }
  }, [postId]);

  const loadCategories = async () => {
    try {
      const categoriesData = await fetchCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchPost = async () => {
    try {
      setFetchLoading(true);
      const postData = await newsService.fetchPost(postId);

      if (postData) {
        setPost(postData);
        setIsPublished(postData.published_at !== null);

        // Lấy danh mục hiện tại của bài viết
        const { data: postCategories } = await supabase
          .from('post_categories')
          .select('category_id')
          .eq('post_id', postData.id);

        const currentCategoryIds = postCategories?.map(pc => pc.category_id) || [];

        // Pre-fill form with existing data (SEO fields now part of post)
        const formValues = {
          title: postData.title,
          description: postData.description || '',
          thumbnail: postData.thumbnail || '',
          categories: currentCategoryIds,
          slug: postData.slug || '',
          meta_title: postData.meta_title || '',
          meta_description: postData.meta_description || '',
          meta_keywords: postData.meta_keywords || '',
        };

        console.log('=== DEBUG: Form values to set ===', formValues);
        form.setFieldsValue(formValues);

        // Verify form values after setting
        setTimeout(() => {
          console.log('=== DEBUG: Form values after setting ===', form.getFieldsValue());
        }, 100);

        // Set content cho ReactQuill
        setContent(postData.content || '');
      } else {
        message.error('Không tìm thấy bài viết');
        router.push('/dashboard/news');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      message.error('Có lỗi xảy ra khi tải bài viết');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    if (!post) return;

    console.log('=== DEBUG: Form values ===', values);
    console.log('=== DEBUG: Form validation errors ===', form.getFieldsError());
    console.log('=== DEBUG: All form fields ===', form.getFieldsValue());

    // Validate content (markdown format)
    const contentText = content.replace(/[#*_`~\[\]()]/g, '').trim();
    if (!content || content.trim() === '' || contentText.length < 50) {
      message.error('Nội dung bài viết phải có ít nhất 50 ký tự!');
      return;
    }

    setLoading(true);
    try {
      // Update post data
      const postData: UpdatePostRequest = {
        title: values.title,
        description: values.description || null,
        content: content, // Sử dụng content từ ReactQuill
        thumbnail: values.thumbnail || null,
        published_at: isPublished ? (post.published_at || new Date().toISOString()) : null,
        // SEO fields
        slug: values.slug || null,
        meta_title: values.meta_title || null,
        meta_description: values.meta_description || null,
        meta_keywords: values.meta_keywords || null,
      };

      console.log('=== DEBUG: Post data ===', postData);
      const updatedPost = await newsService.updatePost(post.id, postData);
      if (!updatedPost) {
        throw new Error('Không thể cập nhật bài viết');
      }
      console.log('=== DEBUG: Post updated successfully ===');

      // Cập nhật danh mục
      if (values.categories) {
        console.log('=== DEBUG: Updating categories ===', values.categories);
        // Xóa tất cả danh mục hiện tại
        await supabase
          .from('post_categories')
          .delete()
          .eq('post_id', post.id);

        // Thêm danh mục mới nếu có
        if (values.categories.length > 0) {
          const categoryInserts = values.categories.map((categoryId: string) => ({
            post_id: post.id,
            category_id: categoryId
          }));
          await supabase.from('post_categories').insert(categoryInserts);
        }
        console.log('=== DEBUG: Categories updated successfully ===');
      }

      // SEO fields are now part of the post update
      console.log('=== DEBUG: SEO fields included in post update ===');

      message.success('Cập nhật bài viết thành công!');
      router.push('/dashboard/news');
    } catch (error) {
      console.error('=== DEBUG: Error updating post ===', error);
      message.error('Có lỗi xảy ra khi cập nhật bài viết');
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    const slug = generateSlug(title);
    form.setFieldsValue({ slug, meta_title: title });
  };

  const handlePublishToggle = async (checked: boolean) => {
    if (!post) return;

    try {
      if (checked) {
        await newsService.publishPost(post.id);
        message.success('Đã xuất bản bài viết');
      } else {
        await newsService.unpublishPost(post.id);
        message.success('Đã hủy xuất bản bài viết');
      }
      setIsPublished(checked);
      await fetchPost(); // Refresh data
    } catch (error) {
      console.error('Error toggling publish status:', error);
      message.error('Có lỗi xảy ra khi thay đổi trạng thái xuất bản');
    }
  };

  if (fetchLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>Đang tải bài viết...</div>
      </div>
    );
  }

  if (!post) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <div>Không tìm thấy bài viết</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Quay lại
          </Button>
          <h2 style={{ margin: 0 }}>Chỉnh sửa bài viết</h2>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        onFinishFailed={(errorInfo) => {
          console.log('=== DEBUG: Form validation failed ===', errorInfo);
        }}
      >
        <Row gutter={24}>
          <Col span={16}>
            <Card title="Nội dung bài viết" style={{ marginBottom: '24px' }}>
              <Form.Item
                name="categories"
                label="Danh mục"
              >
                <Select
                  mode="multiple"
                  placeholder="Chọn danh mục"
                  options={categories.map(cat => ({
                    label: cat.name,
                    value: cat.id
                  }))}
                />
              </Form.Item>

              <Form.Item
                name="title"
                label="Tiêu đề"
                rules={[
                  { required: true, message: 'Vui lòng nhập tiêu đề' },
                  { min: 5, message: 'Tiêu đề phải có ít nhất 5 ký tự' },
                ]}
              >
                <Input
                  placeholder="Nhập tiêu đề bài viết"
                  onChange={handleTitleChange}
                />
              </Form.Item>

              <Form.Item
                name="description"
                label="Mô tả ngắn"
                rules={[
                  { max: 500, message: 'Mô tả không được quá 500 ký tự' },
                ]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập mô tả ngắn cho bài viết (tối đa 500 ký tự)"
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              <Form.Item
                name="thumbnail"
                label="Ảnh đại diện"
                rules={[
                  { type: 'url', message: 'Vui lòng nhập URL hợp lệ' },
                ]}
              >
                <Input
                  placeholder="Nhập URL ảnh đại diện (ví dụ: https://example.com/image.jpg)"
                />
              </Form.Item>

              <Form.Item
                label="Nội dung"
                required
              >
                <RichTextEditor
                  value={content}
                  onChange={setContent}
                  placeholder="Nhập nội dung bài viết..."
                  height="300px"
                />
              </Form.Item>
            </Card>
          </Col>

          <Col span={8}>
            <Card title="Cài đặt xuất bản" style={{ marginBottom: '24px' }}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>Trạng thái hiện tại:</div>
                <Switch
                  checked={isPublished}
                  onChange={handlePublishToggle}
                  checkedChildren="Đã xuất bản"
                  unCheckedChildren="Bản nháp"
                />
              </div>

              {post.published_at && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Xuất bản lúc: {new Date(post.published_at).toLocaleString('vi-VN')}
                </div>
              )}
            </Card>

            <Card title="SEO Settings">
              <Form.Item
                name="slug"
                label="Slug (URL)"
                rules={[
                  { pattern: /^[a-z0-9-]+$/, message: 'Slug chỉ chứa chữ thường, số và dấu gạch ngang' },
                ]}
              >
                <Input placeholder="auto-generated-from-title" />
              </Form.Item>

              <Form.Item
                name="meta_title"
                label="Meta Title"
                rules={[
                  { max: 60, message: 'Meta title không được quá 60 ký tự' },
                ]}
              >
                <Input placeholder="Tiêu đề SEO" />
              </Form.Item>

              <Form.Item
                name="meta_description"
                label="Meta Description"
                rules={[
                  { max: 160, message: 'Meta description không được quá 160 ký tự' },
                ]}
              >
                <TextArea
                  rows={3}
                  placeholder="Mô tả ngắn cho SEO"
                />
              </Form.Item>

              <Form.Item
                name="meta_keywords"
                label="Meta Keywords"
              >
                <Input placeholder="keyword1, keyword2, keyword3" />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        <Divider />

        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={() => router.back()}>
              Hủy
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              Lưu thay đổi
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
}