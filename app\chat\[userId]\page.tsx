"use client";

import { useState, useEffect } from "react";
import { Input, Button, Avatar, Typography, message as antMessage } from "antd";
import { SendOutlined } from "@ant-design/icons";
import { createClient } from "@/lib/supabase/client";
import { useParams } from "next/navigation";
import {
  <PERSON>t<PERSON>ontainer,
  ChatCard,
  ChatHeader,
  MessagesArea,
  MessageBubble,
  MessageContent,
  InputArea,
  NameInputArea,
} from "@/styles/chat.styles";

const { Text, Title } = Typography;
const { TextArea } = Input;

interface Message {
  id: string;
  content: string;
  sender_name: string;
  is_from_visitor: boolean;
  created_at: string;
}

interface AdminUser {
  id: string;
  full_name?: string;
  email: string;
  role: string;
  avatar_url?: string;
}

export default function PublicChatPage() {
  const params = useParams();
  const userId = params.userId as string;

  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [visitorName, setVisitorName] = useState("");
  const [hasJoined, setHasJoined] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  const supabase = createClient();

  useEffect(() => {
    loadAdminUser();
  }, [userId]);

  useEffect(() => {
    if (hasJoined && adminUser) {
      loadMessages();
      subscribeToMessages();
    }
  }, [hasJoined, adminUser]);

  const loadAdminUser = async () => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("chat_link_id", userId)
        .single();

      if (error) throw error;
      setAdminUser(data);
    } catch (error) {
      console.error("Error loading admin user:", error);
      antMessage.error("Không tìm thấy người dùng");
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async () => {
    if (!adminUser) return;

    try {
      // In a real implementation, you'd have a separate table for public chat messages
      // For now, we'll simulate this
      setMessages([]);
    } catch (error) {
      console.error("Error loading messages:", error);
    }
  };

  const subscribeToMessages = () => {
    // In a real implementation, you'd subscribe to message changes
    // For now, we'll simulate this
  };

  const handleJoinChat = () => {
    if (!visitorName.trim()) {
      antMessage.error("Vui lòng nhập tên của bạn");
      return;
    }
    setHasJoined(true);
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending || !adminUser) return;

    try {
      setSending(true);

      // Create a temporary message for immediate display
      const tempMessage: Message = {
        id: Date.now().toString(),
        content: newMessage.trim(),
        sender_name: visitorName,
        is_from_visitor: true,
        created_at: new Date().toISOString(),
      };

      setMessages(prev => [...prev, tempMessage]);
      setNewMessage("");

      // In a real implementation, you'd save this to a public_messages table
      // and notify the admin user via realtime subscription

      antMessage.success("Tin nhắn đã được gửi!");
    } catch (error) {
      console.error("Error sending message:", error);
      antMessage.error("Không thể gửi tin nhắn");
    } finally {
      setSending(false);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString("vi-VN", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <ChatContainer>
        <ChatCard loading />
      </ChatContainer>
    );
  }

  if (!adminUser) {
    return (
      <ChatContainer>
        <ChatCard>
          <div style={{ padding: "40px", textAlign: "center" }}>
            <Title level={3}>Không tìm thấy người dùng</Title>
            <Text type="secondary">Link chat không hợp lệ hoặc đã hết hạn</Text>
          </div>
        </ChatCard>
      </ChatContainer>
    );
  }

  return (
    <ChatContainer>
      <ChatCard>
        <ChatHeader>
          <Avatar src={adminUser.avatar_url} size={64}>
            {adminUser.full_name?.charAt(0) || adminUser.email.charAt(0)}
          </Avatar>
          <Title level={4} style={{ margin: "12px 0 4px" }}>
            {adminUser.full_name || adminUser.email}
          </Title>
          <Text type="secondary">
            {adminUser.role === "admin" ? "Quản trị viên" : "Bình luận viên"}
          </Text>
        </ChatHeader>

        {!hasJoined ? (
          <NameInputArea>
            <Title level={5}>Nhập tên của bạn để bắt đầu trò chuyện</Title>
            <Input
              placeholder="Tên của bạn..."
              value={visitorName}
              onChange={e => setVisitorName(e.target.value)}
              onPressEnter={handleJoinChat}
              style={{ marginBottom: "16px" }}
            />
            <Button type="primary" onClick={handleJoinChat} block>
              Bắt đầu chat
            </Button>
          </NameInputArea>
        ) : (
          <>
            <MessagesArea>
              {messages.length === 0 ? (
                <div
                  style={{
                    textAlign: "center",
                    color: "#999",
                    padding: "20px",
                  }}
                >
                  <Text type="secondary">
                    Chào {visitorName}! Hãy gửi tin nhắn để bắt đầu cuộc trò
                    chuyện.
                  </Text>
                </div>
              ) : (
                messages.map(message => (
                  <MessageBubble
                    key={message.id}
                    $isOwn={message.is_from_visitor}
                  >
                    {!message.is_from_visitor && (
                      <Avatar src={adminUser.avatar_url} size={32}>
                        {adminUser.full_name?.charAt(0) ||
                          adminUser.email.charAt(0)}
                      </Avatar>
                    )}
                    <div>
                      <MessageContent $isOwn={message.is_from_visitor}>
                        {message.content}
                      </MessageContent>
                      <div
                        style={{
                          fontSize: "11px",
                          color: "#999",
                          marginTop: "4px",
                          textAlign: message.is_from_visitor ? "right" : "left",
                        }}
                      >
                        {formatTime(message.created_at)}
                      </div>
                    </div>
                  </MessageBubble>
                ))
              )}
            </MessagesArea>

            <InputArea>
              <div
                style={{ display: "flex", gap: "8px", alignItems: "flex-end" }}
              >
                <TextArea
                  value={newMessage}
                  onChange={e => setNewMessage(e.target.value)}
                  placeholder="Nhập tin nhắn..."
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  onPressEnter={e => {
                    if (!e.shiftKey) {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                />
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={sendMessage}
                  loading={sending}
                  disabled={!newMessage.trim()}
                />
              </div>
            </InputArea>
          </>
        )}
      </ChatCard>
    </ChatContainer>
  );
}
