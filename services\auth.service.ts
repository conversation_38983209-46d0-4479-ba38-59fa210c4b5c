import { createClient } from "@/lib/supabase/client";
import type { User } from "@supabase/supabase-js";
import type {
  Profile,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  ProfileResponse,
} from "@/types/auth.types";

class AuthService {
  private supabase;

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }

  // Static method to create instance with default client for backward compatibility
  static createWithDefaultClient() {
    return new AuthService(createClient());
  }

  async signIn(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async signUp(registerData: RegisterData): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email: registerData.email,
        password: registerData.password,
        options: {
          emailRedirectTo:
            process.env.NEXT_PUBLIC_DEV_SUPABASE_REDIRECT_URL ||
            `${window.location.origin}/dashboard`,
          data: {
            full_name: registerData.fullName,
            role: registerData.role,
          },
        },
      });

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async signInWithGoogle(): Promise<{ error: Error | null }> {
    try {
      const { error } = await this.supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo:
            process.env.NEXT_PUBLIC_DEV_SUPABASE_REDIRECT_URL ||
            `${window.location.origin}/dashboard`,
        },
      });

      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async signOut(): Promise<{ error: Error | null }> {
    try {
      const { error } = await this.supabase.auth.signOut();
      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getCurrentUser(): Promise<{ user: User | null; error: Error | null }> {
    try {
      const { data, error } = await this.supabase.auth.getUser();

      // Handle "Auth session missing" as a normal case, not an error
      if (error && error.message === "Auth session missing!") {
        return {
          user: null,
          error: null, // No error, just no session
        };
      }

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async getProfile(userId: string): Promise<ProfileResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      return {
        profile: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  async updateProfile(
    userId: string,
    updates: Partial<Profile>
  ): Promise<ProfileResponse> {
    try {
      const { data, error } = await this.supabase
        .from("profiles")
        .update(updates)
        .eq("id", userId)
        .select()
        .single();

      return {
        profile: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  }

  onAuthStateChange(callback: (event: string, session: any) => void) {
    return this.supabase.auth.onAuthStateChange(callback);
  }
}

export const authService = AuthService.createWithDefaultClient();
