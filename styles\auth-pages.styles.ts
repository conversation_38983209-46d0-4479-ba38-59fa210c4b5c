import styled from "styled-components";
import { Card, Input, But<PERSON>, Typography } from "antd";

const { Title, Text } = Typography;

// Login Page Styles
export const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

export const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: none;
`;

export const LogoSection = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

export const LogoIcon = styled.div`
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 24px;
  margin: 0 auto 16px;
`;

export const FormSection = styled.div`
  .ant-input-affix-wrapper {
    height: 48px;
    border-radius: 8px;
    border: 1px solid #d1d5db;

    &:hover,
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }

  .ant-btn-primary {
    height: 48px;
    border-radius: 8px;
    background: linear-gradient(135deg, #1890ff, #1d4ed8);
    border: none;
    font-weight: 500;

    &:hover {
      background: linear-gradient(135deg, #2563eb, #1e40af);
    }
  }
`;

export const InputGroup = styled.div`
  margin-bottom: 20px;
`;

export const Label = styled(Text)`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
`;

// Register Page Styles
export const RegisterContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

export const RegisterCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: none;
`;

export const RegisterFormSection = styled.div`
  .ant-input-affix-wrapper {
    height: 48px;
    border-radius: 8px;
    border: 1px solid #d1d5db;

    &:hover,
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }

  .ant-select {
    height: 48px;

    .ant-select-selector {
      height: 48px !important;
      border-radius: 8px;
      border: 1px solid #d1d5db;

      &:hover,
      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .ant-btn-primary {
    height: 48px;
    border-radius: 8px;
    background: linear-gradient(135deg, #1890ff, #1d4ed8);
    border: none;
    font-weight: 500;

    &:hover {
      background: linear-gradient(135deg, #2563eb, #1e40af);
    }
  }
`;

export const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 20px;

  .ant-form-item {
    flex: 1;
    margin-bottom: 0;
  }
`;

export const GoogleButton = styled(Button)`
  width: 100%;
  height: 48px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }
`;

export const DividerText = styled(Text)`
  color: #6b7280;
  font-size: 14px;
`;

export const LoginLink = styled(Text)`
  color: #1890ff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

// Join Page Styles
export const JoinContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

export const JoinCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: none;
`;

export const JoinHeader = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

export const JoinIcon = styled.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  margin: 0 auto 16px;
`;

export const JoinContent = styled.div`
  text-align: center;
  margin-bottom: 32px;
`;

export const JoinButton = styled(Button)`
  width: 100%;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  font-weight: 500;

  &:hover {
    background: linear-gradient(135deg, #059669, #047857);
  }
`;

export const BackButton = styled(Button)`
  width: 100%;
  height: 48px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  font-weight: 500;

  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }
`;

// Verify Email Page Styles
export const VerifyContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

export const VerifyCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: none;
  text-align: center;
`;

export const VerifyIcon = styled.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1890ff, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  margin: 0 auto 24px;
`;

export const VerifyContent = styled.div`
  margin-bottom: 32px;
`;

export const VerifyButton = styled(Button)`
  width: 100%;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #1890ff, #1d4ed8);
  border: none;
  font-weight: 500;

  &:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
  }
`;

export const AuthButton = styled(Button)`
  height: 45px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
`;
