# QCLG Chat System - Database Setup

## 📁 Files Overview

Sau khi phân tích và gộp tất cả các file trong thư mục `scripts/`, chúng ta có 2 file chính:

### 🗄️ `migrate.sql` - Database Migration

File này chứa toàn bộ cấu trúc database:

- **7 tables** với relationships đầy đủ
- **RLS policies** cho security
- **12 indexes** cho performance
- **9 functions** cho automation
- **1 view** cho complex queries
- **Triggers** cho user registration
- **Realtime** enabled cho tất cả tables
- **Support** cho tất cả room types: `general`, `private`, `direct`, `group`
- **Support** cho tất cả user roles: `admin`, `blv`, `member`

### 🌱 `seed.sql` - Sample Data

File này chứa dữ liệu mẫu để test:

- **8 users** với các role khác nhau
- **8 profiles** tương ứng
- **8 chat rooms** với các loại khác nhau
- **Sample messages** trong các rooms
- **User presence** data
- **Message reads** examples

### 🎯 `seed_betting_data.sql` - Betting Data

File này chứa dữ liệu mẫu cho chức năng "Lên kèo":

- **3 sports** (Bóng đá, Bóng rổ, Tennis)
- **6 leagues** (Premier League, La Liga, Bundesliga, Serie A, Ligue 1, NBA)
- **8 teams** với thông tin chi tiết
- **6 matches** (upcoming, live, completed)
- **6 match predictions** với odds
- **6 kèo bóng** từ các BLV
- **6 players** với thống kê
- **Team/Player stats** và ratings
- **Match events** và streams

## 🚀 Quick Start

### 1. Run Migration

```sql
-- Chạy file migrate.sql trong Supabase SQL Editor
-- File này sẽ tạo toàn bộ cấu trúc database
```

### 2. Run Seed Data

```sql
-- Chạy file seed.sql trong Supabase SQL Editor
-- File này sẽ thêm dữ liệu mẫu để test
```

### 3. Run Betting Data (Optional)

```sql
-- Chạy file seed_betting_data.sql trong Supabase SQL Editor
-- File này sẽ thêm dữ liệu mẫu cho chức năng "Lên kèo"
```

## 🔐 Test Credentials

Sau khi chạy seed, bạn có thể đăng nhập với:

| Role     | Email            | Password  |
| -------- | ---------------- | --------- |
| Admin    | <EMAIL>   | admin123  |
| Admin 2  | <EMAIL>  | admin123  |
| BLV 1    | <EMAIL>    | blv123    |
| BLV 2    | <EMAIL>    | blv123    |
| BLV 3    | <EMAIL>    | blv123    |
| Member 1 | <EMAIL> | member123 |
| Member 2 | <EMAIL> | member123 |
| Member 3 | <EMAIL> | member123 |

## 🏠 Available Chat Rooms

Sau khi seed, bạn sẽ có các rooms:

### General Rooms

- **Nhóm Chat Tổng** (general) - Tất cả users

### Private Rooms

- **Phòng Admin** (private) - Chỉ admins
- **Phòng BLV** (private) - Chỉ BLVs

### Direct Rooms (1-1)

- **Chat riêng với Thành Viên 1** (direct) - Admin ↔ Member 1
- **Chat riêng với Thành Viên 2** (direct) - BLV 1 ↔ Member 2
- **Chat riêng với Thành Viên 3** (direct) - Admin 2 ↔ Member 3

### Group Rooms

- **Nhóm BLV** (group) - Tất cả BLVs
- **Nhóm Thành Viên** (group) - Tất cả Members

## 📊 Database Schema

### Tables Created

#### Chat System Tables

1. **profiles** - User profiles (extends auth.users)
2. **chat_rooms** - Chat rooms (general, private, direct, group)
3. **chat_room_members** - Room membership
4. **messages** - Chat messages
5. **message_reads** - Message read status
6. **file_attachments** - File attachments
7. **user_presence** - Online status

#### Betting System Tables

8. **sports** - Sports categories
9. **leagues** - Football/basketball leagues
10. **teams** - Teams in leagues
11. **matches** - Match fixtures and results
12. **match_predictions** - Odds and predictions
13. **keo_bong** - Betting tips from BLVs
14. **players** - Player information
15. **player_season_stats** - Player statistics
16. **team_season_stats** - Team statistics
17. **team_ratings** - Elo ratings
18. **match_events** - Live match events
19. **match_streams** - Stream information
20. **match_lineups** - Team lineups
21. **articles** - News articles
22. **article_categories** - Article categories
23. **article_tags** - Article tags
24. **article_comments** - Article comments

### Key Features

- ✅ **Row Level Security (RLS)** enabled
- ✅ **Realtime** subscriptions
- ✅ **Foreign key** constraints
- ✅ **Check constraints** for roles and types
- ✅ **Indexes** for performance
- ✅ **Functions** for automation
- ✅ **Triggers** for user registration
- ✅ **Policies** for data access control

## 🔧 What Was Consolidated

### From Original Files:

- `00_migrate_database.sql` - Main migration
- `01_seed_database.sql` - Main seed data
- `FIX_ALL_CONSTRAINTS.sql` - Constraint fixes
- `UPDATE_RPC_FUNCTIONS.sql` - Function updates
- `ADD_DELETE_POLICY.sql` - Delete policies
- `FIX_CHAT_ROOMS_TYPE_CONSTRAINT.sql` - Type constraints
- `fix_role_constraint.sql` - Role constraints
- `COMPLETE_DATABASE_FIX.sql` - Database fixes
- `FINAL_FIX_GUIDE.md` - Fix guides
- `MEMBER_ROLE_FIX_GUIDE.md` - Member role fixes
- `REALTIME_FIX_STEPS.md` - Realtime fixes
- `PRODUCTION_FIX_GUIDE.md` - Production fixes
- `POSTGREST_SOLUTIONS.md` - PostgREST solutions
- `FIX_REALTIME_GUIDE.md` - Realtime guide
- `DEPLOY_TO_SUPABASE.md` - Deployment guide
- `FRONTEND_API_GUIDE.md` - API guide
- `FRONTEND_QUICK_START.md` - Quick start
- `CSS_SEPARATION_SUCCESS.md` - CSS separation
- `SERVICE_LAYER_SUCCESS.md` - Service layer
- `TYPES_SEPARATION_SUCCESS.md` - Types separation
- `DELETE_CHAT_FEATURE.md` - Delete feature
- `DUAL_CHAT_LOGIC.md` - Chat logic
- `FIX_MESSAGE_WARNINGS.md` - Message warnings
- `DIRECT_CHAT_NAME_FIX.md` - Direct chat fixes
- `ONE_TO_ONE_CHAT_LOGIC.md` - 1-1 chat logic
- `DEBUG_DELETE_CHAT.md` - Delete debug
- `UI_UPDATE_GUIDE.md` - UI updates
- `SIMPLIFIED_CHAT_WORKFLOW.md` - Chat workflow
- `ADD_NEW_MENU_ITEMS.md` - Menu items
- `COMPLETE_FIX.sql` - Complete fixes
- `FINAL_FIX_ROLE_CONSTRAINT.sql` - Role constraint fixes
- `QUICK_FIX_ROLE_CONSTRAINT.sql` - Quick role fixes
- `SIMPLE_FIX_ROLE.sql` - Simple role fixes
- `REALTIME_FIX_STEPS.md` - Realtime steps
- `README.md` - Original README
- `deploy.sh` - Deployment script
- Various SQL fix files

### Consolidated Into:

- **`migrate.sql`** - Complete database schema
- **`seed.sql`** - Complete sample data
- **`seed_betting_data.sql`** - Betting and sports data
- **`README.md`** - This documentation

## ✅ Benefits

1. **Simplified Setup** - Chỉ cần chạy 2-3 files
2. **Complete Schema** - Tất cả tables, constraints, policies
3. **Sample Data** - Ready-to-test data cho chat và betting
4. **All Fixes Applied** - Tất cả fixes đã được gộp
5. **Production Ready** - Có thể deploy ngay
6. **Well Documented** - Hướng dẫn rõ ràng
7. **Betting Ready** - Sẵn sàng cho chức năng "Lên kèo"

## 🎯 Next Steps

1. **Run Migration** - Chạy `migrate.sql`
2. **Run Seed** - Chạy `seed.sql`
3. **Run Betting Data** - Chạy `seed_betting_data.sql` (optional)
4. **Test Login** - Đăng nhập với credentials
5. **Test Chat** - Test các tính năng chat
6. **Test Betting** - Test chức năng "Lên kèo"
7. **Deploy** - Deploy lên production nếu cần

---

**🎉 Database setup hoàn tất!** Bây giờ bạn có thể sử dụng hệ thống chat QCLG với đầy đủ tính năng.
