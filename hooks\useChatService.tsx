"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { useSupabaseClient } from "@/contexts/SupabaseContext";
import type { ChatRoom, ChatMessage, CreateChatData, Profile } from "@/types";

export function useChatService(userId: string, userProfile?: Profile) {
  const supabase = useSupabaseClient();
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);

  // Use ref to store the latest loadChatRooms function
  const loadChatRoomsRef = useRef<((forceRefresh?: boolean) => Promise<void>) | null>(null);

  // Load chat rooms
  const loadChatRooms = useCallback(
    async (forceRefresh = false) => {
      if (!userId || !supabase) return;

      try {
        setLoading(true);
        const { data, error } = await supabase.rpc(
          "get_user_rooms_with_messages_v5",
          { user_uuid: userId }
        );

        if (error) {
          console.error("Error loading chat rooms:", error);
          return;
        }

        const rooms: ChatRoom[] = data.map((room: any) => ({
          id: room.id,
          name: room.name,
          type: room.type,
          last_message:
            room.messages && room.messages.length > 0
              ? room.messages[room.messages.length - 1].content
              : undefined,
          last_message_at:
            room.messages && room.messages.length > 0
              ? room.messages[room.messages.length - 1].created_at
              : undefined,
          other_user:
            room.type === "direct"
              ? {
                  id: room.other_user_id,
                  full_name: room.other_user_name,
                  avatar_url: room.other_user_avatar,
                }
              : undefined,
        }));

        setChatRooms(rooms);
      } catch (error) {
        console.error("Error loading chat rooms:", error);
      } finally {
        setLoading(false);
      }
    },
    [userId, supabase]
  );

  // Update ref when loadChatRooms changes
  useEffect(() => {
    loadChatRoomsRef.current = loadChatRooms;
  }, [loadChatRooms]);


  // Load messages for a specific room
  const loadMessages = useCallback(async (roomId: string) => {
    if (!supabase) return;
    
    try {
      setMessagesLoading(true);
      const { data, error } = await supabase
        .from("messages")
        .select(`
          *,
          user:profiles!sender_id(
            full_name,
            avatar_url
          )
        `)
        .eq("room_id", roomId)
        .order("created_at", { ascending: true });

      if (error) {
        console.error("Error loading messages:", error);
        return;
      }
      
      // Map database structure to ChatMessage interface
      const mappedMessages: ChatMessage[] = (data || []).map(msg => ({
        id: msg.id,
        content: msg.content,
        created_at: msg.created_at,
        user_id: msg.sender_id, // Map sender_id to user_id
        user: {
          full_name: msg.user?.full_name || 'Unknown User',
          avatar_url: msg.user?.avatar_url
        },
        status: msg.status || 'sent'
      }));
      
      setMessages(mappedMessages);
    } catch (error) {
      console.error("Error loading messages:", error);
    } finally {
      setMessagesLoading(false);
    }
  }, [supabase]);

  // Send a message
  const sendMessage = useCallback(
    async (roomId: string, content: string) => {
      if (!userId || !supabase) return { error: new Error("User not authenticated") };

      // Optimistic update - add message immediately
      const tempMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        content: content.trim(),
        user_id: userId,
        created_at: new Date().toISOString(),
        user: {
          full_name: 'You',
          avatar_url: userProfile?.avatar_url
        },
        status: 'pending'
      };

      setMessages(prev => [...prev, tempMessage]);

      try {
        const { error } = await supabase
          .from("messages")
          .insert({
            room_id: roomId,
            sender_id: userId,
            content: content.trim(),
          });
        
        if (error) {
          // Remove optimistic message on error
          setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
          return { error: new Error(error.message) };
        }
        
        return { error: null };
      } catch (error) {
        // Remove optimistic message on error
        setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
        return { error: error instanceof Error ? error : new Error("Unknown error") };
      }
    },
    [userId, supabase]
  );

  // Create a new chat
  const createChat = useCallback(
    async (chatData: CreateChatData) => {
      if (!userId || !supabase)
        return { data: null, error: new Error("User not authenticated") };

      try {
        const { data, error } = await supabase.rpc("create_chat_room", {
           room_name: chatData.name,
           room_type: chatData.type,
           creator_id: userId,
           participant_ids: chatData.members || [],
         });

        if (error) {
          return { data: null, error: new Error(error.message) };
        }

        // Reload chat rooms after creating
        if (loadChatRoomsRef.current) {
          await loadChatRoomsRef.current(true);
        }
        
        return { data, error: null };
      } catch (error) {
        return { data: null, error: error instanceof Error ? error : new Error("Unknown error") };
      }
    },
    [userId, supabase]
  );

  // Delete a chat
  const deleteChat = useCallback(
    async (roomId: string) => {
      if (!supabase) return { error: new Error("Supabase not initialized") };
      
      try {
        const { error } = await supabase
          .from("rooms")
          .delete()
          .eq("id", roomId);

        if (error) {
          return { error: new Error(error.message) };
        }

        // Reload chat rooms after deleting
        if (loadChatRoomsRef.current) {
          await loadChatRoomsRef.current(true);
        }
        
        return { error: null };
      } catch (error) {
        return { error: error instanceof Error ? error : new Error("Unknown error") };
      }
    },
    [supabase]
  );

  // Join a chat
  const joinChat = useCallback(
    async (roomId: string) => {
      if (!userId || !supabase) return { error: new Error("User not authenticated") };

      try {
        const { error } = await supabase
          .from("room_participants")
          .insert({
            room_id: roomId,
            user_id: userId,
          });

        if (error) {
          return { error: new Error(error.message) };
        }

        // Reload chat rooms after joining
        if (loadChatRoomsRef.current) {
          await loadChatRoomsRef.current(true);
        }
        
        return { error: null };
      } catch (error) {
        return { error: error instanceof Error ? error : new Error("Unknown error") };
      }
    },
    [userId, supabase]
  );

  // Get chat room details
  const getChatRoom = useCallback(async (roomId: string) => {
    if (!supabase) return { room: null, error: new Error("Supabase not initialized") };
    
    try {
      const { data, error } = await supabase
        .from("rooms")
        .select("*")
        .eq("id", roomId)
        .single();

      if (error) {
        return { room: null, error: new Error(error.message) };
      }

      return { room: data, error: null };
    } catch (error) {
      return { room: null, error: error instanceof Error ? error : new Error("Unknown error") };
    }
  }, [supabase]);

  // Subscribe to messages for a room
  const subscribeToMessages = useCallback((roomId: string, onConnectionLost?: () => void) => {
    if (!supabase) return null;
    
    const channel = supabase
      .channel(`messages:${roomId}`, {
        config: {
          presence: {
            key: userId,
          },
        },
      })
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `room_id=eq.${roomId}`,
        },
        async (payload: any) => {
          const newMessage = payload.new as any;
          
          // Fetch user details for the message
          const { data: userData } = await supabase
            .from('profiles')
            .select('full_name, avatar_url')
            .eq('id', newMessage.sender_id)
            .single();
          
          const messageWithUser: ChatMessage = {
            id: newMessage.id,
            content: newMessage.content,
            created_at: newMessage.created_at,
            user_id: newMessage.sender_id,
            user: {
              full_name: userData?.full_name || 'Unknown',
              avatar_url: userData?.avatar_url,
            },
          };
          
          setMessages(prev => {
            // Check for duplicates by ID
            const existingIndex = prev.findIndex(msg => msg.id === messageWithUser.id);
          if (existingIndex !== -1) {
            console.warn('Duplicate message detected, ignoring:', newMessage.id);
            return prev;
          }
          
          // Check if this is a real message replacing a temporary one
          const tempIndex = prev.findIndex(msg => 
            msg.id.startsWith('temp-') &&
            msg.content === messageWithUser.content &&
            msg.user_id === messageWithUser.user_id &&
            Math.abs(new Date(msg.created_at).getTime() - new Date(messageWithUser.created_at).getTime()) < 5000
          );
          
          if (tempIndex !== -1) {
            // Replace temporary message with real one
            const updated = [...prev];
            updated[tempIndex] = messageWithUser;
            return updated;
          }
          
          return [...prev, messageWithUser];
        });
      }
    )
    .subscribe((status) => {
      if (status === 'CHANNEL_ERROR') {
         console.error('Channel error for room:', roomId);
         if (onConnectionLost) {
           onConnectionLost();
         }
         
         // Don't auto-retry to prevent subscription loops
         // Let the parent component handle reconnection
      } else if (status === 'TIMED_OUT') {
        console.warn('Subscription timed out for room:', roomId);
        if (onConnectionLost) {
          onConnectionLost();
        }
      }
    });
    
    return () => {
      if (supabase && channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [supabase]);

  // Load chat rooms on mount
  useEffect(() => {
    if (userId) {
      loadChatRooms();
    }
  }, [userId]); // Remove loadChatRooms from dependencies

  return {
    chatRooms,
    messages,
    setMessages,
    loading,
    messagesLoading,
    loadChatRooms,
    loadMessages,
    sendMessage,
    createChat,
    deleteChat,
    joinChat,
    getChatRoom,
    subscribeToMessages,
  };
}
