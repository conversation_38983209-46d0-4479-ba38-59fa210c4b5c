"use client";

import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Switch,
  message,
  Card,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Divider,
  Spin,
} from "antd";
import {
  SaveOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { useRouter, useParams } from "next/navigation";
import { useForumService } from "@/hooks/useForumService";
import type { ForumPost, CreateForumPostDto } from "@/types/forum.types";

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function EditForumPostPage() {
  const router = useRouter();
  const params = useParams();
  const postId = params.id as string;

  const { getPost, updatePost, categories, fetchCategories, loading } =
    useForumService();

  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [loadingPost, setLoadingPost] = useState(true);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [currentPost, setCurrentPost] = useState<ForumPost | null>(null);

  useEffect(() => {
    fetchCategories();
    loadPost();
  }, [fetchCategories, postId]);

  const loadPost = async () => {
    setLoadingPost(true);
    try {
      const post = await getPost(postId);
      if (post) {
        setCurrentPost(post);
        form.setFieldsValue({
          title: post.title,
          slug: post.slug,
          content: post.content,
          excerpt: post.excerpt,
          category_id: post.category_id,
          status: post.status,
          is_featured: post.is_featured,
          image_urls: post.image_urls?.join("\n") || "",
          video_urls: post.video_urls?.join("\n") || "",
        });
        setTags(post.tags || []);
      } else {
        message.error("Không tìm thấy bài viết!");
        router.push("/dashboard/forum");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi tải bài viết!");
      router.push("/dashboard/forum");
    } finally {
      setLoadingPost(false);
    }
  };

  const handleSubmit = async (values: any) => {
    if (!currentPost) return;

    setSubmitting(true);
    try {
      const imageUrls = values.image_urls
        ? values.image_urls.split("\n").filter((url: string) => url.trim())
        : [];
      const videoUrls = values.video_urls
        ? values.video_urls.split("\n").filter((url: string) => url.trim())
        : [];

      const postData: CreateForumPostDto & { id: string } = {
        id: currentPost.id,
        title: values.title,
        content: values.content,
        excerpt: values.excerpt,
        slug: values.slug,
        category_id: values.category_id,
        tags: tags.length > 0 ? tags : undefined,
        status: values.status || "draft",
        image_urls: imageUrls.length > 0 ? imageUrls : undefined,
        video_urls: videoUrls.length > 0 ? videoUrls : undefined,
      };

      const updatedPost = await updatePost(postData);
      if (updatedPost) {
        message.success("Cập nhật bài viết thành công!");
        router.push("/dashboard/forum");
      } else {
        message.error("Không thể cập nhật bài viết!");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi cập nhật bài viết!");
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, "")
      .replace(/[\s_-]+/g, "-")
      .replace(/^-+|-+$/g, "");
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    const slug = generateSlug(title);
    form.setFieldsValue({ slug });
  };

  if (loadingPost) {
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>Đang tải bài viết...</div>
      </div>
    );
  }

  if (!currentPost) {
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <div>Không tìm thấy bài viết</div>
        <Button
          style={{ marginTop: 16 }}
          onClick={() => router.push("/dashboard/forum")}
        >
          Quay lại danh sách
        </Button>
      </div>
    );
  }

  return (
    <div style={{ padding: "24px" }}>
      <Card>
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: 24 }}
        >
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => router.push("/dashboard/forum")}
              >
                Quay lại
              </Button>
              <Title level={3} style={{ margin: 0 }}>
                Chỉnh sửa bài viết
              </Title>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button onClick={() => form.resetFields()} disabled={submitting}>
                Đặt lại
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={submitting}
                onClick={() => form.submit()}
              >
                Cập nhật bài viết
              </Button>
            </Space>
          </Col>
        </Row>

        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={24}>
            <Col span={16}>
              <Card title="Thông tin cơ bản" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="Tiêu đề"
                  name="title"
                  rules={[
                    { required: true, message: "Vui lòng nhập tiêu đề!" },
                    { min: 5, message: "Tiêu đề phải có ít nhất 5 ký tự!" },
                    {
                      max: 500,
                      message: "Tiêu đề không được vượt quá 500 ký tự!",
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập tiêu đề bài viết..."
                    onChange={handleTitleChange}
                    showCount
                    maxLength={500}
                  />
                </Form.Item>

                <Form.Item
                  label="Slug (URL)"
                  name="slug"
                  rules={[
                    { required: true, message: "Vui lòng nhập slug!" },
                    {
                      pattern: /^[a-z0-9-]+$/,
                      message:
                        "Slug chỉ được chứa chữ thường, số và dấu gạch ngang!",
                    },
                  ]}
                >
                  <Input placeholder="url-friendly-slug" />
                </Form.Item>

                <Form.Item
                  label="Mô tả ngắn (Excerpt)"
                  name="excerpt"
                  rules={[
                    {
                      max: 1000,
                      message: "Mô tả không được vượt quá 1000 ký tự!",
                    },
                  ]}
                >
                  <TextArea
                    placeholder="Mô tả ngắn gọn về bài viết..."
                    rows={3}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>

                <Form.Item
                  label="Nội dung"
                  name="content"
                  rules={[
                    { required: true, message: "Vui lòng nhập nội dung!" },
                    { min: 10, message: "Nội dung phải có ít nhất 10 ký tự!" },
                  ]}
                >
                  <TextArea
                    placeholder="Nhập nội dung bài viết..."
                    rows={12}
                    showCount
                    maxLength={50000}
                  />
                </Form.Item>
              </Card>

              <Card title="Hình ảnh và Video" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="URLs hình ảnh (mỗi URL một dòng)"
                  name="image_urls"
                >
                  <TextArea
                    placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"
                    rows={3}
                  />
                  <div
                    style={{ fontSize: "12px", color: "#666", marginTop: 4 }}
                  >
                    Các URL hình ảnh sẽ được lưu trữ tại
                    service.ngoaihangtv.live
                  </div>
                </Form.Item>

                <Form.Item
                  label="URLs video (mỗi URL một dòng)"
                  name="video_urls"
                >
                  <TextArea
                    placeholder="https://example.com/video1.mp4&#10;https://example.com/video2.mp4"
                    rows={2}
                  />
                  <div
                    style={{ fontSize: "12px", color: "#666", marginTop: 4 }}
                  >
                    Các URL video sẽ được lưu trữ tại service.ngoaihangtv.live
                  </div>
                </Form.Item>
              </Card>
            </Col>

            <Col span={8}>
              <Card title="Cài đặt" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="Danh mục"
                  name="category_id"
                  rules={[
                    { required: true, message: "Vui lòng chọn danh mục!" },
                  ]}
                >
                  <Select placeholder="Chọn danh mục">
                    {categories.map(category => (
                      <Option key={category.id} value={category.id}>
                        <Space>
                          <div
                            style={{
                              width: 12,
                              height: 12,
                              backgroundColor: category.color || "#1890ff",
                              borderRadius: "50%",
                            }}
                          />
                          {category.name}
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item label="Trạng thái" name="status">
                  <Select>
                    <Option value="draft">Bản nháp</Option>
                    <Option value="pending">Chờ duyệt</Option>
                    <Option value="approved">Đã duyệt</Option>
                    <Option value="rejected">Từ chối</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  label="Bài viết nổi bật"
                  name="is_featured"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Card>

              <Card title="Tags" style={{ marginBottom: 16 }}>
                <Space style={{ marginBottom: 16 }}>
                  <Input
                    placeholder="Thêm tag..."
                    value={tagInput}
                    onChange={e => setTagInput(e.target.value)}
                    onPressEnter={e => {
                      e.preventDefault();
                      handleAddTag();
                    }}
                    style={{ width: 150 }}
                  />
                  <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={handleAddTag}
                  >
                    Thêm
                  </Button>
                </Space>

                <div style={{ minHeight: 32 }}>
                  {tags.map(tag => (
                    <Tag
                      key={tag}
                      closable
                      onClose={() => handleRemoveTag(tag)}
                      style={{ marginBottom: 4 }}
                    >
                      {tag}
                    </Tag>
                  ))}
                </div>
              </Card>

              <Card title="Thông tin bài viết">
                <div style={{ fontSize: "12px", color: "#666" }}>
                  <p>
                    <strong>Tác giả:</strong>{" "}
                    {currentPost.author?.full_name || "Không xác định"}
                  </p>
                  <p>
                    <strong>Ngày tạo:</strong>{" "}
                    {new Date(currentPost.created_at).toLocaleString("vi-VN")}
                  </p>
                  <p>
                    <strong>Cập nhật:</strong>{" "}
                    {new Date(currentPost.updated_at).toLocaleString("vi-VN")}
                  </p>
                  <p>
                    <strong>Lượt xem:</strong> {currentPost.view_count}
                  </p>
                  <p>
                    <strong>Bình luận:</strong> {currentPost.comment_count}
                  </p>
                  <p>
                    <strong>Likes:</strong> {currentPost.like_count}
                  </p>
                </div>
              </Card>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
}
