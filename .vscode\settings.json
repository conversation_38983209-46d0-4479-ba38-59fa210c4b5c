{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}