"use client";

import { bettingService } from "@/services/betting.service";
import type { BettingOdds, CreateBettingOddsData, UpdateBettingOddsData, ExpertInfo } from "@/types/betting.types";
import { profileService } from '@/services/profile.service';
import { Profile } from '@/types/profile.types';
import { chatService } from '@/services/chat.service';
import type { CreateChatData } from '@/types/chat.types';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SendOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import {
  App,
  Button,
  Card,
  Col,
  Form,
  Input,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";

const { Title, Text } = Typography;
const { TextArea } = Input;

interface BettingOddsFormData {
  expert: string;
  tournament: string;
  team_name: string;
  closing_bet: string;
  saying: string;
  status?: "win" | "lose" | null;
  type?: string;
}

export default function BettingOddsAdmin() {
  const { message } = App.useApp();
  const [odds, setOdds] = useState<BettingOdds[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOdds, setEditingOdds] = useState<BettingOdds | null>(null);
  const [form] = Form.useForm<BettingOddsFormData>();
  const [sendingIds, setSendingIds] = useState<Set<string>>(new Set());
  const [updatingIds, setUpdatingIds] = useState<Set<string>>(new Set());

  // State for quick import
  const [quickImportVisible, setQuickImportVisible] = useState(false);
  const [quickImportForm] = Form.useForm();

  // State for admin list
  const [adminList, setAdminList] = useState<Profile[]>([]);

  // State for betting types
  const [bettingTypes, setBettingTypes] = useState([
    { value: "KÈO VIP", label: "KÈO VIP" },
    { value: "NHẬN KÈO VIP", label: "NHẬN KÈO VIP" },
  ]);

  // Fetch admin list on component mount
  useEffect(() => {
    const fetchAdminList = async () => {
      try {
        const { profiles, error } = await profileService.getProfilesByRole('admin');
        if (!error && profiles) {
          setAdminList(profiles);
        }
      } catch (error) {
        console.error('Error fetching admin list:', error);
      }
    };

    fetchAdminList();
  }, []);

  // Parse quick import data
  const parseQuickImportData = (text: string) => {
    const lines = text.trim().split('\n').map(line => line.trim()).filter(line => line);

    if (lines.length < 4) {
      throw new Error('Dữ liệu không đủ thông tin. Cần ít nhất 4 dòng: Giải đấu, Hiệp/Thời gian, Đội 1, Đội 2');
    }

    // Find tournament line (usually contains "GIẢI" or is the first substantial line)
    let tournamentIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('GIẢI') || lines[i].length > 10) {
        tournamentIndex = i;
        break;
      }
    }

    const tournament = lines[tournamentIndex].replace(/\t/g, '').trim();

    // Find teams (usually the last two substantial lines)
    const teams = [];
    for (let i = lines.length - 1; i >= 0 && teams.length < 2; i--) {
      if (lines[i] && !lines[i].includes('GIẢI') && !lines[i].match(/^\d+[HT']?$/) && !lines[i].includes('-')) {
        teams.unshift(lines[i]);
      }
    }

    if (teams.length < 2) {
      throw new Error('Không tìm thấy đủ thông tin về 2 đội bóng');
    }

    const teamName = `${teams[0]} vs ${teams[1]}`;

    return {
      tournament,
      team_name: teamName,
    };
  };

  // Handle quick import
  const handleQuickImport = async (values: { importData: string }) => {
    try {
      const parsedData = parseQuickImportData(values.importData);

      // Set parsed data to main form
      form.setFieldsValue({
        tournament: parsedData.tournament,
        team_name: parsedData.team_name,
      });

      // Close quick import modal and open main modal
      setQuickImportVisible(false);
      quickImportForm.resetFields();
      setEditingOdds(null);
      setModalVisible(true);

      message.success('Đã nhập dữ liệu thành công! Vui lòng điền thêm thông tin còn lại.');
    } catch (error) {
      message.error(`Lỗi phân tích dữ liệu: ${error instanceof Error ? error.message : 'Định dạng không hợp lệ'}`);
    }
  };

  // Load betting odds
  const loadBettingOdds = async () => {
    setLoading(true);
    try {
      const { odds: data, error } = await bettingService.getBettingOdds();
      if (error) {
        message.error(`Lỗi tải dữ liệu: ${error.message}`);
        return;
      }
      setOdds(data);
    } catch (error) {
      message.error("Có lỗi xảy ra khi tải dữ liệu");
      console.error("Load betting odds error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBettingOdds();
  }, []);

  // Handle create/update
  const handleSubmit = async (values: BettingOddsFormData) => {
    try {
      // Check if selected value is an admin ID or custom expert name
      let expertData: string | ExpertInfo = values.expert;

      const selectedAdmin = adminList.find(admin => admin.id === values.expert);
      if (selectedAdmin) {
        // If it's an admin ID, create expert object with full info
        expertData = {
          id: selectedAdmin.id,
          name: selectedAdmin.full_name,
          email: selectedAdmin.email,
          avatar_url: selectedAdmin.avatar_url,
          role: selectedAdmin.role
        };
      }
      // Otherwise, keep it as string (custom expert name)

      const formData = {
        expert: expertData,
        tournament: values.tournament,
        team_name: values.team_name,
        closing_bet: values.closing_bet,
        saying: values.saying,
        status: values.status,
        type: values.type
      };

      if (editingOdds) {
        // Update existing odds
        const { error } = await bettingService.updateBettingOdds(editingOdds.id, formData);
        if (error) {
          message.error(`Lỗi cập nhật: ${error.message}`);
          return;
        }
        message.success("Cập nhật thành công!");
      } else {
        // Create new odds
        const { error } = await bettingService.createBettingOdds(formData);
        if (error) {
          message.error(`Lỗi tạo mới: ${error.message}`);
          return;
        }

        // Auto-create chat room if admin is selected
        if (selectedAdmin) {
          try {
            const chatData: CreateChatData = {
              name: `Chat kèo: ${values.tournament} - ${values.team_name}`,
              type: 'direct',
              members: [selectedAdmin.id]
            };

            const chatResponse = await chatService.createChat(chatData, selectedAdmin.id);
            if (chatResponse.data?.id) {
              message.success("Tạo kèo và phòng chat thành công!");
            } else {
              message.success("Tạo kèo thành công! (Lỗi tạo phòng chat)");
            }
          } catch (chatError) {
            console.error('Error creating chat:', chatError);
            message.success("Tạo kèo thành công! (Lỗi tạo phòng chat)");
          }
        } else {
          message.success("Tạo thành công!");
        }
      }

      // Reset form and close modal
      form.resetFields();
      setModalVisible(false);
      loadBettingOdds();
    } catch (error) {
      message.error("Có lỗi xảy ra");
      console.error("Submit error:", error);
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const { error } = await bettingService.deleteBettingOdds(id);
      if (error) {
        message.error(`Lỗi xóa: ${error.message}`);
        return;
      }
      message.success("Xóa thành công!");
      loadBettingOdds();
    } catch (error) {
      message.error("Có lỗi xảy ra");
      console.error("Delete error:", error);
    }
  };

  // Handle send
  const handleSend = async (id: string) => {
    setSendingIds(prev => new Set(prev).add(id));
    try {
      const { error } = await bettingService.sendBettingOdds(id);
      if (error) {
        message.error(`Lỗi gửi: ${error.message}`);
        return;
      }
      message.success("Gửi thành công!");
      loadBettingOdds();
    } catch (error) {
      message.error("Có lỗi xảy ra khi gửi");
      console.error("Send error:", error);
    } finally {
      setSendingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  // Handle open modal for create
  const handleCreate = () => {
    setEditingOdds(null);
    form.resetFields();
    setModalVisible(true);
  };

  // Handle open modal for edit
  const handleEdit = (record: BettingOdds) => {
    setEditingOdds(record);

    // Determine expert value for form
    let expertValue = '';

    if (typeof record.expert === 'object' && record.expert?.id) {
      // If it's an admin object, use the admin ID for selection
      expertValue = record.expert.id;
    } else if (typeof record.expert === 'string') {
      // If it's a string, use the string value
      expertValue = record.expert;
    }

    form.setFieldsValue({
      expert: expertValue,
      tournament: record.tournament,
      team_name: record.team_name,
      closing_bet: record.closing_bet,
      saying: record.saying,
      status: record.status,
      type: record.type,
    });
    setModalVisible(true);
  };

  // Handle close modal
  const handleCloseModal = () => {
    setModalVisible(false);
    setEditingOdds(null);
    form.resetFields();
  };

  // Handle quick status update
  const handleQuickStatusUpdate = async (id: string, status: 'win' | 'lose') => {
    try {
      setUpdatingIds(prev => new Set(prev).add(id));

      const updateData: UpdateBettingOddsData = { status };
      const { error } = await bettingService.updateBettingOdds(id, updateData);

      if (error) {
        message.error('Cập nhật trạng thái thất bại');
      } else {
        message.success(`Đã cập nhật trạng thái: ${status === 'win' ? 'Thắng' : 'Thua'}`);
        loadBettingOdds(); // Reload data
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi cập nhật trạng thái');
    } finally {
      setUpdatingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  // Handle chat with admin
  const handleChatWithAdmin = async (adminId: string) => {
    const selectedAdmin = adminList.find(admin => admin.id === adminId);
    if (selectedAdmin) {
      try {
        // Create chat data
        const chatData: CreateChatData = {
          name: `Chat với ${selectedAdmin.full_name}`,
          type: 'direct',
          members: [adminId]
        };

        // Create or get existing chat
        const response = await chatService.createChat(chatData, adminId);

        if (response.error) {
          message.error(`Lỗi tạo chat: ${response.error.message}`);
          return;
        }

        // Navigate to chat
        if (response.data?.id) {
          window.open(`/dashboard/chat?room=${response.data.id}`, '_blank');
        } else {
          // Fallback to public chat link
          window.open(`/chat/join?userId=${adminId}`, '_blank');
        }

        message.success('Đã tạo phòng chat thành công!');
      } catch (error) {
        console.error('Error creating chat:', error);
        message.error('Không thể tạo chat với admin này');
      }
    } else {
      message.warning('Không tìm thấy thông tin admin này');
    }
  };

  // Table columns
  const columns = [
    {
      title: "Loại kèo",
      dataIndex: "type",
      key: "type",
      width: 120,
      render: (type: string) => (
        <Tag color="blue">{type || "Chưa có"}</Tag>
      ),
    },
    {
      title: "Expert phụ trách",
      dataIndex: "expert",
      key: "expert_info",
      width: 200,
      render: (expert: string | ExpertInfo) => {
        if (typeof expert === 'object' && expert?.id) {
          return (
            <div>
              <Tag color="green" style={{ cursor: 'pointer' }}
                onClick={() => handleChatWithAdmin(expert.id)}>
                {expert.name}
              </Tag>
              {expert.email && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>{expert.email}</div>
              )}
              {expert.role && (
                <div style={{ fontSize: '11px', color: '#999' }}>Role: {expert.role}</div>
              )}
            </div>
          );
        } else if (typeof expert === 'string') {
          return <Tag color="blue">{expert}</Tag>;
        }
        return <Tag color="default">Chưa chọn</Tag>;
      },
    },
    {
      title: "Giải đấu",
      dataIndex: "tournament",
      key: "tournament",
      width: 150,
    },
    {
      title: "Đội bóng",
      dataIndex: "team_name",
      key: "team_name",
      width: 120,
    },
    {
      title: "Kèo đóng",
      dataIndex: "closing_bet",
      key: "closing_bet",
      width: 100,
    },
    {
      title: "Lời nói",
      dataIndex: "saying",
      key: "saying",
      width: 200,
      render: (text: string) => (
        <Text ellipsis={{ tooltip: text }} style={{ maxWidth: 180 }}>
          {text}
        </Text>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 180,
      render: (status: "win" | "lose" | null, record: BettingOdds) => {
        if (status === "win") {
          return <Tag color="green" icon={<CheckCircleOutlined />}>Thắng</Tag>;
        }
        if (status === "lose") {
          return <Tag color="red" icon={<CloseCircleOutlined />}>Thua</Tag>;
        }
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            <Button
              size="small"
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => handleQuickStatusUpdate(record.id, 'win')}
              loading={updatingIds.has(record.id)}
            >
              Thắng
            </Button>
            <Button
              size="small"
              danger
              icon={<CloseCircleOutlined />}
              onClick={() => handleQuickStatusUpdate(record.id, 'lose')}
              loading={updatingIds.has(record.id)}
            >
              Thua
            </Button>
          </div>
        );
      },
    },
    {
      title: "Đã gửi",
      dataIndex: "sent_at",
      key: "sent_at",
      width: 100,
      render: (sent_at: string | null) => (
        <Tag color={sent_at ? "blue" : "default"}>
          {sent_at ? "Đã gửi" : "Chưa gửi"}
        </Tag>
      ),
    },
    {
      title: "Thao tác",
      key: "actions",
      width: 200,
      render: (_: any, record: BettingOdds) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Sửa
          </Button>
          <Button
            type="default"
            size="small"
            icon={<SendOutlined />}
            loading={sendingIds.has(record.id)}
            onClick={() => handleSend(record.id)}
            disabled={
              record.sent_at !== null || // Đã gửi thì disable
              (record.status === "lose" && record.sent_at === null) // Thua và chưa gửi thì disable
            }
          >
            Gửi
          </Button>
          <Popconfirm
            title="Xác nhận xóa?"
            description="Bạn có chắc chắn muốn xóa kèo này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Xóa"
            cancelText="Hủy"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              Quản lý Kèo Cược
            </Title>
          </Col>
          <Col>
            <Space>
              <Button
                type="default"
                icon={<ImportOutlined />}
                onClick={() => setQuickImportVisible(true)}
              >
                Nhập nhanh
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
              >
                Tạo kèo mới
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={odds}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} kèo`,
          }}
        />
      </Card>

      {/* Main Form Modal */}
      <Modal
        title={editingOdds ? "Chỉnh sửa kèo" : "Tạo kèo mới"}
        open={modalVisible}
        onCancel={handleCloseModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="type"
            label="Loại kèo"
            rules={[
              { required: true, message: "Vui lòng chọn hoặc nhập loại kèo!" },
            ]}
            initialValue="KÈO VIP"
          >
            <Select
              placeholder="Chọn hoặc nhập loại kèo mới (nhấn Enter để thêm)"
              allowClear
              showSearch
              options={bettingTypes}
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              onInputKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const value = e.currentTarget.value;
                  const lowercaseValue = value.toLowerCase();
                  if (
                    value &&
                    !bettingTypes.some(
                      (type) => type.label.toLowerCase() === lowercaseValue
                    )
                  ) {
                    setBettingTypes([...bettingTypes, { value, label: value }]);
                    // Set the new value to the form
                    form.setFieldValue('type', value);
                  }
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="expert"
            label="Expert / Admin phụ trách"
            rules={[
              { required: true, message: "Vui lòng chọn expert hoặc nhập tên!" },
            ]}
          >
            <Select
              placeholder="Chọn admin hoặc nhập tên expert tùy chỉnh"
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option: any) => {
                const children = option?.children;
                if (typeof children === 'string') {
                  return children.toLowerCase().includes(input.toLowerCase());
                }
                return false;
              }}>
              {adminList.map(admin => (
                <Select.Option key={admin.id} value={admin.id}>
                  {admin.full_name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="tournament"
            label="Giải đấu"
            rules={[
              { required: true, message: "Vui lòng nhập tên giải đấu" },
              { max: 100, message: "Tên giải đấu không được quá 100 ký tự" },
            ]}
          >
            <Input placeholder="Ví dụ: Premier League, Champions League" />
          </Form.Item>

          <Form.Item
            name="team_name"
            label="Đội bóng"
            rules={[
              { required: true, message: "Vui lòng nhập tên đội bóng" },
              { max: 100, message: "Tên đội bóng không được quá 100 ký tự" },
            ]}
          >
            <Input placeholder="Ví dụ: Manchester United vs Liverpool" />
          </Form.Item>

          <Form.Item
            name="closing_bet"
            label="Kèo đóng"
            rules={[
              { required: true, message: "Vui lòng nhập kèo đóng" },
              { max: 50, message: "Kèo đóng không được quá 50 ký tự" },
            ]}
          >
            <Input placeholder="Ví dụ: 1.5, Over 2.5, 1X2" />
          </Form.Item>

          <Form.Item
            name="saying"
            label="Lời nói"
            rules={[
              { required: true, message: "Vui lòng nhập lời nói" },
              { max: 500, message: "Lời nói không được quá 500 ký tự" },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="Nhập phân tích và dự đoán của bạn..."
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="Trạng thái"
          >
            <Select placeholder="Chọn trạng thái" allowClear>
              <Select.Option value="win">Thắng</Select.Option>
              <Select.Option value="lose">Thua</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button onClick={handleCloseModal}>
                Hủy
              </Button>
              <Button type="primary" htmlType="submit">
                {editingOdds ? "Cập nhật" : "Tạo mới"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Quick Import Modal */}
      <Modal
        title="Nhập nhanh dữ liệu trận đấu"
        open={quickImportVisible}
        onCancel={() => {
          setQuickImportVisible(false);
          quickImportForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={quickImportForm}
          layout="vertical"
          onFinish={handleQuickImport}
        >
          <Form.Item
            name="importData"
            label="Dán dữ liệu trận đấu"
            rules={[
              { required: true, message: "Vui lòng nhập dữ liệu trận đấu!" },
            ]}
          >
            <TextArea
              rows={8}
              placeholder={`Dán dữ liệu theo định dạng như sau:

	GIẢI NGOẠI HẠNG ANH	
1H
34'
Newcastle United
Arsenal

---------------------

	GIẢI LALIGA TÂY BAN NHA	
2H
42'
Elche
Celta Vigo`}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button onClick={() => {
                setQuickImportVisible(false);
                quickImportForm.resetFields();
              }}>
                Hủy
              </Button>
              <Button type="primary" htmlType="submit">
                Phân tích và nhập
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}