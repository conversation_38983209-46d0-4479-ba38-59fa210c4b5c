"use client";

import { useEffect, useState } from "react";
import { useSupabaseClient } from "@/contexts/SupabaseContext";
import type { AuthProfile, LoginCredentials, RegisterData } from "@/types";
import type { User } from "@supabase/supabase-js";

export function useAuthService() {
  const supabase = useSupabaseClient();
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<AuthProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!supabase) {
      return; // Chờ supabase client được khởi tạo
    }

    const initializeAuth = async () => {
      try {
        const {
          data: { user },
          error,
        } = await supabase.auth.getUser();
        
        if (error) {
          console.error("Error getting current user:", error);
          setUser(null);
          setProfile(null);
          return;
        }

        setUser(user);

        if (user) {
          const { data: profileData, error: profileError } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", user.id)
            .single();
            
          if (profileError) {
            console.error("Error getting profile:", profileError);
            setProfile(null);
            return;
          }
          setProfile(profileData);
        } else {
          setProfile(null);
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        setUser(null);
        setProfile(null);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null);

      if (session?.user) {
        const { data: profileData, error } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", session.user.id)
          .single();
          
        if (error) {
          console.error("Error getting profile on auth change:", error);
          return;
        }
        setProfile(profileData);
      } else {
        setProfile(null);
      }

      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, [supabase]);

  const signIn = async (credentials: LoginCredentials) => {
    if (!supabase) {
      return {
        user: null,
        error: new Error("Supabase client not initialized"),
      };
    }
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  };

  const signUp = async (registerData: RegisterData) => {
    if (!supabase) {
      return {
        user: null,
        error: new Error("Supabase client not initialized"),
      };
    }
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email: registerData.email,
        password: registerData.password,
        options: {
          emailRedirectTo:
            process.env.NEXT_PUBLIC_DEV_SUPABASE_REDIRECT_URL ||
            `${window.location.origin}/dashboard`,
          data: {
            full_name: registerData.fullName,
            role: registerData.role,
          },
        },
      });

      return {
        user: data.user,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  };

  const signInWithGoogle = async () => {
    if (!supabase) {
      return {
        error: new Error("Supabase client not initialized"),
      };
    }
    
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo:
            process.env.NEXT_PUBLIC_DEV_SUPABASE_REDIRECT_URL ||
            `${window.location.origin}/dashboard`,
        },
      });

      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  };

  const signOut = async () => {
    if (!supabase) {
      return {
        error: new Error("Supabase client not initialized"),
      };
    }
    
    try {
      const { error } = await supabase.auth.signOut();
      return {
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  };

  const updateProfile = async (
    userId: string,
    updates: Partial<AuthProfile>
  ) => {
    if (!supabase) {
      return {
        profile: null,
        error: new Error("Supabase client not initialized"),
      };
    }
    
    try {
      const { data, error } = await supabase
        .from("profiles")
        .update(updates)
        .eq("id", userId)
        .select()
        .single();

      if (!error && data) {
        setProfile(data);
      }
      
      return {
        profile: data,
        error: error ? new Error(error.message) : null,
      };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    }
  };

  return {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    updateProfile,
  };
}
