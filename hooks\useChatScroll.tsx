"use client";

import { useEffect, useRef } from 'react';

interface UseChatScrollProps {
  messages: any[];
  isLoading?: boolean;
}

export function useChatScroll({ messages, isLoading = false }: UseChatScrollProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const shouldScrollRef = useRef(true);

  const scrollToBottom = (smooth = true) => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end'
      });
    }
  };

  // Check if user is near bottom of scroll
  const isNearBottom = () => {
    if (!containerRef.current) return true;
    
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    const threshold = 100; // pixels from bottom
    
    return scrollHeight - scrollTop - clientHeight < threshold;
  };

  // Handle scroll events to determine if we should auto-scroll
  const handleScroll = () => {
    shouldScrollRef.current = isNearBottom();
  };

  // Auto-scroll when new messages arrive (only if user is near bottom)
  useEffect(() => {
    if (messages.length > 0 && shouldScrollRef.current && !isLoading) {
      // Small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [messages, isLoading]);

  // Scroll to bottom on initial load
  useEffect(() => {
    if (!isLoading && messages.length > 0) {
      setTimeout(() => {
        scrollToBottom(false); // No smooth scroll on initial load
      }, 100);
    }
  }, [isLoading]);

  return {
    messagesEndRef,
    containerRef,
    scrollToBottom,
    handleScroll,
    isNearBottom
  };
}