import styled from "styled-components";

export const StatusDot = styled.div<{
  $isOnline: boolean;
  $size?: "small" | "medium" | "large";
}>`
  width: ${({ $size }) => {
    switch ($size) {
      case "small":
        return "8px";
      case "large":
        return "16px";
      default:
        return "12px";
    }
  }};
  height: ${({ $size }) => {
    switch ($size) {
      case "small":
        return "8px";
      case "large":
        return "16px";
      default:
        return "12px";
    }
  }};
  border-radius: 50%;
  background-color: ${({ $isOnline }) => ($isOnline ? "#52c41a" : "#d9d9d9")};
  border: 2px solid #ffffff;
  position: absolute;
  bottom: 0;
  right: 0;

  .dark & {
    border-color: #141414;
  }
`;

export const StatusContainer = styled.div`
  position: relative;
  display: inline-block;
`;

export const StatusText = styled.span<{ $isOnline: boolean }>`
  color: ${({ $isOnline }) => ($isOnline ? "#52c41a" : "#999")};
  font-size: 12px;
  font-weight: 500;
`;
