CREATE OR <PERSON><PERSON>LACE FUNCTION public.get_user_rooms_with_messages_v5(
    user_uuid UUID
)
RETURNS TABLE (id UUID, name TEXT, type TEXT, updated_at TIMESTAMP WITH TIME ZONE,
    messages JSON<PERSON>,
    other_user_id UUID,
    other_user_name TEXT,
    other_user_avatar TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cr.id, 
        cr.name, 
        cr.type, 
        cr.updated_at,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'content', m.content, 
                    'created_at', m.created_at
                )
            ) FILTER (WHERE m.id IS NOT NULL), 
            '[]'::jsonb
        ) AS messages,
        CASE 
            WHEN cr.type = 'direct' THEN (
                SELECT p.id 
                FROM public.chat_room_members crm2
                JOIN public.profiles p ON p.id = crm2.user_id
                WHERE crm2.room_id = cr.id 
                  AND crm2.user_id != user_uuid
                LIMIT 1
            )
            ELSE NULL
        END AS other_user_id,
        CASE 
            WHEN cr.type = 'direct' THEN (
                SELECT p.full_name 
                FROM public.chat_room_members crm2
                JOIN public.profiles p ON p.id = crm2.user_id
                WHERE crm2.room_id = cr.id 
                  AND crm2.user_id != user_uuid
                LIMIT 1
            )
            ELSE NULL
        END AS other_user_name,
        CASE 
            WHEN cr.type = 'direct' THEN (
                SELECT p.avatar_url 
                FROM public.chat_room_members crm2
                JOIN public.profiles p ON p.id = crm2.user_id
                WHERE crm2.room_id = cr.id 
                  AND crm2.user_id != user_uuid
                LIMIT 1
            )
            ELSE NULL
        END AS other_user_avatar
    FROM public.chat_rooms cr
    LEFT JOIN public.messages m ON cr.id = m.room_id
    WHERE cr.type = 'general' 
       OR (cr.type IN ('private', 'direct', 'group') AND EXISTS (
            SELECT 1 
            FROM public.chat_room_members crm 
            WHERE crm.room_id = cr.id 
              AND crm.user_id = user_uuid
        ))
    GROUP BY cr.id, cr.name, cr.type, cr.updated_at
    ORDER BY cr.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;