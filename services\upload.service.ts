// ========================================
// UPLOAD SERVICE - Image/Video Upload Operations
// ========================================

import type { UploadedFile, UploadResponse } from "@/types/forum.types";

class UploadService {
  private baseUrl: string;

  constructor() {
    this.baseUrl =
      "https://service.ngoaihangtv.live/storage/v1/object/public/tenan/forums";
  }

  /**
   * Upload file to external storage service
   */
  async uploadFile(
    file: File,
    folder: string = "images"
  ): Promise<UploadResponse> {
    try {
      // Generate unique filename
      const timestamp = Date.now();
      const fileExtension = file.name.split(".").pop();
      const filename = `${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`;

      // Create full URL for the file
      const fileUrl = `${this.baseUrl}/${folder}/${filename}`;

      // In a real implementation, you would:
      // 1. Get presigned URL from backend
      // 2. Upload file directly to storage service
      // 3. Return the public URL

      // For now, we'll simulate the upload process
      const formData = new FormData();
      formData.append("file", file);
      formData.append("filename", filename);
      formData.append("folder", folder);

      // Simulate API call to upload service
      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Upload failed");
      }

      const result = await response.json();

      return {
        success: true,
        data: {
          url: fileUrl,
          filename,
          size: file.size,
          type: file.type,
        },
      };
    } catch (error) {
      console.error("Error uploading file:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Upload failed",
      };
    }
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(
    files: File[],
    folder: string = "images"
  ): Promise<UploadResponse[]> {
    const uploadPromises = files.map(file => this.uploadFile(file, folder));
    return Promise.all(uploadPromises);
  }

  /**
   * Upload image file
   */
  async uploadImage(file: File): Promise<UploadResponse> {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      return {
        success: false,
        error: "File must be an image",
      };
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return {
        success: false,
        error: "Image size must be less than 5MB",
      };
    }

    return this.uploadFile(file, "images");
  }

  /**
   * Upload video file
   */
  async uploadVideo(file: File): Promise<UploadResponse> {
    // Validate file type
    if (!file.type.startsWith("video/")) {
      return {
        success: false,
        error: "File must be a video",
      };
    }

    // Validate file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      return {
        success: false,
        error: "Video size must be less than 50MB",
      };
    }

    return this.uploadFile(file, "videos");
  }

  /**
   * Delete uploaded file
   */
  async deleteFile(url: string): Promise<boolean> {
    try {
      // Extract filename from URL
      const urlParts = url.split("/");
      const filename = urlParts[urlParts.length - 1];

      // In a real implementation, you would call the delete API
      const response = await fetch(`/api/upload/delete`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ filename }),
      });

      return response.ok;
    } catch (error) {
      console.error("Error deleting file:", error);
      return false;
    }
  }

  /**
   * Get file info from URL
   */
  async getFileInfo(url: string): Promise<UploadedFile | null> {
    try {
      // In a real implementation, you would fetch file metadata
      // For now, we'll return basic info
      return {
        url,
        filename: url.split("/").pop() || "",
        size: 0,
        type: "unknown",
      };
    } catch (error) {
      console.error("Error getting file info:", error);
      return null;
    }
  }

  /**
   * Validate image file
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith("image/")) {
      return { valid: false, error: "File phải là hình ảnh" };
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return {
        valid: false,
        error: "Kích thước hình ảnh không được vượt quá 5MB",
      };
    }

    // Check file extension
    const allowedExtensions = ["jpg", "jpeg", "png", "gif", "webp"];
    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return { valid: false, error: "Định dạng hình ảnh không được hỗ trợ" };
    }

    return { valid: true };
  }

  /**
   * Validate video file
   */
  validateVideoFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith("video/")) {
      return { valid: false, error: "File phải là video" };
    }

    // Check file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      return {
        valid: false,
        error: "Kích thước video không được vượt quá 50MB",
      };
    }

    // Check file extension
    const allowedExtensions = ["mp4", "webm", "ogg", "mov"];
    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return { valid: false, error: "Định dạng video không được hỗ trợ" };
    }

    return { valid: true };
  }

  /**
   * Generate thumbnail URL for video
   */
  generateVideoThumbnail(videoUrl: string): string {
    // In a real implementation, you would generate thumbnail on the server
    // For now, we'll return the original URL
    return videoUrl;
  }

  /**
   * Compress image before upload
   */
  async compressImage(
    file: File,
    maxWidth: number = 1920,
    quality: number = 0.8
  ): Promise<File> {
    return new Promise(resolve => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d")!;
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;

        // Draw and compress
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        canvas.toBlob(
          blob => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: "image/jpeg",
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              resolve(file);
            }
          },
          "image/jpeg",
          quality
        );
      };

      img.src = URL.createObjectURL(file);
    });
  }
}

// Export default instance
export const uploadService = new UploadService();
export default uploadService;
