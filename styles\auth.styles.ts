import styled from "styled-components";
import { Card, Form, Button } from "antd";

export const AuthContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

export const AuthCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 12px;

  .ant-card-head {
    text-align: center;
    border-bottom: none;
    padding-bottom: 0;
  }

  .ant-card-head-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
  }
`;

export const RegisterCard = styled(Card)`
  width: 100%;
  max-width: 450px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 12px;

  .ant-card-head {
    text-align: center;
    border-bottom: none;
    padding-bottom: 0;
  }

  .ant-card-head-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
  }
`;

export const StyledForm = styled(Form)`
  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-input-affix-wrapper,
  .ant-select-selector {
    height: 45px;
    border-radius: 8px;
    border: 1px solid #d9d9d9;

    &:hover,
    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }
  }

  .ant-select-selector {
    display: flex;
    align-items: center;
  }
`;

export const AuthButton = styled(Button)`
  height: 45px;
  border-radius: 8px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
`;

export const GoogleButton = styled(Button)`
  height: 45px;
  border-radius: 8px;
  font-weight: 600;
  border: 1px solid #d9d9d9;

  &:hover {
    border-color: #667eea;
    color: #667eea;
  }
`;
