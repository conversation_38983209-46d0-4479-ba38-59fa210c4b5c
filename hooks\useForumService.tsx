"use client";

import { useState, useCallback } from "react";
import { forumService } from "@/services/forum.service";
import type {
  ForumPost,
  ForumCategory,
  ForumComment,
  CreateForumPostDto,
  UpdateForumPostDto,
  CreateForumCommentDto,
  ForumPostFilters,
  ForumCommentFilters,
  PaginationOptions,
  ForumStats,
} from "@/types/forum.types";

export function useForumService() {
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [comments, setComments] = useState<ForumComment[]>([]);
  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [stats, setStats] = useState<ForumStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ========================================
  // POST OPERATIONS
  // ========================================

  const fetchPosts = useCallback(
    async (
      filters: ForumPostFilters = {},
      pagination: PaginationOptions = {}
    ) => {
      setLoading(true);
      setError(null);
      try {
        const response = await forumService.getPosts(filters, pagination);
        setPosts(response.data);
        return response;
      } catch (err) {
        const errorMessage = "Không thể tải danh sách bài viết";
        setError(errorMessage);
        console.error("Error fetching posts:", err);
        return { data: [], count: 0, has_more: false };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const getPost = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const post = await forumService.getPost(id);
      return post;
    } catch (err) {
      const errorMessage = "Không thể tải bài viết";
      setError(errorMessage);
      console.error("Error fetching post:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getPostBySlug = useCallback(async (slug: string) => {
    setLoading(true);
    setError(null);
    try {
      const post = await forumService.getPostBySlug(slug);
      return post;
    } catch (err) {
      const errorMessage = "Không thể tải bài viết";
      setError(errorMessage);
      console.error("Error fetching post by slug:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const createPost = useCallback(async (postData: CreateForumPostDto) => {
    setLoading(true);
    setError(null);
    try {
      const newPost = await forumService.createPost(postData);
      if (newPost) {
        setPosts(prev => [newPost, ...prev]);
      }
      return newPost;
    } catch (err) {
      const errorMessage = "Không thể tạo bài viết";
      setError(errorMessage);
      console.error("Error creating post:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePost = useCallback(async (postData: UpdateForumPostDto) => {
    setLoading(true);
    setError(null);
    try {
      const updatedPost = await forumService.updatePost(postData);
      if (updatedPost) {
        setPosts(prev =>
          prev.map(post => (post.id === updatedPost.id ? updatedPost : post))
        );
      }
      return updatedPost;
    } catch (err) {
      const errorMessage = "Không thể cập nhật bài viết";
      setError(errorMessage);
      console.error("Error updating post:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deletePost = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const success = await forumService.deletePost(id);
      if (success) {
        setPosts(prev => prev.filter(post => post.id !== id));
      }
      return success;
    } catch (err) {
      const errorMessage = "Không thể xóa bài viết";
      setError(errorMessage);
      console.error("Error deleting post:", err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const approvePost = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const approvedPost = await forumService.approvePost(id);
      if (approvedPost) {
        setPosts(prev =>
          prev.map(post => (post.id === approvedPost.id ? approvedPost : post))
        );
      }
      return approvedPost;
    } catch (err) {
      const errorMessage = "Không thể duyệt bài viết";
      setError(errorMessage);
      console.error("Error approving post:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const rejectPost = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const rejectedPost = await forumService.rejectPost(id);
      if (rejectedPost) {
        setPosts(prev =>
          prev.map(post => (post.id === rejectedPost.id ? rejectedPost : post))
        );
      }
      return rejectedPost;
    } catch (err) {
      const errorMessage = "Không thể từ chối bài viết";
      setError(errorMessage);
      console.error("Error rejecting post:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const togglePinPost = useCallback(async (id: string, isPinned: boolean) => {
    setLoading(true);
    setError(null);
    try {
      const pinnedPost = await forumService.togglePinPost(id, isPinned);
      if (pinnedPost) {
        setPosts(prev =>
          prev.map(post => (post.id === pinnedPost.id ? pinnedPost : post))
        );
      }
      return pinnedPost;
    } catch (err) {
      const errorMessage = "Không thể ghim bài viết";
      setError(errorMessage);
      console.error("Error toggling pin post:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // ========================================
  // COMMENT OPERATIONS
  // ========================================

  const fetchComments = useCallback(
    async (
      filters: ForumCommentFilters = {},
      pagination: PaginationOptions = {}
    ) => {
      setLoading(true);
      setError(null);
      try {
        const response = await forumService.getComments(filters, pagination);
        setComments(response.data);
        return response;
      } catch (err) {
        const errorMessage = "Không thể tải bình luận";
        setError(errorMessage);
        console.error("Error fetching comments:", err);
        return { data: [], count: 0, has_more: false };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const createComment = useCallback(
    async (commentData: CreateForumCommentDto) => {
      setLoading(true);
      setError(null);
      try {
        const newComment = await forumService.createComment(commentData);
        if (newComment) {
          setComments(prev => [...prev, newComment]);
        }
        return newComment;
      } catch (err) {
        const errorMessage = "Không thể tạo bình luận";
        setError(errorMessage);
        console.error("Error creating comment:", err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const updateComment = useCallback(async (id: string, content: string) => {
    setLoading(true);
    setError(null);
    try {
      const updatedComment = await forumService.updateComment({ id, content });
      if (updatedComment) {
        setComments(prev =>
          prev.map(comment =>
            comment.id === updatedComment.id ? updatedComment : comment
          )
        );
      }
      return updatedComment;
    } catch (err) {
      const errorMessage = "Không thể cập nhật bình luận";
      setError(errorMessage);
      console.error("Error updating comment:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteComment = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const success = await forumService.deleteComment(id);
      if (success) {
        setComments(prev => prev.filter(comment => comment.id !== id));
      }
      return success;
    } catch (err) {
      const errorMessage = "Không thể xóa bình luận";
      setError(errorMessage);
      console.error("Error deleting comment:", err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // ========================================
  // CATEGORY OPERATIONS
  // ========================================

  const fetchCategories = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const categoriesList = await forumService.getCategories();
      setCategories(categoriesList);
      return categoriesList;
    } catch (err) {
      const errorMessage = "Không thể tải danh mục";
      setError(errorMessage);
      console.error("Error fetching categories:", err);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // ========================================
  // STATS OPERATIONS
  // ========================================

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const forumStats = await forumService.getForumStats();
      setStats(forumStats);
      return forumStats;
    } catch (err) {
      const errorMessage = "Không thể tải thống kê";
      setError(errorMessage);
      console.error("Error fetching stats:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // ========================================
  // LIKE OPERATIONS
  // ========================================

  const togglePostLike = useCallback(async (postId: string, userId: string) => {
    try {
      return await forumService.togglePostLike(postId, userId);
    } catch (err) {
      console.error("Error toggling post like:", err);
      return false;
    }
  }, []);

  const toggleCommentLike = useCallback(
    async (commentId: string, userId: string) => {
      try {
        return await forumService.toggleCommentLike(commentId, userId);
      } catch (err) {
        console.error("Error toggling comment like:", err);
        return false;
      }
    },
    []
  );

  return {
    // State
    posts,
    comments,
    categories,
    stats,
    loading,
    error,

    // Post operations
    fetchPosts,
    getPost,
    getPostBySlug,
    createPost,
    updatePost,
    deletePost,
    approvePost,
    rejectPost,
    togglePinPost,

    // Comment operations
    fetchComments,
    createComment,
    updateComment,
    deleteComment,

    // Category operations
    fetchCategories,

    // Stats operations
    fetchStats,

    // Like operations
    togglePostLike,
    toggleCommentLike,
  };
}
