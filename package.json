{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start -p 3003", "format": "prettier --write .", "format:check": "prettier --check .", "format:staged": "prettier --write --list-different", "lint:fix": "next lint --fix"}, "dependencies": {"@ant-design/icons": "latest", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@uiw/react-md-editor": "^4.0.8", "@vercel/analytics": "latest", "@vercel/blob": "latest", "antd": "latest", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "dayjs": "^1.11.18", "geist": "^1.4.2", "next": "15.2.4", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "quill": "^2.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "react-quill-new": "^3.6.0", "styled-components": "latest"}, "devDependencies": {"@types/node": "^22", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "babel-plugin-styled-components": "^2.1.4", "eslint-config-next": "^15.5.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-unused-imports": "^4.2.0", "postcss": "^8.5", "prettier": "^3.6.2", "typescript": "^5"}}