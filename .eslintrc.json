{"extends": ["next/core-web-vitals", "prettier"], "plugins": ["prettier", "@typescript-eslint", "unused-imports"], "rules": {"prettier/prettier": "error", "react-hooks/exhaustive-deps": "warn", "@next/next/no-img-element": "warn", "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}]}}