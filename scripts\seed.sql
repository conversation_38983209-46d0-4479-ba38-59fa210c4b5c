-- ========================================
-- QCLG CHAT SYSTEM - COMPLETE DATABASE SEED DATA
-- ========================================
-- This file contains sample data for testing
-- Run this file after the migration to add test users and data

-- ========================================
-- 1. INSERT SAMPLE USERS
-- ========================================

-- Insert admin and BLV users (ignore if exists)
INSERT INTO auth.users (
  id,
  instance_id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES 
-- Admin 1
(
  '11111111-1111-1111-1111-111111111111',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('admin123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Admin QCLG"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
),
-- Admin 2
(
  '*************-2222-2222-************',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('admin123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Admin Phụ QCLG"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
),
-- BLV 1
(
  '*************-3333-3333-************',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('blv123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Bình Luận Viên 1"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
),
-- BLV 2
(
  '*************-4444-4444-************',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('blv123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Bình Luận Viên 2"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
),
-- BLV 3
(
  '*************-5555-5555-************',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('blv123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Bình Luận Viên 3"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
),
-- Member 1
(
  '*************-6666-6666-************',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('member123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Thành Viên 1"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
),
-- Member 2
(
  '77777777-7777-7777-7777-777777777777',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('member123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Thành Viên 2"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
),
-- Member 3
(
  '88888888-8888-8888-8888-888888888888',
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('member123', gen_salt('bf')),
  NOW(),
  NULL,
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Thành Viên 3"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 2. INSERT USER PROFILES
-- ========================================

INSERT INTO public.profiles (
  id,
  email,
  full_name,
  role,
  avatar_url,
  created_at,
  updated_at
) VALUES 
-- Admin profiles
(
  '11111111-1111-1111-1111-111111111111',
  '<EMAIL>',
  'Admin QCLG',
  'admin',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=admin1',
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  '<EMAIL>',
  'Admin Phụ QCLG',
  'admin',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=admin2',
  NOW(),
  NOW()
),
-- BLV profiles
(
  '*************-3333-3333-************',
  '<EMAIL>',
  'Bình Luận Viên 1',
  'blv',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv1',
  NOW(),
  NOW()
),
(
  '*************-4444-4444-************',
  '<EMAIL>',
  'Bình Luận Viên 2',
  'blv',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv2',
  NOW(),
  NOW()
),
(
  '*************-5555-5555-************',
  '<EMAIL>',
  'Bình Luận Viên 3',
  'blv',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv3',
  NOW(),
  NOW()
),
-- Member profiles
(
  '*************-6666-6666-************',
  '<EMAIL>',
  'Thành Viên 1',
  'member',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=member1',
  NOW(),
  NOW()
),
(
  '77777777-7777-7777-7777-777777777777',
  '<EMAIL>',
  'Thành Viên 2',
  'member',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=member2',
  NOW(),
  NOW()
),
(
  '88888888-8888-8888-8888-888888888888',
  '<EMAIL>',
  'Thành Viên 3',
  'member',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=member3',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  role = EXCLUDED.role,
  avatar_url = EXCLUDED.avatar_url,
  updated_at = NOW();

-- ========================================
-- 3. CREATE GENERAL ROOM
-- ========================================

-- Create general room
INSERT INTO public.chat_rooms (
  id,
  name,
  type,
  created_by,
  created_at
) VALUES (
  '00000000-0000-0000-0000-000000000001',
  'Nhóm Chat Tổng',
  'general',
  '11111111-1111-1111-1111-111111111111',
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 4. ADD USERS TO GENERAL ROOM
-- ========================================

INSERT INTO public.chat_room_members (
  room_id,
  user_id,
  joined_at
) 
SELECT 
  cr.id,
  u.id,
  NOW()
FROM public.chat_rooms cr
CROSS JOIN auth.users u
WHERE cr.type = 'general'
AND u.email IN (
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ON CONFLICT (room_id, user_id) DO NOTHING;

-- ========================================
-- 5. INSERT SAMPLE MESSAGES IN GENERAL ROOM
-- ========================================

INSERT INTO public.messages (
  room_id,
  sender_id,
  content,
  message_type,
  created_at
)
SELECT 
  cr.id,
  u.id,
  msg.content,
  'text',
  msg.created_at
FROM public.chat_rooms cr
CROSS JOIN auth.users u
CROSS JOIN (
  VALUES 
    ('Chào mừng mọi người đến với hệ thống chat QCLG! 👋', NOW() - INTERVAL '2 hours'),
    ('Hệ thống đã sẵn sàng để sử dụng. Các BLV có thể bắt đầu bình luận!', NOW() - INTERVAL '1 hour 30 minutes'),
    ('Admin sẽ theo dõi và hỗ trợ các BLV trong quá trình làm việc.', NOW() - INTERVAL '1 hour'),
    ('Chúc mọi người làm việc hiệu quả! 🚀', NOW() - INTERVAL '30 minutes')
) AS msg(content, created_at)
WHERE cr.type = 'general'
AND u.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- ========================================
-- 6. SET USER PRESENCE
-- ========================================

INSERT INTO public.user_presence (
  user_id,
  is_online,
  last_seen,
  updated_at
)
SELECT 
  u.id,
  true,
  NOW(),
  NOW()
FROM auth.users u
WHERE u.email IN (
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ON CONFLICT (user_id) DO UPDATE SET
  is_online = true,
  last_seen = NOW(),
  updated_at = NOW();

-- ========================================
-- 7. CREATE PRIVATE ROOMS
-- ========================================

INSERT INTO public.chat_rooms (
  id,
  name,
  type,
  created_by,
  created_at
) VALUES 
(
  '*************-6666-6666-************',
  'Phòng Admin',
  'private',
  '11111111-1111-1111-1111-111111111111',
  NOW()
),
(
  '77777777-7777-7777-7777-777777777777',
  'Phòng BLV',
  'private',
  '*************-3333-3333-************',
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 8. ADD MEMBERS TO PRIVATE ROOMS
-- ========================================

INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
VALUES 
-- Admin room - only admins
('*************-6666-6666-************', '11111111-1111-1111-1111-111111111111', NOW()),
('*************-6666-6666-************', '*************-2222-2222-************', NOW()),
-- BLV room - only BLVs
('77777777-7777-7777-7777-777777777777', '*************-3333-3333-************', NOW()),
('77777777-7777-7777-7777-777777777777', '*************-4444-4444-************', NOW()),
('77777777-7777-7777-7777-777777777777', '*************-5555-5555-************', NOW())
ON CONFLICT (room_id, user_id) DO NOTHING;

-- ========================================
-- 9. ADD MESSAGES TO PRIVATE ROOMS
-- ========================================

INSERT INTO public.messages (room_id, sender_id, content, message_type, created_at)
VALUES 
-- Admin room messages
('*************-6666-6666-************', '11111111-1111-1111-1111-111111111111', 'Chào admin phụ! 👋', 'text', NOW() - INTERVAL '1 hour'),
('*************-6666-6666-************', '*************-2222-2222-************', 'Chào admin chính! Hệ thống hoạt động tốt.', 'text', NOW() - INTERVAL '50 minutes'),
-- BLV room messages
('77777777-7777-7777-7777-777777777777', '*************-3333-3333-************', 'Chào các BLV! Hôm nay chúng ta sẽ bình luận trận đấu nào?', 'text', NOW() - INTERVAL '45 minutes'),
('77777777-7777-7777-7777-777777777777', '*************-4444-4444-************', 'Tôi sẵn sàng cho trận đấu tối nay! ⚽', 'text', NOW() - INTERVAL '30 minutes'),
('77777777-7777-7777-7777-777777777777', '*************-5555-5555-************', 'Cùng nhau tạo ra những bình luận hay nhé! 🎯', 'text', NOW() - INTERVAL '15 minutes')
ON CONFLICT DO NOTHING;

-- ========================================
-- 10. CREATE DIRECT CHAT ROOMS
-- ========================================

-- Direct chat between Admin and Member 1
INSERT INTO public.chat_rooms (
  id,
  name,
  type,
  created_by,
  created_at
) VALUES (
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'Chat riêng với Thành Viên 1',
  'direct',
  '11111111-1111-1111-1111-111111111111',
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Add members to direct chat
INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
VALUES 
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', NOW()),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-6666-6666-************', NOW())
ON CONFLICT (room_id, user_id) DO NOTHING;

-- Direct chat between BLV 1 and Member 2
INSERT INTO public.chat_rooms (
  id,
  name,
  type,
  created_by,
  created_at
) VALUES (
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'Chat riêng với Thành Viên 2',
  'direct',
  '*************-3333-3333-************',
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Add members to direct chat
INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
VALUES 
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '*************-3333-3333-************', NOW()),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '77777777-7777-7777-7777-777777777777', NOW())
ON CONFLICT (room_id, user_id) DO NOTHING;

-- Direct chat between Admin 2 and Member 3
INSERT INTO public.chat_rooms (
  id,
  name,
  type,
  created_by,
  created_at
) VALUES (
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'Chat riêng với Thành Viên 3',
  'direct',
  '*************-2222-2222-************',
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Add members to direct chat
INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
VALUES 
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', '*************-2222-2222-************', NOW()),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', '88888888-8888-8888-8888-888888888888', NOW())
ON CONFLICT (room_id, user_id) DO NOTHING;

-- ========================================
-- 11. CREATE GROUP CHAT ROOMS
-- ========================================

-- Group chat for BLVs
INSERT INTO public.chat_rooms (
  id,
  name,
  type,
  created_by,
  created_at
) VALUES (
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'Nhóm BLV',
  'group',
  '*************-3333-3333-************',
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Add members to group chat
INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
VALUES 
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '*************-3333-3333-************', NOW()),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '*************-4444-4444-************', NOW()),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '*************-5555-5555-************', NOW())
ON CONFLICT (room_id, user_id) DO NOTHING;

-- Group chat for Members
INSERT INTO public.chat_rooms (
  id,
  name,
  type,
  created_by,
  created_at
) VALUES (
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'Nhóm Thành Viên',
  'group',
  '*************-6666-6666-************',
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Add members to group chat
INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
VALUES 
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '*************-6666-6666-************', NOW()),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '77777777-7777-7777-7777-777777777777', NOW()),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '88888888-8888-8888-8888-888888888888', NOW())
ON CONFLICT (room_id, user_id) DO NOTHING;

-- ========================================
-- 12. ADD MESSAGES TO DIRECT CHATS
-- ========================================

INSERT INTO public.messages (room_id, sender_id, content, message_type, created_at)
VALUES 
-- Admin - Member 1 chat
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 'Chào bạn! Tôi là admin của hệ thống.', 'text', NOW() - INTERVAL '2 hours'),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-6666-6666-************', 'Chào admin! Rất vui được gặp bạn.', 'text', NOW() - INTERVAL '1 hour 50 minutes'),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 'Bạn có câu hỏi gì về hệ thống không?', 'text', NOW() - INTERVAL '1 hour 30 minutes'),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-6666-6666-************', 'Tôi muốn tìm hiểu về tính năng chat này.', 'text', NOW() - INTERVAL '1 hour'),

-- BLV 1 - Member 2 chat
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '*************-3333-3333-************', 'Xin chào! Tôi là bình luận viên.', 'text', NOW() - INTERVAL '1 hour 45 minutes'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '77777777-7777-7777-7777-777777777777', 'Chào anh! Em muốn hỏi về cách sử dụng chat.', 'text', NOW() - INTERVAL '1 hour 30 minutes'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '*************-3333-3333-************', 'Tôi sẽ hướng dẫn bạn cách sử dụng.', 'text', NOW() - INTERVAL '1 hour 15 minutes'),

-- Admin 2 - Member 3 chat
('cccccccc-cccc-cccc-cccc-cccccccccccc', '*************-2222-2222-************', 'Chào bạn! Tôi là admin phụ.', 'text', NOW() - INTERVAL '1 hour 20 minutes'),
('cccccccc-cccc-cccc-cccc-cccccccccccc', '88888888-8888-8888-8888-888888888888', 'Chào admin! Em cần hỗ trợ về tài khoản.', 'text', NOW() - INTERVAL '1 hour 10 minutes'),
('cccccccc-cccc-cccc-cccc-cccccccccccc', '*************-2222-2222-************', 'Tôi sẽ giúp bạn ngay.', 'text', NOW() - INTERVAL '1 hour')
ON CONFLICT DO NOTHING;

-- ========================================
-- 13. ADD MESSAGES TO GROUP CHATS
-- ========================================

INSERT INTO public.messages (room_id, sender_id, content, message_type, created_at)
VALUES 
-- BLV Group messages
('dddddddd-dddd-dddd-dddd-dddddddddddd', '*************-3333-3333-************', 'Chào các BLV! Hôm nay chúng ta sẽ bình luận trận đấu nào?', 'text', NOW() - INTERVAL '1 hour'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', '*************-4444-4444-************', 'Tôi sẵn sàng cho trận đấu tối nay! ⚽', 'text', NOW() - INTERVAL '45 minutes'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', '*************-5555-5555-************', 'Cùng nhau tạo ra những bình luận hay nhé! 🎯', 'text', NOW() - INTERVAL '30 minutes'),

-- Member Group messages
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '*************-6666-6666-************', 'Chào các thành viên! 👋', 'text', NOW() - INTERVAL '2 hours'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '77777777-7777-7777-7777-777777777777', 'Chào bạn! Rất vui được gặp mọi người.', 'text', NOW() - INTERVAL '1 hour 45 minutes'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '88888888-8888-8888-8888-888888888888', 'Chúng ta có thể trao đổi kinh nghiệm ở đây! 💬', 'text', NOW() - INTERVAL '1 hour 30 minutes')
ON CONFLICT DO NOTHING;

-- ========================================
-- 14. ADD SOME MESSAGE STATUS EXAMPLES
-- ========================================

-- Mark some messages as delivered
UPDATE public.messages 
SET status = 'delivered', delivered_at = NOW()
WHERE id IN (
  SELECT id FROM public.messages 
  WHERE created_at < NOW() - INTERVAL '1 hour'
  LIMIT 3
);

-- Mark some messages as read
UPDATE public.messages 
SET status = 'read', read_at = NOW()
WHERE id IN (
  SELECT id FROM public.messages 
  WHERE created_at < NOW() - INTERVAL '2 hours'
  LIMIT 2
);

-- ========================================
-- 15. ADD SOME MESSAGE READS
-- ========================================

INSERT INTO public.message_reads (message_id, user_id, read_at)
SELECT 
  m.id,
  u.id,
  NOW() - INTERVAL '1 hour'
FROM public.messages m
CROSS JOIN auth.users u
WHERE m.created_at < NOW() - INTERVAL '2 hours'
AND u.email IN ('<EMAIL>', '<EMAIL>')
LIMIT 5
ON CONFLICT (message_id, user_id) DO NOTHING;

-- ========================================
-- SEED COMPLETE
-- ========================================

DO $$
DECLARE
    user_count INTEGER;
    profile_count INTEGER;
    room_count INTEGER;
    message_count INTEGER;
    presence_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM auth.users WHERE email LIKE '%@qclg.com';
    SELECT COUNT(*) INTO profile_count FROM public.profiles;
    SELECT COUNT(*) INTO room_count FROM public.chat_rooms;
    SELECT COUNT(*) INTO message_count FROM public.messages;
    SELECT COUNT(*) INTO presence_count FROM public.user_presence WHERE is_online = true;
    
    RAISE NOTICE '🎉 Database seed completed successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 Sample data created:';
    RAISE NOTICE '   Users: %', user_count;
    RAISE NOTICE '   Profiles: %', profile_count;
    RAISE NOTICE '   Rooms: %', room_count;
    RAISE NOTICE '   Messages: %', message_count;
    RAISE NOTICE '   Online users: %', presence_count;
    RAISE NOTICE '';
    RAISE NOTICE '🔐 Login credentials:';
    RAISE NOTICE '   Admin: <EMAIL> / admin123';
    RAISE NOTICE '   Admin 2: <EMAIL> / admin123';
    RAISE NOTICE '   BLV 1: <EMAIL> / blv123';
    RAISE NOTICE '   BLV 2: <EMAIL> / blv123';
    RAISE NOTICE '   BLV 3: <EMAIL> / blv123';
    RAISE NOTICE '   Member 1: <EMAIL> / member123';
    RAISE NOTICE '   Member 2: <EMAIL> / member123';
    RAISE NOTICE '   Member 3: <EMAIL> / member123';
    RAISE NOTICE '';
    RAISE NOTICE '🏠 Available rooms:';
    RAISE NOTICE '   - Nhóm Chat Tổng (general)';
    RAISE NOTICE '   - Phòng Admin (private)';
    RAISE NOTICE '   - Phòng BLV (private)';
    RAISE NOTICE '   - Chat riêng với Thành Viên 1 (direct)';
    RAISE NOTICE '   - Chat riêng với Thành Viên 2 (direct)';
    RAISE NOTICE '   - Chat riêng với Thành Viên 3 (direct)';
    RAISE NOTICE '   - Nhóm BLV (group)';
    RAISE NOTICE '   - Nhóm Thành Viên (group)';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Database is ready for testing!';
END $$;


-- ========================================
-- QCLG CHAT SYSTEM - BETTING DATA SEED (UNIQUE UUIDs)
-- ========================================
-- This file contains sample betting and sports data for "Lên kèo" feature
-- Run this file after migrate.sql and seed.sql to add betting sample data

-- ========================================
-- 1. INSERT SPORTS DATA
-- ========================================

INSERT INTO public.sports (
  sport_id,
  name,
  is_football,
  description,
  rules,
  created_at,
  updated_at
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  'Bóng đá',
  true,
  'Môn thể thao vua với 11 cầu thủ mỗi đội',
  'Thời gian thi đấu 90 phút, chia làm 2 hiệp 45 phút',
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  'Bóng rổ',
  false,
  'Môn thể thao với 5 cầu thủ mỗi đội',
  'Thời gian thi đấu 48 phút, chia làm 4 hiệp 12 phút',
  NOW(),
  NOW()
),
(
  '*************-3333-3333-************',
  'Tennis',
  false,
  'Môn thể thao cá nhân hoặc đôi',
  'Thắng 2 set trong 3 set hoặc 3 set trong 5 set',
  NOW(),
  NOW()
)
ON CONFLICT (sport_id) DO NOTHING;

-- ========================================
-- 2. INSERT LEAGUES DATA
-- ========================================

INSERT INTO public.leagues (
  league_id,
  sport_id,
  name,
  country,
  season,
  start_date,
  end_date,
  logo_url,
  created_at,
  updated_at
) VALUES 
-- Football leagues
(
  '*************-4444-4444-************',
  '11111111-1111-1111-1111-111111111111',
  'Premier League',
  'Anh',
  2024,
  '2024-08-17',
  '2025-05-25',
  'https://logos-world.net/wp-content/uploads/2020/06/Premier-League-Logo.png',
  NOW(),
  NOW()
),
(
  '*************-5555-5555-************',
  '11111111-1111-1111-1111-111111111111',
  'La Liga',
  'Tây Ban Nha',
  2024,
  '2024-08-18',
  '2025-05-26',
  'https://logos-world.net/wp-content/uploads/2020/06/La-Liga-Logo.png',
  NOW(),
  NOW()
),
(
  '*************-6666-6666-************',
  '11111111-1111-1111-1111-111111111111',
  'Bundesliga',
  'Đức',
  2024,
  '2024-08-16',
  '2025-05-24',
  'https://logos-world.net/wp-content/uploads/2020/06/Bundesliga-Logo.png',
  NOW(),
  NOW()
),
(
  '77777777-7777-7777-7777-777777777777',
  '11111111-1111-1111-1111-111111111111',
  'Serie A',
  'Ý',
  2024,
  '2024-08-19',
  '2025-05-27',
  'https://logos-world.net/wp-content/uploads/2020/06/Serie-A-Logo.png',
  NOW(),
  NOW()
),
(
  '88888888-8888-8888-8888-888888888888',
  '11111111-1111-1111-1111-111111111111',
  'Ligue 1',
  'Pháp',
  2024,
  '2024-08-20',
  '2025-05-28',
  'https://logos-world.net/wp-content/uploads/2020/06/Ligue-1-Logo.png',
  NOW(),
  NOW()
),
-- Basketball leagues
(
  '99999999-9999-9999-9999-999999999999',
  '*************-2222-2222-************',
  'NBA',
  'Mỹ',
  2024,
  '2024-10-22',
  '2025-06-15',
  'https://logos-world.net/wp-content/uploads/2020/06/NBA-Logo.png',
  NOW(),
  NOW()
)
ON CONFLICT (league_id) DO NOTHING;

-- ========================================
-- 3. INSERT TEAMS DATA
-- ========================================

INSERT INTO public.teams (
  id,
  external_id,
  name,
  league_id,
  stadium,
  coach,
  founded_year,
  logo_url,
  created_at,
  updated_at
) VALUES 
-- Premier League teams
(
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'MAN_CITY',
  'Manchester City',
  '*************-4444-4444-************',
  'Etihad Stadium',
  'Pep Guardiola',
  1880,
  'https://logos-world.net/wp-content/uploads/2020/06/Manchester-City-Logo.png',
  NOW(),
  NOW()
),
(
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'ARSENAL',
  'Arsenal',
  '*************-4444-4444-************',
  'Emirates Stadium',
  'Mikel Arteta',
  1886,
  'https://logos-world.net/wp-content/uploads/2020/06/Arsenal-Logo.png',
  NOW(),
  NOW()
),
(
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'LIVERPOOL',
  'Liverpool',
  '*************-4444-4444-************',
  'Anfield',
  'Jürgen Klopp',
  1892,
  'https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png',
  NOW(),
  NOW()
),
(
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'CHELSEA',
  'Chelsea',
  '*************-4444-4444-************',
  'Stamford Bridge',
  'Mauricio Pochettino',
  1905,
  'https://logos-world.net/wp-content/uploads/2020/06/Chelsea-Logo.png',
  NOW(),
  NOW()
),
-- La Liga teams
(
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'REAL_MADRID',
  'Real Madrid',
  '*************-5555-5555-************',
  'Santiago Bernabéu',
  'Carlo Ancelotti',
  1902,
  'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png',
  NOW(),
  NOW()
),
(
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  'BARCELONA',
  'Barcelona',
  '*************-5555-5555-************',
  'Camp Nou',
  'Xavi Hernández',
  1899,
  'https://logos-world.net/wp-content/uploads/2020/06/Barcelona-Logo.png',
  NOW(),
  NOW()
),
-- NBA teams
(
  '00000000-0000-0000-0000-000000000001',
  'LAKERS',
  'Los Angeles Lakers',
  '99999999-9999-9999-9999-999999999999',
  'Crypto.com Arena',
  'Darvin Ham',
  1947,
  'https://logos-world.net/wp-content/uploads/2020/06/Lakers-Logo.png',
  NOW(),
  NOW()
),
(
  '00000000-0000-0000-0000-000000000002',
  'WARRIORS',
  'Golden State Warriors',
  '99999999-9999-9999-9999-999999999999',
  'Chase Center',
  'Steve Kerr',
  1946,
  'https://logos-world.net/wp-content/uploads/2020/06/Warriors-Logo.png',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 4. INSERT MATCHES DATA
-- ========================================

INSERT INTO public.matches (
  id,
  league_id,
  home_team_id,
  away_team_id,
  match_date,
  stadium,
  status,
  home_score,
  away_score,
  referee,
  weather,
  created_at,
  updated_at
) VALUES 
-- Upcoming matches
(
  '11111111-1111-1111-1111-111111111111',
  '*************-4444-4444-************',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  NOW() + INTERVAL '2 days',
  'Etihad Stadium',
  'scheduled',
  NULL,
  NULL,
  'Michael Oliver',
  'Sunny',
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  '*************-4444-4444-************',
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  NOW() + INTERVAL '3 days',
  'Anfield',
  'scheduled',
  NULL,
  NULL,
  'Anthony Taylor',
  'Cloudy',
  NOW(),
  NOW()
),
(
  '*************-3333-3333-************',
  '*************-5555-5555-************',
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  NOW() + INTERVAL '4 days',
  'Santiago Bernabéu',
  'scheduled',
  NULL,
  NULL,
  'Carlos del Cerro Grande',
  'Clear',
  NOW(),
  NOW()
),
-- Live matches
(
  '*************-4444-4444-************',
  '*************-4444-4444-************',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  NOW() - INTERVAL '30 minutes',
  'Etihad Stadium',
  'live',
  1,
  0,
  'Paul Tierney',
  'Sunny',
  NOW(),
  NOW()
),
-- Completed matches
(
  '*************-5555-5555-************',
  '*************-4444-4444-************',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  NOW() - INTERVAL '2 days',
  'Emirates Stadium',
  'completed',
  2,
  1,
  'Craig Pawson',
  'Rainy',
  NOW(),
  NOW()
),
-- NBA matches
(
  '*************-6666-6666-************',
  '99999999-9999-9999-9999-999999999999',
  '00000000-0000-0000-0000-000000000001',
  '00000000-0000-0000-0000-000000000002',
  NOW() + INTERVAL '1 day',
  'Crypto.com Arena',
  'scheduled',
  NULL,
  NULL,
  'Scott Foster',
  'Indoor',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 5. INSERT MATCH PREDICTIONS DATA
-- ========================================

INSERT INTO public.match_predictions (
  id,
  match_id,
  home_win_prob,
  away_win_prob,
  draw_prob,
  odds_home,
  odds_away,
  odds_draw,
  created_at,
  updated_at
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  '11111111-1111-1111-1111-111111111111',
  0.45,
  0.35,
  0.20,
  2.20,
  2.85,
  3.50,
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  '*************-2222-2222-************',
  0.40,
  0.40,
  0.20,
  2.50,
  2.50,
  3.20,
  NOW(),
  NOW()
),
(
  '*************-3333-3333-************',
  '*************-3333-3333-************',
  0.50,
  0.30,
  0.20,
  2.00,
  3.30,
  3.40,
  NOW(),
  NOW()
),
(
  '*************-4444-4444-************',
  '*************-4444-4444-************',
  0.55,
  0.25,
  0.20,
  1.80,
  4.00,
  3.60,
  NOW(),
  NOW()
),
(
  '*************-5555-5555-************',
  '*************-5555-5555-************',
  0.60,
  0.25,
  0.15,
  1.67,
  4.50,
  4.20,
  NOW(),
  NOW()
),
(
  '*************-6666-6666-************',
  '*************-6666-6666-************',
  0.48,
  0.42,
  0.10,
  2.08,
  2.38,
  8.00,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 6. INSERT KEO BONG DATA (Betting Tips)
-- ========================================

INSERT INTO public.keo_bong (
  id,
  "order",
  type,
  expert_uid,
  expert_image,
  expert,
  tournament,
  team_name,
  closing_bet,
  saying,
  status,
  displayed,
  "winDisplayed",
  updated_at,
  created_at
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  1,
  'over_under',
  '*************-3333-3333-************',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv1',
  'BLV Minh Tuấn',
  'Premier League',
  'Man City vs Arsenal',
  'Over 2.5',
  'Cả hai đội đều có hàng công mạnh, trận đấu sẽ có nhiều bàn thắng. Tôi tin tưởng vào kèo Over 2.5 với tỷ lệ 1.85.',
  'active',
  true,
  false,
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  2,
  'handicap',
  '*************-4444-4444-************',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv2',
  'BLV Hoàng Nam',
  'La Liga',
  'Real Madrid vs Barcelona',
  'Real Madrid -0.5',
  'Real Madrid đang trong phong độ tốt, sân nhà sẽ là lợi thế lớn. Kèo Real Madrid -0.5 với tỷ lệ 1.90 là lựa chọn an toàn.',
  'active',
  true,
  false,
  NOW(),
  NOW()
),
(
  '*************-3333-3333-************',
  3,
  '1x2',
  '*************-5555-5555-************',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv3',
  'BLV Văn Đức',
  'Premier League',
  'Liverpool vs Chelsea',
  'Liverpool Win',
  'Liverpool có lợi thế sân nhà và phong độ ổn định. Kèo Liverpool thắng với tỷ lệ 2.10 rất hấp dẫn.',
  'active',
  true,
  false,
  NOW(),
  NOW()
),
(
  '*************-4444-4444-************',
  4,
  'both_teams_score',
  '*************-3333-3333-************',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv1',
  'BLV Minh Tuấn',
  'Bundesliga',
  'Bayern vs Dortmund',
  'Both Teams Score',
  'Cả hai đội đều có khả năng ghi bàn cao. Kèo Both Teams Score với tỷ lệ 1.75 là lựa chọn tốt.',
  'active',
  true,
  false,
  NOW(),
  NOW()
),
(
  '*************-5555-5555-************',
  5,
  'over_under',
  '*************-4444-4444-************',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv2',
  'BLV Hoàng Nam',
  'NBA',
  'Lakers vs Warriors',
  'Over 220.5',
  'Hai đội đều có lối chơi tấn công mạnh. Kèo Over 220.5 điểm với tỷ lệ 1.80 rất khả thi.',
  'active',
  true,
  false,
  NOW(),
  NOW()
),
(
  '*************-6666-6666-************',
  6,
  'handicap',
  '*************-5555-5555-************',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=blv3',
  'BLV Văn Đức',
  'Serie A',
  'Juventus vs Inter',
  'Under 2.5',
  'Hai đội đều chơi phòng thủ chặt chẽ. Kèo Under 2.5 với tỷ lệ 1.95 là lựa chọn an toàn.',
  'won',
  true,
  true,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 7. INSERT PLAYERS DATA
-- ========================================

INSERT INTO public.players (
  id,
  external_id,
  name,
  team_id,
  "position",
  nationality,
  birth_date,
  jersey_number,
  height,
  weight,
  created_at,
  updated_at
) VALUES 
-- Manchester City players
(
  '11111111-1111-1111-1111-111111111111',
  'HAALAND',
  'Erling Haaland',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'ST',
  'Na Uy',
  '2000-07-21',
  9,
  194,
  88,
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  'DE_BRUYNE',
  'Kevin De Bruyne',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'CAM',
  'Bỉ',
  '1991-06-28',
  17,
  181,
  70,
  NOW(),
  NOW()
),
-- Arsenal players
(
  '*************-3333-3333-************',
  'SALAH',
  'Mohamed Salah',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'RW',
  'Ai Cập',
  '1992-06-15',
  11,
  175,
  71,
  NOW(),
  NOW()
),
(
  '*************-4444-4444-************',
  'ODEKARD',
  'Martin Ødegaard',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'CAM',
  'Na Uy',
  '1998-12-17',
  8,
  178,
  68,
  NOW(),
  NOW()
),
-- Real Madrid players
(
  '*************-5555-5555-************',
  'BENZEMA',
  'Karim Benzema',
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'ST',
  'Pháp',
  '1987-12-19',
  9,
  185,
  81,
  NOW(),
  NOW()
),
(
  '*************-6666-6666-************',
  'MODRIC',
  'Luka Modrić',
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'CM',
  'Croatia',
  '1985-09-09',
  10,
  172,
  66,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 8. INSERT TEAM SEASON STATS
-- ========================================

INSERT INTO public.team_season_stats (
  id,
  team_id,
  league_id,
  season,
  wins,
  losses,
  draws,
  goals_scored,
  goals_conceded,
  "xG",
  "xGA",
  passes_completed,
  shots_on_target,
  shots,
  tackles,
  interceptions,
  clean_sheets,
  created_at,
  updated_at
) VALUES 
-- Manchester City stats
(
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '*************-4444-4444-************',
  2024,
  15,
  3,
  2,
  45,
  18,
  42.5,
  19.2,
  12500,
  180,
  320,
  450,
  380,
  8,
  NOW(),
  NOW()
),
-- Arsenal stats
(
  '*************-2222-2222-************',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '*************-4444-4444-************',
  2024,
  14,
  4,
  2,
  42,
  20,
  40.8,
  21.5,
  11800,
  165,
  295,
  420,
  350,
  6,
  NOW(),
  NOW()
),
-- Real Madrid stats
(
  '*************-3333-3333-************',
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  '*************-5555-5555-************',
  2024,
  16,
  2,
  2,
  48,
  15,
  45.2,
  16.8,
  13200,
  195,
  340,
  480,
  420,
  10,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 9. INSERT PLAYER SEASON STATS
-- ========================================

INSERT INTO public.player_season_stats (
  id,
  player_id,
  team_id,
  league_id,
  season,
  appearances,
  goals,
  assists,
  minutes_played,
  yellow_cards,
  red_cards,
  "xG",
  "xA",
  shots,
  key_passes,
  created_at,
  updated_at
) VALUES 
-- Erling Haaland stats
(
  '11111111-1111-1111-1111-111111111111',
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '*************-4444-4444-************',
  2024,
  18,
  22,
  3,
  1620,
  2,
  0,
  20.5,
  2.8,
  85,
  15,
  NOW(),
  NOW()
),
-- Kevin De Bruyne stats
(
  '*************-2222-2222-************',
  '*************-2222-2222-************',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '*************-4444-4444-************',
  2024,
  16,
  8,
  12,
  1440,
  1,
  0,
  7.2,
  11.5,
  45,
  65,
  NOW(),
  NOW()
),
-- Mohamed Salah stats
(
  '*************-3333-3333-************',
  '*************-3333-3333-************',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '*************-4444-4444-************',
  2024,
  19,
  18,
  8,
  1710,
  3,
  0,
  16.8,
  7.2,
  78,
  42,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 10. INSERT MATCH EVENTS
-- ========================================

INSERT INTO public.match_events (
  id,
  match_id,
  sport_id,
  event_type,
  minute,
  player_id,
  details,
  created_at,
  updated_at
) VALUES 
-- Live match events
(
  '11111111-1111-1111-1111-111111111111',
  '*************-4444-4444-************',
  '11111111-1111-1111-1111-111111111111',
  'goal',
  15,
  '11111111-1111-1111-1111-111111111111',
  '{"assist": "*************-2222-2222-************", "type": "header", "xG": 0.8}',
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  '*************-4444-4444-************',
  '11111111-1111-1111-1111-111111111111',
  'yellow_card',
  28,
  '*************-3333-3333-************',
  '{"reason": "foul", "position": "midfield"}',
  NOW(),
  NOW()
),
(
  '*************-3333-3333-************',
  '*************-4444-4444-************',
  '11111111-1111-1111-1111-111111111111',
  'substitution',
  65,
  '*************-4444-4444-************',
  '{"player_out": "*************-4444-4444-************", "player_in": "*************-5555-5555-************"}',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 11. INSERT MATCH STREAMS
-- ========================================

INSERT INTO public.match_streams (
  id,
  match_id,
  stream_url,
  total_views,
  peak_views,
  average_view_duration,
  start_time,
  end_time,
  created_at,
  updated_at
) VALUES 
(
  1,
  '*************-4444-4444-************',
  'https://stream.qclg.com/live/man-city-vs-liverpool',
  15000,
  2500,
  85.5,
  NOW() - INTERVAL '30 minutes',
  NULL,
  NOW(),
  NOW()
),
(
  2,
  '11111111-1111-1111-1111-111111111111',
  'https://stream.qclg.com/live/man-city-vs-arsenal',
  0,
  0,
  0,
  NOW() + INTERVAL '2 days',
  NULL,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- 12. INSERT TEAM RATINGS
-- ========================================

INSERT INTO public.team_ratings (
  id,
  team_id,
  rating_date,
  elo_score,
  created_at,
  updated_at
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  CURRENT_DATE,
  1850.5,
  NOW(),
  NOW()
),
(
  '*************-2222-2222-************',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  CURRENT_DATE,
  1820.3,
  NOW(),
  NOW()
),
(
  '*************-3333-3333-************',
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  CURRENT_DATE,
  1880.7,
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- ========================================
-- BETTING DATA SEED COMPLETE
-- ========================================

DO $$
DECLARE
    sports_count INTEGER;
    leagues_count INTEGER;
    teams_count INTEGER;
    matches_count INTEGER;
    predictions_count INTEGER;
    keo_count INTEGER;
    players_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO sports_count FROM public.sports;
    SELECT COUNT(*) INTO leagues_count FROM public.leagues;
    SELECT COUNT(*) INTO teams_count FROM public.teams;
    SELECT COUNT(*) INTO matches_count FROM public.matches;
    SELECT COUNT(*) INTO predictions_count FROM public.match_predictions;
    SELECT COUNT(*) INTO keo_count FROM public.keo_bong;
    SELECT COUNT(*) INTO players_count FROM public.players;
    
    RAISE NOTICE '🎉 Betting data seed completed successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 Betting data created:';
    RAISE NOTICE '   Sports: %', sports_count;
    RAISE NOTICE '   Leagues: %', leagues_count;
    RAISE NOTICE '   Teams: %', teams_count;
    RAISE NOTICE '   Matches: %', matches_count;
    RAISE NOTICE '   Predictions: %', predictions_count;
    RAISE NOTICE '   Kèo bóng: %', keo_count;
    RAISE NOTICE '   Players: %', players_count;
    RAISE NOTICE '';
    RAISE NOTICE '🏆 Available leagues:';
    RAISE NOTICE '   - Premier League (Anh)';
    RAISE NOTICE '   - La Liga (Tây Ban Nha)';
    RAISE NOTICE '   - Bundesliga (Đức)';
    RAISE NOTICE '   - Serie A (Ý)';
    RAISE NOTICE '   - Ligue 1 (Pháp)';
    RAISE NOTICE '   - NBA (Mỹ)';
    RAISE NOTICE '';
    RAISE NOTICE '⚽ Sample matches:';
    RAISE NOTICE '   - Man City vs Arsenal (Upcoming)';
    RAISE NOTICE '   - Liverpool vs Chelsea (Upcoming)';
    RAISE NOTICE '   - Real Madrid vs Barcelona (Upcoming)';
    RAISE NOTICE '   - Man City vs Liverpool (Live)';
    RAISE NOTICE '   - Arsenal vs Chelsea (Completed)';
    RAISE NOTICE '   - Lakers vs Warriors (NBA)';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 Sample betting tips:';
    RAISE NOTICE '   - Over/Under predictions';
    RAISE NOTICE '   - Handicap predictions';
    RAISE NOTICE '   - 1X2 predictions';
    RAISE NOTICE '   - Both Teams Score predictions';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Betting system is ready for "Lên kèo" feature!';
END $$;

