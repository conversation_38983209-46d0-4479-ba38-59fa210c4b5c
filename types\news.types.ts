// ========================================
// NEWS SYSTEM TYPE DEFINITIONS
// ========================================

export interface Post {
  id: string;
  title: string;
  content: string;
  description: string | null;
  thumbnail: string | null;
  author_id: string | null;
  published_at: string | null;
  created_at: string;
  updated_at: string | null;
  // SEO fields (migrated from page_seo table)
  meta_title: string | null;
  meta_description: string | null;
  meta_keywords: string | null;
  slug: string | null;
}

// PageSeo interface removed - SEO fields now part of Post interface

export interface PostWithAuthor extends Post {
  author?: {
    id: string;
    full_name: string | null;
    email: string;
    role: 'admin' | 'blv' | 'member';
  };
}

// PostWithSeo is now same as PostWithAuthor since SEO is part of Post
export interface PostWithSeo extends PostWithAuthor { }

export interface PublishedPostWithSeo {
  post_id: string;
  title: string;
  content: string;
  description: string | null;
  thumbnail: string | null;
  author_name: string | null;
  published_at: string;
  slug: string | null;
  meta_title: string | null;
  meta_description: string | null;
  meta_keywords: string | null;
}

// Form interfaces for creating/updating
export interface CreatePostRequest {
  title: string;
  content: string;
  description?: string | null;
  thumbnail?: string | null;
  author_id: string;
  published_at?: string | null;
  // SEO fields
  meta_title?: string | null;
  meta_description?: string | null;
  meta_keywords?: string | null;
  slug?: string | null;
}

export interface UpdatePostRequest {
  title?: string;
  content?: string;
  description?: string | null;
  thumbnail?: string | null;
  published_at?: string | null;
  // SEO fields
  meta_title?: string | null;
  meta_description?: string | null;
  meta_keywords?: string | null;
  slug?: string | null;
}

// PageSeo request interfaces removed - SEO fields now part of Post

// API Response interfaces
export interface PostsResponse {
  data: PostWithSeo[];
  count: number;
  error?: string;
}

export interface PostResponse {
  data: PostWithSeo | null;
  error?: string;
}

// PageSeoResponse removed - SEO data now part of PostResponse

// Filter and pagination interfaces
export interface PostFilters {
  author_id?: string;
  published?: boolean;
  search?: string;
  type?: 'article' | 'page' | 'category' | 'tag';
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PostsQueryParams extends PostFilters, PaginationParams {
  sort_by?: 'created_at' | 'updated_at' | 'published_at' | 'title';
  sort_order?: 'asc' | 'desc';
}

// News management context
export interface NewsContextType {
  posts: PostWithSeo[];
  loading: boolean;
  error: string | null;

  // Post operations
  createPost: (post: CreatePostRequest) => Promise<PostWithSeo | null>;
  updatePost: (id: string, post: UpdatePostRequest) => Promise<PostWithSeo | null>;
  deletePost: (id: string) => Promise<boolean>;
  publishPost: (id: string) => Promise<PostWithSeo | null>;
  unpublishPost: (id: string) => Promise<PostWithSeo | null>;

  // SEO operations
  // PageSeo methods removed - SEO fields now part of Post operations

  // Fetch operations
  fetchPosts: (params?: PostsQueryParams) => Promise<void>;
  fetchPost: (id: string) => Promise<PostWithSeo | null>;
  fetchPublishedPosts: () => Promise<PublishedPostWithSeo[]>;
  fetchPostBySlug: (slug: string) => Promise<PostWithSeo | null>;

  // Utility operations
  generateSlug: (title: string) => string;
  validateSlug: (slug: string) => Promise<boolean>;
  refreshPosts: () => Promise<void>;
}

// Component props interfaces
export interface PostEditorProps {
  post?: PostWithSeo;
  onSave: (post: CreatePostRequest | UpdatePostRequest) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

// SeoEditorProps removed - SEO editing now part of PostEditorProps

export interface PostListProps {
  posts: PostWithSeo[];
  loading?: boolean;
  onEdit: (post: PostWithSeo) => void;
  onDelete: (id: string) => void;
  onPublish: (id: string) => void;
  onUnpublish: (id: string) => void;
}

export interface PostCardProps {
  post: PostWithSeo;
  onEdit: () => void;
  onDelete: () => void;
  onPublish: () => void;
  onUnpublish: () => void;
  showActions?: boolean;
}

// Validation schemas (for use with form libraries)
export interface PostValidationSchema {
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  slug?: string;
}

export interface SeoValidationSchema {
  slug: string;
  meta_title: string;
  meta_description?: string;
  meta_keywords?: string;
  type: 'article' | 'page' | 'category' | 'tag';
}

// Error types
export interface NewsError {
  code: string;
  message: string;
  details?: any;
}

export interface ValidationError {
  field: string;
  message: string;
}