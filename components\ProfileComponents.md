# Profile Components Documentation

## Tổng quan

Hệ thống Profile Components bao gồm các component và hook để quản lý thông tin người dùng, bao gồm:

- **ProfileUpdateModal**: Modal popup để cập nhật thông tin profile
- **ProfileButton**: Button hiển thị avatar và dropdown menu
- **useProfile**: Hook để quản lý state profile
- **profileService.uploadAvatarToTenan**: Method upload avatar vào bucket "tenan"

## Components

### 1. ProfileButton

Component chính để hiển thị thông tin user và mở modal cập nhật.

```tsx
import ProfileButton from '@/components/ProfileButton';

// Sử dụng cơ bản
<ProfileButton />

// Các tùy chọn
<ProfileButton 
  showName={false}  // Chỉ hiển thị avatar
  size="small"      // Kích thước: small | default | large
/>
```

**Props:**
- `showName?: boolean` - <PERSON><PERSON><PERSON> thị tên và email (default: true)
- `size?: 'small' | 'default' | 'large'` - <PERSON><PERSON>ch thước component (default: 'default')

**Features:**
- Hiển thị avatar, tên và email
- Dropdown menu với options: "Cập nhật thông tin", "Đăng xuất"
- Tự động mở ProfileUpdateModal khi click "Cập nhật thông tin"
- Loading state khi đang tải profile
- Redirect đến login nếu chưa đăng nhập

### 2. ProfileUpdateModal

Modal popup để cập nhật thông tin profile.

```tsx
import ProfileUpdateModal from '@/components/ProfileUpdateModal';

<ProfileUpdateModal
  visible={modalVisible}
  onCancel={() => setModalVisible(false)}
  onSuccess={(updatedProfile) => {
    // Handle success
    console.log('Profile updated:', updatedProfile);
  }}
  currentProfile={profile}
/>
```

**Props:**
- `visible: boolean` - Hiển thị/ẩn modal
- `onCancel: () => void` - Callback khi đóng modal
- `onSuccess: (profile: Profile) => void` - Callback khi cập nhật thành công
- `currentProfile: Profile | null` - Thông tin profile hiện tại

**Features:**
- Upload và preview avatar
- Validation form (tên hiển thị bắt buộc, 2-100 ký tự)
- Upload ảnh vào Supabase bucket "tenan", thư mục "public"
- Validate file ảnh (chỉ chấp nhận image, max 5MB)
- Loading states cho upload và submit
- Auto-fill form với dữ liệu hiện tại

## Hooks

### useProfile

Hook để quản lý state profile của user hiện tại.

```tsx
import { useProfile } from '@/hooks/useProfile';

function MyComponent() {
  const { profile, loading, error, updateProfile, reloadProfile } = useProfile();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!profile) return <div>Not logged in</div>;

  return (
    <div>
      <h1>Hello {profile.full_name}</h1>
      <img src={profile.avatar_url} alt="Avatar" />
    </div>
  );
}
```

**Returns:**
- `profile: Profile | null` - Thông tin profile hiện tại
- `loading: boolean` - Trạng thái loading
- `error: Error | null` - Lỗi nếu có
- `updateProfile: (profile: Profile) => void` - Cập nhật profile trong state
- `reloadProfile: () => void` - Reload profile từ server

## Services

### profileService.uploadAvatarToTenan

Method mới để upload avatar vào bucket "tenan".

```tsx
import { profileService } from '@/services/profile.service';

const { url, error } = await profileService.uploadAvatarToTenan(userId, file);
```

**Parameters:**
- `userId: string` - ID của user
- `file: File` - File ảnh cần upload

**Returns:**
- `url: string | null` - URL của ảnh đã upload
- `error: Error | null` - Lỗi nếu có

**Storage Location:**
- Bucket: `tenan`
- Path: `public/{userId}-{timestamp}.{ext}`

## Cách sử dụng

### 1. Trong Header/Navbar

```tsx
import ProfileButton from '@/components/ProfileButton';

function Header() {
  return (
    <div className="header">
      <div className="logo">My App</div>
      <ProfileButton size="small" />
    </div>
  );
}
```

### 2. Trong Sidebar

```tsx
import ProfileButton from '@/components/ProfileButton';

function Sidebar() {
  return (
    <div className="sidebar">
      <ProfileButton />
      {/* Other menu items */}
    </div>
  );
}
```

### 3. Sử dụng riêng Modal

```tsx
import { useState } from 'react';
import { useProfile } from '@/hooks/useProfile';
import ProfileUpdateModal from '@/components/ProfileUpdateModal';

function MyComponent() {
  const [modalVisible, setModalVisible] = useState(false);
  const { profile, updateProfile } = useProfile();

  return (
    <div>
      <button onClick={() => setModalVisible(true)}>
        Cập nhật Profile
      </button>
      
      <ProfileUpdateModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSuccess={updateProfile}
        currentProfile={profile}
      />
    </div>
  );
}
```

## Styling

Components sử dụng Ant Design, có thể customize thông qua:

```tsx
// Custom style cho ProfileButton
<ProfileButton 
  style={{ 
    background: '#f0f0f0',
    borderRadius: '8px' 
  }}
/>

// Custom width cho Modal
<ProfileUpdateModal
  width={600}
  // other props...
/>
```

## Dependencies

- `antd` - UI components
- `@ant-design/icons` - Icons
- Supabase client - Authentication và storage
- Profile service và types

## Notes

- Avatar được lưu trong Supabase Storage bucket "tenan", thư mục "public"
- File ảnh được validate: chỉ chấp nhận image, max 5MB
- Component tự động handle authentication state
- Profile data được cache trong hook, chỉ reload khi cần thiết
