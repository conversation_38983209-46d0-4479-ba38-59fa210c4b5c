-- Create betting_odds table
CREATE TABLE IF NOT EXISTS betting_odds (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  expert TEXT NOT NULL, -- Will be populated from profile
  tournament TEXT NOT NULL,
  team_name TEXT NOT NULL,
  closing_bet TEXT NOT NULL,
  saying TEXT NOT NULL,
  status TEXT CHECK (status IN ('win', 'lose')) DEFAULT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_betting_odds_status ON betting_odds(status);
CREATE INDEX IF NOT EXISTS idx_betting_odds_created_by ON betting_odds(created_by);
CREATE INDEX IF NOT EXISTS idx_betting_odds_created_at ON betting_odds(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE betting_odds ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Admin can do everything
CREATE POLICY "Admin can manage betting odds" ON betting_odds
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Users can read betting odds
CREATE POLICY "Users can read betting odds" ON betting_odds
  FOR SELECT USING (true);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_betting_odds_updated_at 
  BEFORE UPDATE ON betting_odds 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
