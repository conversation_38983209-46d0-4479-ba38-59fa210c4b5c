"use client";

import React, { useState } from 'react';
import { <PERSON>ton, Card, Space, Typography } from 'antd';
import ProfileButton from './ProfileButton';
import ProfileUpdateModal from './ProfileUpdateModal';
import { useProfile } from '@/hooks/useProfile';

const { Title, Text } = Typography;

export default function ProfileButtonTest() {
  const [testModalVisible, setTestModalVisible] = useState(false);
  const { profile, loading } = useProfile();

  const handleTestModal = () => {
    console.log('Test modal button clicked');
    setTestModalVisible(true);
  };

  const handleCloseTestModal = () => {
    console.log('Closing test modal');
    setTestModalVisible(false);
  };

  const handleTestSuccess = (updatedProfile: any) => {
    console.log('Test modal success:', updatedProfile);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={3}>Profile Button Debug Test</Title>
        
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Profile Info */}
          <div>
            <Title level={5}>Current Profile Info:</Title>
            {loading ? (
              <Text>Loading...</Text>
            ) : profile ? (
              <div>
                <Text>Name: {profile.full_name}</Text><br />
                <Text>Email: {profile.email}</Text><br />
                <Text>Avatar: {profile.avatar_url || 'No avatar'}</Text>
              </div>
            ) : (
              <Text type="danger">No profile found</Text>
            )}
          </div>

          {/* ProfileButton Component */}
          <div>
            <Title level={5}>ProfileButton Component:</Title>
            <ProfileButton />
          </div>

          {/* Manual Test Button */}
          <div>
            <Title level={5}>Manual Test:</Title>
            <Button type="primary" onClick={handleTestModal}>
              Open Modal Manually
            </Button>
          </div>

          {/* Debug Info */}
          <div>
            <Title level={5}>Debug Info:</Title>
            <Text code>
              Profile loaded: {profile ? 'Yes' : 'No'}<br />
              Loading: {loading ? 'Yes' : 'No'}<br />
              Test Modal Visible: {testModalVisible ? 'Yes' : 'No'}
            </Text>
          </div>
        </Space>
      </Card>

      {/* Test Modal */}
      <ProfileUpdateModal
        visible={testModalVisible}
        onCancel={handleCloseTestModal}
        onSuccess={handleTestSuccess}
        currentProfile={profile}
      />
    </div>
  );
}
