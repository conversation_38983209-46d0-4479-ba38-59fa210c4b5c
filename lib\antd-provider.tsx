"use client";

import { App, ConfigProvider } from "antd";
import { ReactNode } from "react";

// Suppress React compatibility warnings
if (typeof window !== 'undefined') {
  const originalConsoleWarn = console.warn;
  console.warn = (...args) => {
    if (args[0]?.includes?.('[antd: compatible]')) {
      return;
    }
    originalConsoleWarn.apply(console, args);
  };
}

interface AntdProviderProps {
  children: ReactNode;
}

export function AntdProvider({ children }: AntdProviderProps) {
  return (
    <ConfigProvider
      theme={{
        token: {
          // Customize theme tokens here if needed
          colorPrimary: "#1890ff",
          borderRadius: 6,
        },
        components: {
          // Customize component styles here if needed
        },
      }}
    >
      <App>{children}</App>
    </ConfigProvider>
  );
}
