-- ========================================
-- QCLG CHAT SYSTEM - COMPLETE DATABASE MIGRATION
-- ========================================
-- This file contains all database schema migrations and fixes
-- Run this file to set up the complete database structure

-- ========================================
-- 1. CREATE USER PROFILES TABLE
-- ========================================

-- Create user profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  role TEXT NOT NULL CHECK (role IN ('admin', 'blv', 'member')) DEFAULT 'blv',
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Profiles policies (using auth.users properly)
CREATE POLICY "Users can view all profiles" ON public.profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- ========================================
-- 2. CREATE CHAT ROOMS TABLE
-- ========================================

-- Create chat rooms table
CREATE TABLE IF NOT EXISTS public.chat_rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('general', 'private', 'direct', 'group')) DEFAULT 'private',
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on chat_rooms
ALTER TABLE public.chat_rooms ENABLE ROW LEVEL SECURITY;

-- ========================================
-- 3. CREATE CHAT ROOM MEMBERS TABLE
-- ========================================

-- Create chat room members table
CREATE TABLE IF NOT EXISTS public.chat_room_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_id UUID REFERENCES public.chat_rooms(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(room_id, user_id)
);

-- Enable RLS on chat_room_members
ALTER TABLE public.chat_room_members ENABLE ROW LEVEL SECURITY;

-- Chat room members policies
CREATE POLICY "Users can view room members" ON public.chat_room_members FOR SELECT USING (
  auth.uid() = user_id OR
  EXISTS (
    SELECT 1 FROM public.chat_rooms cr 
    WHERE cr.id = room_id AND cr.type = 'general'
  )
);

CREATE POLICY "Users can join rooms" ON public.chat_room_members FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Chat rooms policies (moved after chat_room_members table creation)
CREATE POLICY "Users can view rooms they participate in" ON public.chat_rooms FOR SELECT USING (
  type = 'general' OR 
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM public.chat_room_members crm
    WHERE crm.room_id = chat_rooms.id AND crm.user_id = auth.uid()
  )
);

CREATE POLICY "Users can create rooms" ON public.chat_rooms FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Add DELETE policy for chat_rooms (allows users to delete rooms they created)
CREATE POLICY "Users can delete rooms they created" ON public.chat_rooms 
FOR DELETE USING (auth.uid() = created_by);

-- ========================================
-- 4. CREATE MESSAGES TABLE
-- ========================================

-- Create messages table (using public schema, not realtime)
CREATE TABLE IF NOT EXISTS public.messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_id UUID REFERENCES public.chat_rooms(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  message_type TEXT NOT NULL CHECK (message_type IN ('text', 'image', 'file')) DEFAULT 'text',
  status VARCHAR(20) DEFAULT 'sent',
  delivered_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on messages
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Fix foreign key to reference public.profiles instead of auth.users
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;
ALTER TABLE public.messages ADD CONSTRAINT messages_sender_id_fkey 
FOREIGN KEY (sender_id) REFERENCES public.profiles(id) ON DELETE SET NULL;

-- Messages policies (simplified to avoid infinite recursion)
CREATE POLICY "Users can view messages" ON public.messages FOR SELECT USING (true);
CREATE POLICY "Users can send messages" ON public.messages FOR INSERT WITH CHECK (true);

-- ========================================
-- 5. CREATE MESSAGE READS TABLE
-- ========================================

-- Create message_reads table to track who read which message
CREATE TABLE IF NOT EXISTS public.message_reads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(message_id, user_id)
);

-- Enable RLS on message_reads
ALTER TABLE public.message_reads ENABLE ROW LEVEL SECURITY;

-- RLS policies for message_reads
CREATE POLICY "Users can view message reads for their rooms" ON public.message_reads FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.messages m
    JOIN public.chat_room_members crm ON m.room_id = crm.room_id
    WHERE m.id = message_reads.message_id
    AND crm.user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own message reads" ON public.message_reads FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ========================================
-- 6. CREATE FILE ATTACHMENTS TABLE
-- ========================================

-- Create file attachments table
CREATE TABLE IF NOT EXISTS public.file_attachments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE,
  filename TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  uploaded_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on file_attachments
ALTER TABLE public.file_attachments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for file_attachments
CREATE POLICY "Users can view file attachments in their chat rooms" ON public.file_attachments FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.messages m
    JOIN public.chat_room_members crm ON m.room_id = crm.room_id
    WHERE m.id = file_attachments.message_id
    AND crm.user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert file attachments in their chat rooms" ON public.file_attachments FOR INSERT WITH CHECK (
  uploaded_by = auth.uid() AND
  EXISTS (
    SELECT 1 FROM public.messages m
    JOIN public.chat_room_members crm ON m.room_id = crm.room_id
    WHERE m.id = file_attachments.message_id
    AND crm.user_id = auth.uid()
  )
);

-- ========================================
-- 7. CREATE USER PRESENCE TABLE
-- ========================================

-- Create user presence table
CREATE TABLE IF NOT EXISTS public.user_presence (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  is_online BOOLEAN DEFAULT false,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on user_presence
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;

-- User presence policies
CREATE POLICY "Users can view all presence" ON public.user_presence FOR SELECT USING (true);
CREATE POLICY "Users can update own presence" ON public.user_presence FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own presence" ON public.user_presence FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ========================================
-- 8. CREATE INDEXES FOR PERFORMANCE
-- ========================================

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_room_id ON public.messages(room_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_room_members_room_id ON public.chat_room_members(room_id);
CREATE INDEX IF NOT EXISTS idx_chat_room_members_user_id ON public.chat_room_members(user_id);
CREATE INDEX IF NOT EXISTS idx_message_reads_message_id ON public.message_reads(message_id);
CREATE INDEX IF NOT EXISTS idx_message_reads_user_id ON public.message_reads(user_id);
CREATE INDEX IF NOT EXISTS idx_file_attachments_message_id ON public.file_attachments(message_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON public.user_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_is_online ON public.user_presence(is_online);
CREATE INDEX IF NOT EXISTS idx_chat_rooms_type ON public.chat_rooms(type);
CREATE INDEX IF NOT EXISTS idx_chat_rooms_updated_at ON public.chat_rooms(updated_at);

-- ========================================
-- 9. CREATE FUNCTIONS
-- ========================================

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    'blv'
  );
  
  -- Add to general room
  INSERT INTO public.chat_room_members (room_id, user_id)
  SELECT id, NEW.id FROM public.chat_rooms WHERE type = 'general';
  
  -- Set initial presence
  INSERT INTO public.user_presence (user_id, is_online, last_seen)
  VALUES (NEW.id, true, NOW());
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user presence
CREATE OR REPLACE FUNCTION public.update_user_presence(user_uuid UUID, online_status BOOLEAN)
RETURNS VOID AS $$
BEGIN
  INSERT INTO public.user_presence (user_id, is_online, last_seen, updated_at)
  VALUES (user_uuid, online_status, NOW(), NOW())
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    is_online = online_status,
    last_seen = NOW(),
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark message as delivered
CREATE OR REPLACE FUNCTION public.mark_message_delivered(message_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE public.messages 
  SET status = 'delivered', delivered_at = NOW()
  WHERE id = message_id AND status = 'sent';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark message as read
CREATE OR REPLACE FUNCTION public.mark_message_read(msg_id UUID, reader_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Insert read record
  INSERT INTO public.message_reads (message_id, user_id)
  VALUES (msg_id, reader_id)
  ON CONFLICT (message_id, user_id) DO NOTHING;
  
  -- Update message status if all room members have read it
  UPDATE public.messages 
  SET status = 'read', read_at = NOW()
  WHERE id = msg_id 
  AND status IN ('sent', 'delivered')
  AND (
    SELECT COUNT(*) FROM public.chat_room_members crm 
    WHERE crm.room_id = public.messages.room_id 
    AND crm.user_id != public.messages.sender_id
  ) = (
    SELECT COUNT(*) FROM public.message_reads mr 
    WHERE mr.message_id = msg_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get online users count
CREATE OR REPLACE FUNCTION public.get_online_users_count()
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*) 
    FROM public.user_presence 
    WHERE is_online = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get or create private room between two users
CREATE OR REPLACE FUNCTION public.get_or_create_private_room(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
  room_id UUID;
BEGIN
  -- Try to find existing private room between these users
  SELECT cr.id INTO room_id
  FROM public.chat_rooms cr
  JOIN public.chat_room_members crm1 ON cr.id = crm1.room_id
  JOIN public.chat_room_members crm2 ON cr.id = crm2.room_id
  WHERE cr.type = 'private'
  AND crm1.user_id = user1_id
  AND crm2.user_id = user2_id
  AND (
    SELECT COUNT(*) FROM public.chat_room_members WHERE room_id = cr.id
  ) = 2;
  
  -- If no room exists, create one
  IF room_id IS NULL THEN
    INSERT INTO public.chat_rooms (name, type, created_by)
    VALUES ('Private Chat', 'private', user1_id)
    RETURNING id INTO room_id;
    
    -- Add both users to the room
    INSERT INTO public.chat_room_members (room_id, user_id)
    VALUES (room_id, user1_id), (room_id, user2_id);
  END IF;
  
  RETURN room_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's accessible rooms (updated to support all room types)
CREATE OR REPLACE FUNCTION public.get_user_rooms(user_uuid UUID)
RETURNS TABLE (id UUID, name TEXT, type TEXT, updated_at TIMESTAMP WITH TIME ZONE) AS $$
BEGIN
  RETURN QUERY
  SELECT cr.id, cr.name, cr.type, cr.updated_at
  FROM public.chat_rooms cr
  WHERE cr.type = 'general' 
  OR (cr.type IN ('private', 'direct', 'group') AND EXISTS (
    SELECT 1 FROM public.chat_room_members crm 
    WHERE crm.room_id = cr.id AND crm.user_id = user_uuid
  ))
  ORDER BY cr.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's rooms with messages (updated to support all room types)
CREATE OR REPLACE FUNCTION public.get_user_rooms_with_messages(user_uuid UUID)
RETURNS TABLE (id UUID, name TEXT, type TEXT, updated_at TIMESTAMP WITH TIME ZONE, messages JSONB) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cr.id, 
    cr.name, 
    cr.type, 
    cr.updated_at,
    COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'content', m.content, 
          'created_at', m.created_at
        )
      ) FILTER (WHERE m.id IS NOT NULL), 
      '[]'::jsonb
    ) as messages
  FROM public.chat_rooms cr
  LEFT JOIN public.messages m ON cr.id = m.room_id
  WHERE cr.type = 'general' 
  OR (cr.type IN ('private', 'direct', 'group') AND EXISTS (
    SELECT 1 FROM public.chat_room_members crm 
    WHERE crm.room_id = cr.id AND crm.user_id = user_uuid
  ))
  GROUP BY cr.id, cr.name, cr.type, cr.updated_at
  ORDER BY cr.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for user accessible rooms (alternative to complex PostgREST queries)
CREATE OR REPLACE FUNCTION public.get_user_accessible_rooms(user_uuid UUID)
RETURNS TABLE (id UUID, name TEXT, type TEXT, updated_at TIMESTAMP WITH TIME ZONE) AS $$
BEGIN
  RETURN QUERY
  SELECT cr.id, cr.name, cr.type, cr.updated_at
  FROM public.chat_rooms cr
  WHERE cr.type = 'general' 
  OR (cr.type IN ('private', 'direct', 'group') AND EXISTS (
    SELECT 1 FROM public.chat_room_members crm 
    WHERE crm.room_id = cr.id AND crm.user_id = user_uuid
  ))
  ORDER BY cr.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 10. CREATE VIEWS
-- ========================================

-- View for user accessible rooms (alternative to complex PostgREST queries)
CREATE OR REPLACE VIEW public.user_accessible_rooms AS 
SELECT DISTINCT cr.id, cr.name, cr.type, cr.updated_at 
FROM public.chat_rooms cr 
WHERE cr.type = 'general' 
OR EXISTS (
  SELECT 1 FROM public.chat_room_members crm 
  WHERE crm.room_id = cr.id
);

-- ========================================
-- 11. CREATE TRIGGERS
-- ========================================

-- Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ========================================
-- 12. GRANT PERMISSIONS
-- ========================================

-- Grant schema usage
GRANT USAGE ON SCHEMA public TO anon, authenticated;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO anon, authenticated;

-- Grant view permissions
GRANT SELECT ON public.user_accessible_rooms TO anon, authenticated;

-- Grant function permissions
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- ========================================
-- 13. ENABLE REALTIME
-- ========================================

-- Enable realtime for all tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.profiles;
ALTER PUBLICATION supabase_realtime ADD TABLE public.chat_rooms;
ALTER PUBLICATION supabase_realtime ADD TABLE public.chat_room_members;
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_reads;
ALTER PUBLICATION supabase_realtime ADD TABLE public.file_attachments;
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_presence;


ALTER TABLE public.user_presence 
ADD CONSTRAINT user_presence_profile_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- Update the existing foreign key constraint to also reference profiles
-- This ensures the relationship is properly established
ALTER TABLE public.user_presence 
DROP CONSTRAINT IF EXISTS user_presence_user_id_fkey;

-- Re-add the constraint with both references
ALTER TABLE public.user_presence 
ADD CONSTRAINT user_presence_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add a separate constraint for profiles relationship
ALTER TABLE public.user_presence 
ADD CONSTRAINT user_presence_profile_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


-- ========================================
-- MIGRATION COMPLETE
-- ========================================

DO $$
BEGIN
  RAISE NOTICE '🎉 Database migration completed successfully!';
  RAISE NOTICE '';
  RAISE NOTICE '✅ Created 7 tables with proper relationships';
  RAISE NOTICE '✅ RLS policies configured for security';
  RAISE NOTICE '✅ Permissions granted to anon/authenticated roles';
  RAISE NOTICE '✅ Realtime enabled for all tables';
  RAISE NOTICE '✅ 9 functions created for automation';
  RAISE NOTICE '✅ 1 view created for complex queries';
  RAISE NOTICE '✅ Triggers for user registration';
  RAISE NOTICE '✅ 12 indexes for optimal performance';
  RAISE NOTICE '✅ Support for all room types: general, private, direct, group';
  RAISE NOTICE '✅ Support for all user roles: admin, blv, member';
  RAISE NOTICE '✅ DELETE policy for chat rooms';
  RAISE NOTICE '';
  RAISE NOTICE 'Next: Run the seed file to add sample data';
END $$;


