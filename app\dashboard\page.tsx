"use client";

import { useAuthService } from "@/hooks";
import {
  CalendarOutlined,
  ClockCircleOutlined,
  DashboardOutlined,
  FileTextOutlined,
  LogoutOutlined,
  MessageOutlined,
  RiseOutlined,
  SettingOutlined,
  TagsOutlined,
  TeamOutlined,
  TrophyOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Avatar, Dropdown, Layout, Typography } from "antd";
import { usePathname, useRouter } from "next/navigation";
import React, { useEffect } from "react";
import {
  DashboardWrapper,
  DashboardLogoIcon as LogoIcon,
  DashboardLogoSection as LogoSection,
  DashboardMenuContainer as MenuContainer,
  DashboardStyledContent as <PERSON>d<PERSON>ontent,
  DashboardStyledHeader as StyledHeader,
  StyledMenu,
  DashboardStyledSider as StyledSider,
  UserSection,
} from "../../styles";

const { Title, Text } = Typography;

export default function DashboardPage({ children }: { children?: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, profile, loading, signOut } = useAuthService();

  // Get current menu from pathname
  const getCurrentMenu = () => {
    if (pathname === "/dashboard") return "dashboard";
    if (pathname.startsWith("/dashboard/chat")) return "chat";
    if (pathname.startsWith("/dashboard/matches")) return "matches";
    if (pathname.startsWith("/dashboard/articles")) return "articles";
    if (pathname.startsWith("/dashboard/news")) return "news";
    if (pathname.startsWith("/dashboard/categories")) return "categories";
    if (pathname.startsWith("/dashboard/schedule")) return "schedule";
    if (pathname.startsWith("/dashboard/competition")) return "competition";
    if (pathname.startsWith("/dashboard/betting-odds")) return "betting-odds";
    if (pathname.startsWith("/dashboard/users")) return "users";
    if (pathname.startsWith("/dashboard/settings")) return "settings";
    return "dashboard";
  };

  const currentMenu = getCurrentMenu();

  const handleLogout = async () => {
    try {
      await signOut();
      router.push("/auth/login");
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  const handleMenuClick = (key: string) => {
    switch (key) {
      case "chat":
        router.push("/dashboard/chat");
        break;
      case "matches":
        router.push("/dashboard/matches");
        break;
      case "articles":
        router.push("/dashboard/articles");
        break;
      case "schedule":
        router.push("/dashboard/schedule");
        break;
      case "competition":
        router.push("/dashboard/competition");
        break;
      case "news":
        router.push("/dashboard/news");
        break;
      case "categories":
        router.push("/dashboard/categories");
        break;
      case "betting-odds":
        router.push("/dashboard/betting-odds");
        break;
      case "users":
        router.push("/dashboard/users");
        break;
      case "settings":
        router.push("/dashboard/settings");
        break;
      default:
        router.push("/dashboard");
    }
  };

  // Redirect to chat by default if on root dashboard
  useEffect(() => {
    if (pathname === "/dashboard") {
      router.push("/dashboard/chat");
    }
  }, [pathname, router]);

  const userMenuItems = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "Hồ sơ",
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "Đăng xuất",
      onClick: handleLogout,
    },
  ];

  const menuItems = [
    {
      key: "chat",
      icon: <MessageOutlined />,
      label: "Chat",
    },
    {
      key: "matches",
      icon: <TrophyOutlined />,
      label: "Trận đấu",
    },
    {
      key: "articles",
      icon: <FileTextOutlined />,
      label: "Bài viết",
    },
    {
      key: "news",
      icon: <FileTextOutlined />,
      label: "Quản lý tin tức",
    },
    {
      key: "categories",
      icon: <TagsOutlined />,
      label: "Danh mục",
    },
    {
      key: "schedule",
      icon: <CalendarOutlined />,
      label: "Lịch làm việc",
    },
    {
      key: "competition",
      icon: <ClockCircleOutlined />,
      label: "Lịch thi đấu",
    },
    {
      key: "betting-odds",
      icon: <RiseOutlined />,
      label: "Quản lý Kèo",
    },
    // Show user manager to administrators only
    ...(profile?.role === "admin" ? [{
      key: "users",
      icon: <TeamOutlined />,
      label: "Quản lý Users",
    }] : []),
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "Setting",
    },
  ];

  if (loading) {
    return (
      <div
        style={{
          height: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div>Loading...</div>
      </div>
    );
  }

  if (!user || !profile) {
    return null;
  }

  return (
    <DashboardWrapper>
      <StyledHeader>
        <LogoSection>
          <LogoIcon>T</LogoIcon>
          <Title level={4} style={{ margin: 0, color: "#1f2937" }}>
            Tenan Backoffice
          </Title>
        </LogoSection>

        <UserSection>
          <Text>Xin chào, {profile.full_name || user.email}</Text>
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <Avatar
              src={profile.avatar_url}
              icon={<UserOutlined />}
              style={{ cursor: "pointer" }}
            />
          </Dropdown>
        </UserSection>
      </StyledHeader>

      <Layout>
        <StyledSider width={250}>
          <MenuContainer>
            <StyledMenu
              mode="inline"
              selectedKeys={[currentMenu]}
              items={menuItems}
              onClick={({ key }) => handleMenuClick(key)}
            />
          </MenuContainer>
        </StyledSider>

        <StyledContent>
          {children}
        </StyledContent>
      </Layout>
    </DashboardWrapper>
  );
}
