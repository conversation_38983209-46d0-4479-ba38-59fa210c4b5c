export interface Match {
  id: string;
  league_id?: string;
  home_team_id?: string;
  away_team_id?: string;
  home_team?: string; // For display purposes
  away_team?: string; // For display purposes
  home_score?: number;
  away_score?: number;
  match_date: string; // timestamp without time zone
  stadium?: string; // venue in database
  status: "scheduled" | "live" | "finished" | "cancelled" | "postponed";
  competition?: string; // Not in database, might be derived
  round?: string;
  referee?: string;
  weather?: string;
  created_at: string;
  updated_at: string;
  home_team_logo?: string;
  away_team_logo?: string;
  match_notes?: string;
  live_url?: string;
  highlights_url?: string;
}

export interface MatchFilters {
  search?: string;
  status?: "scheduled" | "live" | "finished" | "cancelled" | "postponed";
  competition?: string;
  date_from?: string;
  date_to?: string;
  team?: string;
  sort_by?: "match_date" | "created_at" | "home_team" | "away_team" | "status";
  sort_order?: "asc" | "desc";
  page?: number;
  limit?: number;
}

export interface MatchResponse {
  matches: Match[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface MatchStats {
  total_matches: number;
  scheduled_matches: number;
  live_matches: number;
  finished_matches: number;
  cancelled_matches: number;
  postponed_matches: number;
  matches_today: number;
  matches_this_week: number;
  matches_this_month: number;
  total_goals: number;
}

export interface CreateMatchData {
  home_team: string;
  away_team: string;
  match_date: string;
  match_time: string;
  venue: string;
  competition: string;
  round?: string;
  referee?: string;
  weather?: string;
  home_team_logo?: string;
  away_team_logo?: string;
  match_notes?: string;
  live_url?: string;
}

export interface UpdateMatchData {
  home_team?: string;
  away_team?: string;
  home_score?: number;
  away_score?: number;
  match_date?: string;
  match_time?: string;
  venue?: string;
  status?: "scheduled" | "live" | "finished" | "cancelled" | "postponed";
  competition?: string;
  round?: string;
  referee?: string;
  weather?: string;
  home_team_logo?: string;
  away_team_logo?: string;
  match_notes?: string;
  live_url?: string;
  highlights_url?: string;
}

export interface ScheduleFilters {
  date_from?: string;
  date_to?: string;
  competition?: string;
  sport_id?: string;
  team?: string;
  venue?: string;
  status?: "scheduled" | "live" | "finished" | "cancelled" | "postponed";
  view_type?: "day" | "week" | "month";
}

export interface ScheduleResponse {
  matches: Match[];
  total: number;
  date_range: {
    from: string;
    to: string;
  };
  view_type: "day" | "week" | "month";
}

export interface MatchEvent {
  id: string;
  match_id: string;
  event_type:
    | "goal"
    | "yellow_card"
    | "red_card"
    | "substitution"
    | "penalty"
    | "own_goal"
    | "var_decision";
  player_name: string;
  team: "home" | "away";
  minute: number;
  description?: string;
  created_at: string;
}

export interface MatchCommentary {
  id: string;
  match_id: string;
  minute: number;
  commentary: string;
  event_type?: "goal" | "card" | "substitution" | "general";
  created_at: string;
}

export interface LiveMatchData {
  match: Match;
  events: MatchEvent[];
  commentary: MatchCommentary[];
  last_updated: string;
}
