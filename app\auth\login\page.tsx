"use client";

import { useAuthService } from "@/hooks";
import {
  FormSection,
  AuthGoogleButton as GoogleButton,
  InputGroup,
  Label,
  LoginCard,
  LoginContainer,
  AuthLogoIcon as LogoIcon,
  AuthLogoSection as LogoSection,
} from "@/styles";
import { GoogleOutlined, LockOutlined, UserOutlined } from "@ant-design/icons";
import { App, Button, Divider, Form, Input, Typography } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";

const { Title, Text } = Typography;

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const router = useRouter();
  const { signIn, signInWithGoogle } = useAuthService();
  const { message } = App.useApp();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      message.error("Vui lòng nhập đầy đủ thông tin");
      return;
    }

    setLoading(true);

    try {
      const { user, error } = await signIn({ email, password });

      if (error) {
        message.error(error.message);
      } else {
        message.success("Đăng nhập thành công!");
        router.push("/dashboard");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi đăng nhập");
    } finally {
      setLoading(false);
    }
  };


  const handleGoogleRegister = async () => {
    setGoogleLoading(true);
    try {
      const { error } = await signInWithGoogle();

      if (error) throw error;
    } catch (error: any) {
      message.error(error.message || "Đăng ký Google thất bại");
      setGoogleLoading(false);
    }
  };


  return (
    <LoginContainer>
      <LoginCard>
        <LogoSection>
          <Title level={2} style={{ margin: 0, color: "#1f2937" }}>
            Tenan Backoffice
          </Title>
          <Text type="secondary">Đăng nhập để tiếp tục</Text>
        </LogoSection>

        <FormSection>
          <form onSubmit={handleLogin}>
            <InputGroup>
              <Label>Email</Label>
              <Input
                prefix={<UserOutlined />}
                placeholder="Nhập email của bạn"
                value={email}
                onChange={e => setEmail(e.target.value)}
                type="email"
              />
            </InputGroup>

            <InputGroup>
              <Label>Mật khẩu</Label>
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Nhập mật khẩu"
                value={password}
                onChange={e => setPassword(e.target.value)}
              />
            </InputGroup>

            <Button type="primary" htmlType="submit" loading={loading} block>
              Đăng nhập
            </Button>
          </form>
        </FormSection>
      </LoginCard>
    </LoginContainer>
  );
}
