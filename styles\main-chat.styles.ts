import styled from "styled-components";

export const ChatContainer = styled.div`
  flex-direction: column;
  height: 100%;
  display: grid;
`;

export const ChatHeader = styled.div`
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .chat-title {
    font-size: 18px;
    font-weight: 600;
    color: #262626;
    margin: 0;
  }

  .chat-subtitle {
    color: #8c8c8c;
    font-size: 12px;
    margin-top: 4px;
  }
`;

export const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  background: #f9f9f9;
  max-height: calc(100vh - 200px);
  scroll-behavior: smooth;
  position: relative;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

export const MessageItem = styled.div<{ $isOwn: boolean }>`
  display: flex;
  margin-bottom: 16px;
  justify-content: ${props => (props.$isOwn ? "flex-end" : "flex-start")};

  .message-content {
    max-width: 70%;
    display: flex;
    flex-direction: ${props => (props.$isOwn ? "row-reverse" : "row")};
    align-items: flex-start;
    gap: 8px;
  }

  .message-bubble {
    background: ${props => (props.$isOwn ? "#1890ff" : "#fff")};
    color: ${props => (props.$isOwn ? "#fff" : "#262626")};
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
  }

  .message-info {
    font-size: 11px;
    color: #8c8c8c;
    margin-top: 4px;
    text-align: ${props => (props.$isOwn ? "right" : "left")};
  }
`;

export const InputContainer = styled.div`
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fff;

  .input-wrapper {
    display: flex;
    gap: 8px;
    align-items: flex-end;
  }

  .message-input {
    flex: 1;
  }
`;
