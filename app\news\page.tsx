'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, Typography, Row, Col, Input, Spin, Empty, Tag, Image, Pagination } from 'antd';
import { SearchOutlined, CalendarOutlined, UserOutlined, EyeOutlined } from '@ant-design/icons';
import { useNewsService } from '@/hooks/useNewsService';
import { PublishedPostWithSeo } from '@/types/news.types';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

export default function NewsListPublicPage() {
  const router = useRouter();
  const newsService = useNewsService();
  const [loading, setLoading] = useState(true);
  const [posts, setPosts] = useState<PublishedPostWithSeo[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<PublishedPostWithSeo[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(9);

  useEffect(() => {
    fetchPublishedPosts();
  }, []);

  useEffect(() => {
    if (searchTerm) {
      const filtered = posts.filter(post => 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (post.description && post.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredPosts(filtered);
    } else {
      setFilteredPosts(posts);
    }
    setCurrentPage(1);
  }, [searchTerm, posts]);

  const fetchPublishedPosts = async () => {
    try {
      setLoading(true);
      const publishedPosts = await newsService.fetchPublishedPosts();
      setPosts(publishedPosts);
      setFilteredPosts(publishedPosts);
    } catch (error) {
      console.error('Error fetching published posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePostClick = (slug: string) => {
    router.push(`/news/${slug}`);
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Get current page posts
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentPosts = filteredPosts.slice(startIndex, endIndex);

  const truncateContent = (content: string, maxLength: number = 150) => {
    const textContent = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
    return textContent.length > maxLength 
      ? textContent.substring(0, maxLength) + '...' 
      : textContent;
  };

  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{ textAlign: 'center' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', color: '#666' }}>Đang tải tin tức...</div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f5f5f5',
      padding: '24px 0'
    }}>
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto',
        padding: '0 24px'
      }}>
        {/* Header */}
        <div style={{ 
          textAlign: 'center', 
          marginBottom: '48px',
          backgroundColor: 'white',
          padding: '48px 24px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <Title level={1} style={{ 
            marginBottom: '16px',
            color: '#262626',
            fontSize: '48px'
          }}>
            <EyeOutlined style={{ marginRight: '16px', color: '#1890ff' }} />
            Tin Tức
          </Title>
          <Paragraph style={{ 
            fontSize: '18px', 
            color: '#666',
            maxWidth: '600px',
            margin: '0 auto 32px'
          }}>
            Cập nhật những tin tức mới nhất và thông tin hữu ích
          </Paragraph>
          
          <Search
            placeholder="Tìm kiếm tin tức..."
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            style={{ maxWidth: '500px' }}
            onSearch={handleSearch}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Search Results Info */}
        {searchTerm && (
          <div style={{ marginBottom: '24px' }}>
            <Text style={{ color: '#666', fontSize: '16px' }}>
              Tìm thấy <strong>{filteredPosts.length}</strong> kết quả cho "{searchTerm}"
            </Text>
          </div>
        )}

        {/* Posts Grid */}
        {currentPosts.length === 0 ? (
          <Empty 
            description="Không có tin tức nào"
            style={{ 
              backgroundColor: 'white',
              padding: '48px',
              borderRadius: '12px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}
          />
        ) : (
          <>
            <Row gutter={[24, 24]}>
              {currentPosts.map((post) => (
                <Col xs={24} sm={12} lg={8} key={post.post_id}>
                  <Card
                    hoverable
                    style={{ 
                      height: '100%',
                      borderRadius: '12px',
                      overflow: 'hidden',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      transition: 'all 0.3s ease'
                    }}
                    bodyStyle={{ 
                      padding: '0',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column'
                    }}
                    onClick={() => post.slug && handlePostClick(post.slug)}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-4px)';
                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                    }}
                  >
                    {/* Thumbnail */}
                    <div style={{ 
                      height: '200px', 
                      overflow: 'hidden',
                      position: 'relative'
                    }}>
                      <Image
                        src={post.thumbnail || '/images/default-news.jpg'}
                        alt={post.title}
                        style={{ 
                          width: '100%', 
                          height: '100%', 
                          objectFit: 'cover'
                        }}
                        fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                        preview={false}
                      />
                      
                      {/* Category Tag */}
                      <div style={{
                        position: 'absolute',
                        top: '12px',
                        left: '12px'
                      }}>
                        <Tag color="blue" style={{ 
                          borderRadius: '6px',
                          fontWeight: '500'
                        }}>
                          Tin tức
                        </Tag>
                      </div>
                    </div>

                    {/* Content */}
                    <div style={{ 
                      padding: '20px',
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'column'
                    }}>
                      <Title 
                        level={4} 
                        style={{ 
                          marginBottom: '12px',
                          fontSize: '18px',
                          lineHeight: '1.4',
                          color: '#262626',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {post.title}
                      </Title>

                      {post.description && (
                        <Paragraph 
                          style={{ 
                            color: '#666',
                            fontSize: '14px',
                            marginBottom: '16px',
                            flex: 1,
                            display: '-webkit-box',
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden'
                          }}
                        >
                          {post.description}
                        </Paragraph>
                      )}

                      {!post.description && (
                        <Paragraph 
                          style={{ 
                            color: '#666',
                            fontSize: '14px',
                            marginBottom: '16px',
                            flex: 1,
                            display: '-webkit-box',
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden'
                          }}
                        >
                          {truncateContent(post.content)}
                        </Paragraph>
                      )}

                      {/* Meta Info */}
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        paddingTop: '16px',
                        borderTop: '1px solid #f0f0f0',
                        marginTop: 'auto'
                      }}>
                        <div style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          gap: '8px',
                          color: '#666',
                          fontSize: '12px'
                        }}>
                          <UserOutlined />
                          <Text style={{ color: '#666', fontSize: '12px' }}>
                            {post.author_name || 'Admin'}
                          </Text>
                        </div>
                        
                        <div style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          gap: '8px',
                          color: '#666',
                          fontSize: '12px'
                        }}>
                          <CalendarOutlined />
                          <Text style={{ color: '#666', fontSize: '12px' }}>
                            {dayjs(post.published_at).format('DD/MM/YYYY')}
                          </Text>
                        </div>
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>

            {/* Pagination */}
            {filteredPosts.length > pageSize && (
              <div style={{ 
                textAlign: 'center', 
                marginTop: '48px',
                padding: '24px',
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}>
                <Pagination
                  current={currentPage}
                  total={filteredPosts.length}
                  pageSize={pageSize}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) => 
                    `${range[0]}-${range[1]} của ${total} bài viết`
                  }
                  style={{ fontSize: '16px' }}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}