"use client";

import type React from "react";
import { createContext, useContext, useCallback } from "react";
import { notification } from "antd";
import type { NotificationArgsProps } from "antd";

interface NotificationContextType {
  showNotification: (config: NotificationArgsProps) => void;
  showMessageNotification: (message: string, sender: string) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function NotificationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [api, contextHolder] = notification.useNotification();

  const showNotification = useCallback(
    (config: NotificationArgsProps) => {
      api.open(config);
    },
    [api]
  );

  const showMessageNotification = useCallback(
    (message: string, sender: string) => {
      api.open({
        message: `Tin nhắn mới từ ${sender}`,
        description:
          message.length > 50 ? `${message.substring(0, 50)}...` : message,
        placement: "topRight",
        duration: 4,
        style: {
          borderLeft: "4px solid #1890ff",
        },
      });
    },
    [api]
  );

  return (
    <NotificationContext.Provider
      value={{ showNotification, showMessageNotification }}
    >
      {contextHolder}
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotification() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error("useNotification must be used within NotificationProvider");
  }
  return context;
}
