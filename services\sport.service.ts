import { createClient } from "@/lib/supabase/client";

export interface Sport {
  sport_id: string;
  name: string;
  is_football: boolean;
  description?: string;
  rules?: string;
  created_at: string;
  updated_at: string;
}

export interface SportResponse {
  data: Sport[];
  error: string | null;
}

class SportService {
  private supabase;

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }

  // Static method to create instance with default client for backward compatibility
  static createWithDefaultClient() {
    return new SportService(createClient());
  }

  /**
   * L<PERSON><PERSON> danh s<PERSON>ch tất cả môn thể thao
   */
  async getSports(): Promise<SportResponse> {
    try {
      const { data, error } = await this.supabase
        .from("sports")
        .select("*")
        .order("name", { ascending: true });

      if (error) {
        console.error("Error fetching sports:", error);
        return { data: [], error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error("Error in getSports:", error);
      return {
        data: [],
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * <PERSON><PERSON><PERSON> môn thể thao theo ID
   */
  async getSportById(
    sportId: string
  ): Promise<{ data: Sport | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from("sports")
        .select("*")
        .eq("sport_id", sportId)
        .single();

      if (error) {
        console.error("Error fetching sport by ID:", error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error("Error in getSportById:", error);
      return {
        data: null,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Tạo môn thể thao mới
   */
  async createSport(
    sportData: Omit<Sport, "sport_id" | "created_at" | "updated_at">
  ): Promise<{ data: Sport | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from("sports")
        .insert([sportData])
        .select()
        .single();

      if (error) {
        console.error("Error creating sport:", error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error("Error in createSport:", error);
      return {
        data: null,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Cập nhật môn thể thao
   */
  async updateSport(
    sportId: string,
    updates: Partial<Omit<Sport, "sport_id" | "created_at" | "updated_at">>
  ): Promise<{ data: Sport | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from("sports")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("sport_id", sportId)
        .select()
        .single();

      if (error) {
        console.error("Error updating sport:", error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error("Error in updateSport:", error);
      return {
        data: null,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Xóa môn thể thao
   */
  async deleteSport(sportId: string): Promise<{ error: string | null }> {
    try {
      const { error } = await this.supabase
        .from("sports")
        .delete()
        .eq("sport_id", sportId);

      if (error) {
        console.error("Error deleting sport:", error);
        return { error: error.message };
      }

      return { error: null };
    } catch (error) {
      console.error("Error in deleteSport:", error);
      return {
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}

export const sportService = SportService.createWithDefaultClient();
