"use client";

import { useAuthService } from "@/hooks";
import {
  Auth<PERSON>utton,
  DividerText,
  LoginLink,
  AuthRegisterCard as RegisterCard,
  RegisterContainer,
  RegisterFormSection,
} from "@/styles";
import {
  LockOutlined,
  MailOutlined,
  UserOutlined
} from "@ant-design/icons";
import { App, Form, Input, Select } from "antd";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  role: "admin" | "blv";
}

export default function RegisterPage() {
  const [loading, setLoading] = useState(false);

  const router = useRouter();
  const { signUp } = useAuthService();
  const { message } = App.useApp();

  const handleRegister = async (values: RegisterForm) => {
    setLoading(true);
    try {
      const { user, error } = await signUp({
        email: values.email,
        password: values.password,
        fullName: values.fullName,
        role: values.role,
      });

      if (error) throw error;

      message.success(
        "Đăng ký thành công! Vui lòng kiểm tra email để xác nhận tài khoản."
      );
      router.push("/auth/verify-email");
    } catch (error: any) {
      message.error(error.message || "Đăng ký thất bại");
    } finally {
      setLoading(false);
    }
  };

  return (
    <RegisterContainer>
      <RegisterCard title="Đăng Ký Tài Khoản">
        <RegisterFormSection>
          <Form onFinish={handleRegister} layout="vertical">
            <Form.Item
              name="fullName"
              rules={[{ required: true, message: "Vui lòng nhập họ tên!" }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Họ và tên"
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="email"
              rules={[
                { required: true, message: "Vui lòng nhập email!" },
                { type: "email", message: "Email không hợp lệ!" },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Email"
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="role"
              rules={[{ required: true, message: "Vui lòng chọn vai trò!" }]}
            >
              <Select placeholder="Chọn vai trò" size="large">
                <Select.Option value="blv">Bình luận viên (BLV)</Select.Option>
                <Select.Option value="admin">
                  Quản trị viên (Admin)
                </Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: "Vui lòng nhập mật khẩu!" },
                { min: 6, message: "Mật khẩu phải có ít nhất 6 ký tự!" },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Mật khẩu"
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              dependencies={["password"]}
              rules={[
                { required: true, message: "Vui lòng xác nhận mật khẩu!" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue("password") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(
                      new Error("Mật khẩu xác nhận không khớp!")
                    );
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Xác nhận mật khẩu"
                size="large"
              />
            </Form.Item>

            <Form.Item>
              <AuthButton
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
              >
                Đăng Ký
              </AuthButton>
            </Form.Item>

            <div style={{ textAlign: "center", marginTop: "20px" }}>
              <DividerText>
                Đã có tài khoản?{" "}
                <Link href="/auth/login">
                  <LoginLink>Đăng nhập ngay</LoginLink>
                </Link>
              </DividerText>
            </div>
          </Form>
        </RegisterFormSection>
      </RegisterCard>
    </RegisterContainer>
  );
}
