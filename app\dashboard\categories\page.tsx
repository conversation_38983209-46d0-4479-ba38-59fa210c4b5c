"use client";

import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Switch,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Image,
  Card,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  fetchCategoriesWithPostCount,
  createCategory,
  updateCategory,
  deleteCategory,
  toggleCategoryStatus,
  checkSlugAvailability,
} from '@/lib/api/categories';
import type {
  CategoryWithPostCount,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryFilters,
} from '@/types';

const { TextArea } = Input;
const { Search } = Input;

export default function CategoriesPage() {
  const [categories, setCategories] = useState<CategoryWithPostCount[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<CategoryWithPostCount | null>(null);
  const [form] = Form.useForm();
  const [filters, setFilters] = useState<CategoryFilters>({});

  // Statistics
  const totalCategories = categories.length;
  const activeCategories = categories.filter(cat => cat.is_active).length;
  const totalPosts = categories.reduce((sum, cat) => sum + cat.post_count, 0);

  // Load categories
  const loadCategories = async () => {
    setLoading(true);
    try {
      const data = await fetchCategoriesWithPostCount(filters);
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
      message.error('Không thể tải danh sách danh mục');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, [filters]);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value || undefined }));
  };

  // Handle create/edit category
  const handleSubmit = async (values: any) => {
    try {
      // Check slug availability
      const isSlugAvailable = await checkSlugAvailability(
        values.slug,
        editingCategory?.id
      );
      
      if (!isSlugAvailable) {
        message.error('Slug đã tồn tại, vui lòng chọn slug khác');
        return;
      }

      if (editingCategory) {
        // Update category
        const updateData: UpdateCategoryRequest = {
          name: values.name,
          slug: values.slug,
          description: values.description,
          thumbnail: values.thumbnail,
          is_active: values.is_active,
          sort_order: values.sort_order,
        };
        await updateCategory(editingCategory.id, updateData);
        message.success('Cập nhật danh mục thành công');
      } else {
        // Create category
        const createData: CreateCategoryRequest = {
          name: values.name,
          slug: values.slug,
          description: values.description,
          thumbnail: values.thumbnail,
          is_active: values.is_active ?? true,
          sort_order: values.sort_order ?? 0,
        };
        await createCategory(createData);
        message.success('Tạo danh mục thành công');
      }

      setModalVisible(false);
      form.resetFields();
      setEditingCategory(null);
      loadCategories();
    } catch (error) {
      console.error('Error saving category:', error);
      message.error('Có lỗi xảy ra khi lưu danh mục');
    }
  };

  // Handle delete category
  const handleDelete = async (id: string) => {
    try {
      await deleteCategory(id);
      message.success('Xóa danh mục thành công');
      loadCategories();
    } catch (error: any) {
      console.error('Error deleting category:', error);
      if (error.message.includes('being used by posts')) {
        message.error('Không thể xóa danh mục đang được sử dụng bởi bài viết');
      } else {
        message.error('Có lỗi xảy ra khi xóa danh mục');
      }
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      await toggleCategoryStatus(id, isActive);
      message.success(`${isActive ? 'Kích hoạt' : 'Vô hiệu hóa'} danh mục thành công`);
      loadCategories();
    } catch (error) {
      console.error('Error toggling category status:', error);
      message.error('Có lỗi xảy ra khi thay đổi trạng thái danh mục');
    }
  };

  // Open modal for create/edit
  const openModal = (category?: CategoryWithPostCount) => {
    if (category) {
      setEditingCategory(category);
      form.setFieldsValue({
        name: category.name,
        slug: category.slug,
        description: category.description,
        thumbnail: category.thumbnail,
        is_active: category.is_active,
        sort_order: category.sort_order,
      });
    } else {
      setEditingCategory(null);
      form.resetFields();
      form.setFieldsValue({ is_active: true, sort_order: 0 });
    }
    setModalVisible(true);
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  // Table columns
  const columns: ColumnsType<CategoryWithPostCount> = [
    {
      title: 'Hình ảnh',
      dataIndex: 'thumbnail',
      key: 'thumbnail',
      width: 80,
      render: (thumbnail: string) => (
        thumbnail ? (
          <Image
            src={thumbnail}
            alt="Category thumbnail"
            width={50}
            height={50}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div style={{ width: 50, height: 50, backgroundColor: '#f0f0f0', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <EyeOutlined style={{ color: '#ccc' }} />
          </div>
        )
      ),
    },
    {
      title: 'Tên danh mục',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Slug',
      dataIndex: 'slug',
      key: 'slug',
      render: (slug: string) => (
        <code style={{ backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: 3 }}>
          {slug}
        </code>
      ),
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string) => description || <span style={{ color: '#ccc' }}>Chưa có mô tả</span>,
    },
    {
      title: 'Số bài viết',
      dataIndex: 'post_count',
      key: 'post_count',
      width: 120,
      sorter: (a, b) => a.post_count - b.post_count,
      render: (count: number) => (
        <Tag color={count > 0 ? 'blue' : 'default'}>
          {count} bài
        </Tag>
      ),
    },
    {
      title: 'Thứ tự',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 100,
      sorter: (a, b) => a.sort_order - b.sort_order,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 120,
      render: (isActive: boolean, record) => (
        <Switch
          checked={isActive}
          onChange={(checked) => handleToggleStatus(record.id, checked)}
          checkedChildren="Hoạt động"
          unCheckedChildren="Tạm dừng"
        />
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
          >
            Sửa
          </Button>
          <Popconfirm
            title="Xóa danh mục"
            description="Bạn có chắc chắn muốn xóa danh mục này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Xóa"
            cancelText="Hủy"
            disabled={record.post_count > 0}
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
              disabled={record.post_count > 0}
            >
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <h1>Quản lý Danh mục</h1>
      
      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng danh mục"
              value={totalCategories}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Danh mục hoạt động"
              value={activeCategories}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng bài viết"
              value={totalPosts}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tỷ lệ hoạt động"
              value={totalCategories > 0 ? Math.round((activeCategories / totalCategories) * 100) : 0}
              suffix="%"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Actions */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Search
          placeholder="Tìm kiếm danh mục..."
          allowClear
          onSearch={handleSearch}
          style={{ width: 300 }}
          prefix={<SearchOutlined />}
        />
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => openModal()}
        >
          Thêm danh mục
        </Button>
      </div>

      {/* Table */}
      <Table
        columns={columns}
        dataSource={categories}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} danh mục`,
        }}
      />

      {/* Create/Edit Modal */}
      <Modal
        title={editingCategory ? 'Chỉnh sửa danh mục' : 'Thêm danh mục mới'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingCategory(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true, sort_order: 0 }}
        >
          <Form.Item
            name="name"
            label="Tên danh mục"
            rules={[
              { required: true, message: 'Vui lòng nhập tên danh mục' },
              { min: 2, message: 'Tên danh mục phải có ít nhất 2 ký tự' },
              { max: 100, message: 'Tên danh mục không được quá 100 ký tự' },
            ]}
          >
            <Input
              placeholder="Nhập tên danh mục"
              onChange={(e) => {
                const slug = generateSlug(e.target.value);
                form.setFieldsValue({ slug });
              }}
            />
          </Form.Item>

          <Form.Item
            name="slug"
            label="Slug (URL thân thiện)"
            rules={[
              { required: true, message: 'Vui lòng nhập slug' },
              { pattern: /^[a-z0-9-]+$/, message: 'Slug chỉ được chứa chữ thường, số và dấu gạch ngang' },
            ]}
          >
            <Input placeholder="vi-du-slug" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Mô tả"
            rules={[
              { max: 500, message: 'Mô tả không được quá 500 ký tự' },
            ]}
          >
            <TextArea
              rows={3}
              placeholder="Nhập mô tả cho danh mục (tùy chọn)"
            />
          </Form.Item>

          <Form.Item
            name="thumbnail"
            label="Hình ảnh đại diện"
            rules={[
              { type: 'url', message: 'Vui lòng nhập URL hợp lệ' },
            ]}
          >
            <Input placeholder="https://example.com/image.jpg" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="sort_order"
                label="Thứ tự sắp xếp"
                rules={[
                  { type: 'number', min: 0, message: 'Thứ tự phải là số không âm' },
                ]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="Trạng thái"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="Hoạt động"
                  unCheckedChildren="Tạm dừng"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                  setEditingCategory(null);
                }}
              >
                Hủy
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCategory ? 'Cập nhật' : 'Tạo mới'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}