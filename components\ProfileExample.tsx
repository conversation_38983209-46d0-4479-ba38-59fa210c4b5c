"use client";

import React from 'react';
import { Card, Space, Typography, Divider } from 'antd';
import ProfileButton from './ProfileButton';

const { Title, Text } = Typography;

export default function ProfileExample() {
  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={3}>Profile Button Examples</Title>
        <Text type="secondary">
          Các ví dụ sử dụng ProfileButton component
        </Text>

        <Divider />

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Default Profile Button */}
          <div>
            <Title level={5}>Default (với tên và email)</Title>
            <ProfileButton />
          </div>

          {/* Small Profile Button */}
          <div>
            <Title level={5}>Small size</Title>
            <ProfileButton size="small" />
          </div>

          {/* Large Profile Button */}
          <div>
            <Title level={5}>Large size</Title>
            <ProfileButton size="large" />
          </div>

          {/* Avatar only */}
          <div>
            <Title level={5}>Chỉ hiển thị avatar</Title>
            <ProfileButton showName={false} />
          </div>

          {/* In header/navbar */}
          <div>
            <Title level={5}>Trong header/navbar</Title>
            <div
              style={{
                background: '#001529',
                padding: '12px 24px',
                borderRadius: '6px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <Text style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>
                My App
              </Text>
              <ProfileButton size="small" />
            </div>
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              💡 Click vào avatar → chọn "Hồ sơ" → popup chỉnh sửa hồ sơ sẽ mở
            </div>
          </div>
        </Space>
      </Card>
    </div>
  );
}
