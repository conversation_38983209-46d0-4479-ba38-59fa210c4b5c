"use client";

import { useForumService } from "@/hooks/useForumService";
import { useAuth } from "@/hooks/useAuth";
import type { CreateForumPostDto } from "@/types/forum.types";
import {
  ArrowLeftOutlined,
  PlusOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  Row,
  Select,
  Space,
  Tag,
  Typography,
  message,
} from "antd";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function CreateForumPostUserPage() {
  const router = useRouter();
  const { createPost, categories, fetchCategories, loading } =
    useForumService();
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleSubmit = async (values: any) => {
    if (!user) {
      message.error("Bạn cần đăng nhập để tạo bài viết!");
      return;
    }

    setSubmitting(true);
    try {
      const postData: CreateForumPostDto = {
        title: values.title,
        content: values.content,
        excerpt: values.excerpt,
        category_id: values.category_id,
        author_id: user.id,
        tags: tags.length > 0 ? tags : undefined,
        status: "pending", // User posts need approval
        image_urls: values.image_urls,
        video_urls: values.video_urls,
      };

      // Generate slug from title if not provided
      if (!values.slug && values.title) {
        postData.slug = generateSlug(values.title);
      } else {
        postData.slug = values.slug;
      }

      const newPost = await createPost(postData);
      if (newPost) {
        message.success("Bài viết đã được gửi và đang chờ duyệt!");
        router.push("/forum");
      } else {
        message.error("Không thể tạo bài viết!");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi tạo bài viết!");
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, "")
      .replace(/[\s_-]+/g, "-")
      .replace(/^-+|-+$/g, "");
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    const slug = generateSlug(title);
    form.setFieldsValue({ slug });
  };

  return (
    <div style={{ padding: "24px", maxWidth: "1000px", margin: "0 auto" }}>
      <Card>
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: 24 }}
        >
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => router.push("/forum")}
              >
                Quay lại diễn đàn
              </Button>
              <Title level={3} style={{ margin: 0 }}>
                Tạo bài viết mới
              </Title>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button onClick={() => form.resetFields()} disabled={submitting}>
                Đặt lại
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={submitting}
                onClick={() => form.submit()}
              >
                Gửi bài viết
              </Button>
            </Space>
          </Col>
        </Row>

        <div
          style={{
            backgroundColor: "#f0f9ff",
            border: "1px solid #0ea5e9",
            borderRadius: "8px",
            padding: "16px",
            marginBottom: "24px",
          }}
        >
          <Title level={5} style={{ color: "#0ea5e9", marginBottom: "8px" }}>
            📝 Thông báo quan trọng
          </Title>
          <Text style={{ color: "#0c4a6e" }}>
            Bài viết của bạn sẽ được gửi tới ban quản trị để duyệt trước khi
            hiển thị công khai. Chúng tôi sẽ xem xét và duyệt bài viết trong
            thời gian sớm nhất.
          </Text>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: "pending",
          }}
        >
          <Row gutter={24}>
            <Col span={16}>
              <Card title="Thông tin bài viết" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="Tiêu đề"
                  name="title"
                  rules={[
                    { required: true, message: "Vui lòng nhập tiêu đề!" },
                    { min: 5, message: "Tiêu đề phải có ít nhất 5 ký tự!" },
                    {
                      max: 500,
                      message: "Tiêu đề không được vượt quá 500 ký tự!",
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập tiêu đề bài viết..."
                    onChange={handleTitleChange}
                    showCount
                    maxLength={500}
                  />
                </Form.Item>

                <Form.Item
                  label="Mô tả ngắn (Excerpt)"
                  name="excerpt"
                  rules={[
                    {
                      max: 1000,
                      message: "Mô tả không được vượt quá 1000 ký tự!",
                    },
                  ]}
                >
                  <TextArea
                    placeholder="Mô tả ngắn gọn về bài viết..."
                    rows={3}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>

                <Form.Item
                  label="Nội dung"
                  name="content"
                  rules={[
                    { required: true, message: "Vui lòng nhập nội dung!" },
                    { min: 10, message: "Nội dung phải có ít nhất 10 ký tự!" },
                  ]}
                >
                  <TextArea
                    placeholder="Nhập nội dung bài viết..."
                    rows={12}
                    showCount
                    maxLength={50000}
                  />
                </Form.Item>
              </Card>

              <Card title="Hình ảnh và Video" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="URLs hình ảnh (mỗi URL một dòng)"
                  name="image_urls"
                >
                  <div>
                    <TextArea
                      placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"
                      rows={3}
                    />
                    <div
                      style={{ fontSize: "12px", color: "#666", marginTop: 4 }}
                    >
                      Các URL hình ảnh sẽ được lưu trữ tại
                      service.ngoaihangtv.live
                    </div>
                  </div>
                </Form.Item>

                <Form.Item
                  label="URLs video (mỗi URL một dòng)"
                  name="video_urls"
                >
                  <div>
                    <TextArea
                      placeholder="https://example.com/video1.mp4&#10;https://example.com/video2.mp4"
                      rows={2}
                    />
                    <div
                      style={{ fontSize: "12px", color: "#666", marginTop: 4 }}
                    >
                      Các URL video sẽ được lưu trữ tại service.ngoaihangtv.live
                    </div>
                  </div>
                </Form.Item>
              </Card>
            </Col>

            <Col span={8}>
              <Card title="Cài đặt" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="Danh mục"
                  name="category_id"
                  rules={[
                    { required: true, message: "Vui lòng chọn danh mục!" },
                  ]}
                >
                  <Select placeholder="Chọn danh mục">
                    {categories.map(category => (
                      <Option key={category.id} value={category.id}>
                        <Space>
                          <div
                            style={{
                              width: 12,
                              height: 12,
                              backgroundColor: category.color || "#1890ff",
                              borderRadius: "50%",
                            }}
                          />
                          {category.name}
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Card>

              <Card title="Tags" style={{ marginBottom: 16 }}>
                <Space style={{ marginBottom: 16 }}>
                  <Input
                    placeholder="Thêm tag..."
                    value={tagInput}
                    onChange={e => setTagInput(e.target.value)}
                    onPressEnter={e => {
                      e.preventDefault();
                      handleAddTag();
                    }}
                    style={{ width: 150 }}
                  />
                  <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={handleAddTag}
                  >
                    Thêm
                  </Button>
                </Space>

                <div style={{ minHeight: 32 }}>
                  {tags.map(tag => (
                    <Tag
                      key={tag}
                      closable
                      onClose={() => handleRemoveTag(tag)}
                      style={{ marginBottom: 4 }}
                    >
                      {tag}
                    </Tag>
                  ))}
                </div>
              </Card>

              <Card title="Lưu ý">
                <div style={{ color: "#666", fontSize: "12px" }}>
                  <p>• Bài viết sẽ được gửi tới admin để duyệt</p>
                  <p>• Thời gian duyệt thường từ 1-24 giờ</p>
                  <p>• Nội dung phải phù hợp với cộng đồng</p>
                  <p>• Có thể chỉnh sửa bài viết sau khi đăng</p>
                  <p>• Bài viết đã duyệt sẽ hiển thị công khai</p>
                </div>
              </Card>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
}
