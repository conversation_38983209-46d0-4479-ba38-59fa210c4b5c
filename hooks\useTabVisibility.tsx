import { useEffect, useState, useRef } from 'react';

export function useTabVisibility() {
    const [isVisible, setIsVisible] = useState(!document.hidden);
    const [isOnline, setIsOnline] = useState(navigator.onLine);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        const handleVisibilityChange = () => {
            // Clear any existing timeout
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
            
            if (!document.hidden) {
                // Add small delay when tab becomes visible to avoid rapid API calls
                timeoutRef.current = setTimeout(() => {
                    setIsVisible(true);
                }, 100);
            } else {
                setIsVisible(false);
            }
        };

        const handleOnline = () => {
            setIsOnline(true);
        };

        const handleOffline = () => {
            setIsOnline(false);
        };

        // Listen for visibility changes
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Listen for online/offline status
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
            
            // Clear timeout on cleanup
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return { isVisible, isOnline };
}
