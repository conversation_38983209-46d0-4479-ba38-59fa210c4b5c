"use client";

import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  message,
  Typography,
  Card,
  Row,
  Col,
  Switch,
  Avatar,
  Tooltip,
  Alert,
  Pagination,
  App,
} from 'antd';
import {
  UserOutlined,
  SearchOutlined,
  ReloadOutlined,
  TeamOutlined,
  EyeOutlined,
  FilterOutlined,
  ClearOutlined,
} from '@ant-design/icons';
import { useUserService } from '@/hooks/useUserService';
import { useAuthService } from '@/hooks/useAuthService';
import type { Profile } from '@/types/profile.types';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/vi';

dayjs.extend(relativeTime);
dayjs.locale('vi');

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

function UsersPageContent() {
  const { message: messageApi } = App.useApp();
  const { profile } = useAuthService();
  const {
    users,
    loading,
    error,
    totalUsers,
    currentPage,
    totalPages,
    stats,
    updateBettingPermission,
    updateMultipleBettingPermissions,
    fetchUserStats,
    refreshUsers,
    setFilters,
    resetFilters,
    setPage
  } = useUserService({ limit: 10 });

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [roleFilter, setRoleFilter] = useState<string | undefined>();
  const [bettingFilter, setBettingFilter] = useState<boolean | undefined>();

  // Check if user is admin
  if (profile?.role !== 'admin') {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="Không có quyền truy cập"
          description="Bạn không có quyền truy cập vào trang này."
          type="error"
          showIcon
        />
      </div>
    );
  }

  const handleSearch = (value: string) => {
    setSearchValue(value);
    setFilters({ search: value });
  };

  const handleRoleFilter = (role: string | undefined) => {
    setRoleFilter(role);
    setFilters({ role });
  };

  const handleBettingFilter = (canAccess: boolean | undefined) => {
    setBettingFilter(canAccess);
    setFilters({ can_access_betting: canAccess });
  };

  const handleResetFilters = () => {
    setSearchValue('');
    setRoleFilter(undefined);
    setBettingFilter(undefined);
    resetFilters();
  };

  const handleRefresh = () => {
    refreshUsers();
    fetchUserStats();
    messageApi.success('Đã làm mới danh sách users!');
  };

  const handleBulkBettingUpdate = async (canAccess: boolean) => {
    if (selectedRowKeys.length === 0) {
      messageApi.warning('Vui lòng chọn ít nhất một user!');
      return;
    }

    Modal.confirm({
      title: `${canAccess ? 'Cấp' : 'Thu hồi'} quyền soi kèo`,
      content: `Bạn có chắc chắn muốn ${canAccess ? 'cấp' : 'thu hồi'} quyền soi kèo cho ${selectedRowKeys.length} users đã chọn?`,
      onOk: async () => {
        try {
          const success = await updateMultipleBettingPermissions(selectedRowKeys, canAccess);
          if (success) {
            messageApi.success(`${canAccess ? 'Cấp' : 'Thu hồi'} quyền soi kèo thành công cho ${selectedRowKeys.length} users!`);
            setSelectedRowKeys([]);
          } else {
            messageApi.error('Không thể cập nhật quyền soi kèo!');
          }
        } catch (error) {
          messageApi.error('Có lỗi xảy ra khi cập nhật quyền soi kèo!');
        }
      },
    });
  };

  const handleBettingToggle = async (userId: string, currentValue: boolean) => {
    try {
      const success = await updateBettingPermission(userId, !currentValue);
      if (success) {
        messageApi.success(`${!currentValue ? 'Cấp' : 'Thu hồi'} quyền soi kèo thành công!`);
      } else {
        messageApi.error('Không thể cập nhật quyền soi kèo!');
      }
    } catch (error) {
      messageApi.error('Có lỗi xảy ra khi cập nhật quyền soi kèo!');
    }
  };





  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'red';
      case 'moderator':
        return 'orange';
      case 'user':
        return 'blue';
      default:
        return 'default';
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Quản trị viên';
      case 'blv':
        return 'Bình luận viên';
      case 'member':
        return 'Thành viên';
      default:
        return 'Không xác định';
    }
  };

  const columns = [
    {
      title: 'Thành viên',
      dataIndex: 'full_name',
      key: 'user',
      width: 250,
      render: (text: string, record: Profile) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Avatar 
            size={40} 
            icon={<UserOutlined />}
            src={record.avatar_url}
          />
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: 2 }}>
              {text || 'Chưa có tên'}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.email}
            </div>
            <div style={{ fontSize: '11px', color: '#999' }}>
              ID: {record.id.slice(0, 8)}...
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Vai trò',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role: string) => (
        <Tag color={getRoleColor(role)}>
          {getRoleText(role)}
        </Tag>
      ),
    },

    {
      title: 'Quyền soi kèo',
      dataIndex: 'can_access_betting',
      key: 'betting',
      width: 120,
      render: (canAccess: boolean, record: Profile) => (
        <Switch
          checked={canAccess}
          onChange={() => handleBettingToggle(record.id, canAccess)}
          loading={loading}
          checkedChildren="Có"
          unCheckedChildren="Không"
          aria-label={`${canAccess ? 'Thu hồi' : 'Cấp'} quyền soi kèo cho ${record.full_name || record.email}`}
        />
      ),
    },

    {
      title: 'Ngày tạo',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => (
        <Tooltip title={dayjs(date).format('DD/MM/YYYY HH:mm:ss')}>
          <div style={{ fontSize: '12px' }}>
            {dayjs(date).format('DD/MM/YYYY')}
          </div>
        </Tooltip>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 100,
      render: (_, record: Profile) => (
        <Space size="small">
          <Tooltip title="Xem chi tiết">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                Modal.info({
                  title: 'Thông tin chi tiết',
                  content: (
                    <div>
                      <p><strong>ID:</strong> {record.id}</p>
                      <p><strong>Email:</strong> {record.email}</p>
                      <p><strong>Tên:</strong> {record.full_name || 'Chưa có'}</p>
                      <p><strong>Vai trò:</strong> {getRoleText(record.role)}</p>
                      <p><strong>Quyền soi kèo:</strong> {record.can_access_betting ? 'Có' : 'Không'}</p>
                      <p><strong>Ngày tạo:</strong> {dayjs(record.created_at).format('DD/MM/YYYY HH:mm:ss')}</p>
                    </div>
                  ),
                  width: 500,
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: 8 }}>
            <TeamOutlined />
            Quản lý Users
          </Title>
          <Text type="secondary">
            Quản lý người dùng và phân quyền truy cập soi kèo
          </Text>
        </div>

        {error && (
          <Alert
            message="Có lỗi xảy ra"
            description={error}
            type="error"
            closable
            style={{ marginBottom: 16 }}
          />
        )}

        {/* Filters */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={24} md={12} lg={8} xl={6}>
              <Search
                placeholder="Tìm kiếm theo tên, email..."
                value={searchValue}
                onSearch={handleSearch}
                onChange={(e) => setSearchValue(e.target.value)}
                allowClear
                enterButton={<SearchOutlined />}
                aria-label="Tìm kiếm người dùng"
              />
            </Col>
            <Col xs={24} sm={8} md={6} lg={4} xl={3}>
              <Select
                placeholder="Vai trò"
                value={roleFilter}
                onChange={handleRoleFilter}
                allowClear
                style={{ width: '100%' }}
                aria-label="Lọc theo vai trò"
              >
                <Option value="admin">Quản trị viên</Option>
                <Option value="blv">Bình luận viên</Option>
                <Option value="member">Thành viên</Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} md={6} lg={4} xl={3}>
              <Select
                placeholder="Quyền soi kèo"
                value={bettingFilter}
                onChange={handleBettingFilter}
                allowClear
                style={{ width: '100%' }}
                aria-label="Lọc theo quyền soi kèo"
              >
                <Option value={true}>Có quyền</Option>
                <Option value={false}>Không có quyền</Option>
              </Select>
            </Col>

            <Col xs={24} sm={24} md={12} lg={8} xl={9}>
              <Space wrap>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                  aria-label="Làm mới danh sách"
                >
                  Làm mới
                </Button>
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleResetFilters}
                  aria-label="Xóa tất cả bộ lọc"
                >
                  Xóa bộ lọc
                </Button>

              </Space>
            </Col>
          </Row>
        </Card>

        {/* Stats calculations */}
        {(() => {
          const totalUsersDisplay = stats?.totalUsers || totalUsers;
          const bettingUsersDisplay = stats?.bettingUsers || users.filter(user => user.can_access_betting).length;
          const adminUsersDisplay = stats?.adminUsers || users.filter(user => user.role === 'admin').length;

          return (
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={12} sm={12} md={8} lg={8} xl={8}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                      {totalUsersDisplay}
                    </div>
                    <div style={{ color: '#666', fontSize: '12px' }}>Tổng số users</div>
                  </div>
                </Card>
              </Col>
              <Col xs={12} sm={12} md={8} lg={8} xl={8}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                      {bettingUsersDisplay}
                    </div>
                    <div style={{ color: '#666', fontSize: '12px' }}>Có quyền soi kèo</div>
                  </div>
                </Card>
              </Col>
              <Col xs={12} sm={12} md={8} lg={8} xl={8}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f5222d' }}>
                      {adminUsersDisplay}
                    </div>
                    <div style={{ color: '#666', fontSize: '12px' }}>Quản trị viên</div>
                  </div>
                </Card>
              </Col>
            </Row>
          );
        })()}

        {/* Table */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          size="middle"
          locale={{
            emptyText: 'Không có dữ liệu',
            selectAll: 'Chọn tất cả',
            selectInvert: 'Đảo ngược lựa chọn',
            selectionAll: 'Chọn tất cả dữ liệu',
            sortTitle: 'Sắp xếp',
            expand: 'Mở rộng dòng',
            collapse: 'Thu gọn dòng',
          }}
          aria-label="Bảng danh sách người dùng"
        />

        {/* Pagination */}
        {totalPages > 1 && (
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Pagination
              current={currentPage}
              total={totalUsers}
              pageSize={10}
              onChange={(newPage) => {
                setPage(newPage);
              }}
              showSizeChanger={false}
              showQuickJumper
              showTotal={(total, range) => 
                `${range[0]}-${range[1]} của ${total} users`
              }
            />
          </div>
        )}

        {selectedRowKeys.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Alert
              message={`Đã chọn ${selectedRowKeys.length} users`}
              type="info"
              action={
                <Space>
                  <Button 
                    size="small" 
                    type="primary"
                    onClick={() => handleBulkBettingUpdate(true)}
                    loading={loading}
                  >
                    Cấp quyền soi kèo
                  </Button>
                  <Button 
                    size="small" 
                    danger
                    onClick={() => handleBulkBettingUpdate(false)}
                    loading={loading}
                  >
                    Thu hồi quyền
                  </Button>
                  <Button size="small" onClick={() => setSelectedRowKeys([])}>
                    Bỏ chọn
                  </Button>
                </Space>
              }
            />
          </div>
        )}
      </Card>
    </div>
  );
}

export default function UsersPage() {
  return (
    <App>
      <UsersPageContent />
    </App>
  );
}