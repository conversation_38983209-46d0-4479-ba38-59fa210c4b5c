-- Create forum categories table
CREATE TABLE IF NOT EXISTS forum_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(255),
    color VARCHAR(7), -- hex color code
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create forum posts table
CREATE TABLE IF NOT EXISTS forum_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    slug VARCHAR(500) UNIQUE NOT NULL,
    category_id UUID REFERENCES forum_categories(id) ON DELETE SET NULL,
    author_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'draft')),
    is_pinned BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    image_urls TEXT[], -- array of image URLs
    video_urls TEXT[], -- array of video URLs
    tags TEXT[], -- array of tags
    metadata JSONB DEFAULT '{}', -- for additional data
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create forum comments table (supports 3-level nesting)
CREATE TABLE IF NOT EXISTS forum_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content TEXT NOT NULL,
    post_id UUID NOT NULL REFERENCES forum_posts(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES forum_comments(id) ON DELETE CASCADE, -- for nested comments
    level INTEGER DEFAULT 1 CHECK (level >= 1 AND level <= 3), -- comment level (1, 2, or 3)
    status VARCHAR(20) DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected', 'spam')),
    like_count INTEGER DEFAULT 0,
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create forum post likes table
CREATE TABLE IF NOT EXISTS forum_post_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES forum_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- Create forum comment likes table
CREATE TABLE IF NOT EXISTS forum_comment_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES forum_comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(comment_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_forum_posts_category_id ON forum_posts(category_id);
CREATE INDEX IF NOT EXISTS idx_forum_posts_author_id ON forum_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_forum_posts_status ON forum_posts(status);
CREATE INDEX IF NOT EXISTS idx_forum_posts_is_pinned ON forum_posts(is_pinned);
CREATE INDEX IF NOT EXISTS idx_forum_posts_created_at ON forum_posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_forum_posts_slug ON forum_posts(slug);

CREATE INDEX IF NOT EXISTS idx_forum_comments_post_id ON forum_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_forum_comments_author_id ON forum_comments(author_id);
CREATE INDEX IF NOT EXISTS idx_forum_comments_parent_id ON forum_comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_forum_comments_level ON forum_comments(level);
CREATE INDEX IF NOT EXISTS idx_forum_comments_status ON forum_comments(status);
CREATE INDEX IF NOT EXISTS idx_forum_comments_created_at ON forum_comments(created_at DESC);

-- Create function to update comment count on posts
CREATE OR REPLACE FUNCTION update_post_comment_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE forum_posts
        SET comment_count = comment_count + 1
        WHERE id = NEW.post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE forum_posts
        SET comment_count = comment_count - 1
        WHERE id = OLD.post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for comment count
CREATE TRIGGER trigger_update_post_comment_count
    AFTER INSERT OR DELETE ON forum_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_post_comment_count();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_forum_categories_updated_at
    BEFORE UPDATE ON forum_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_forum_posts_updated_at
    BEFORE UPDATE ON forum_posts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_forum_comments_updated_at
    BEFORE UPDATE ON forum_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE forum_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_comment_likes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for forum_categories (public read, admin write)
CREATE POLICY "Forum categories are viewable by everyone" ON forum_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Forum categories are manageable by admins" ON forum_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Create RLS policies for forum_posts
CREATE POLICY "Forum posts are viewable by everyone if approved" ON forum_posts
    FOR SELECT USING (status = 'approved' OR author_id = auth.uid());

CREATE POLICY "Users can create posts" ON forum_posts
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Users can update their own posts" ON forum_posts
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Admins can manage all posts" ON forum_posts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Create RLS policies for forum_comments
CREATE POLICY "Forum comments are viewable by everyone if approved" ON forum_comments
    FOR SELECT USING (status = 'approved' OR author_id = auth.uid());

CREATE POLICY "Users can create comments" ON forum_comments
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Users can update their own comments" ON forum_comments
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Admins can manage all comments" ON forum_comments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Create RLS policies for forum_post_likes
CREATE POLICY "Post likes are viewable by everyone" ON forum_post_likes FOR SELECT USING (true);
CREATE POLICY "Users can manage their own post likes" ON forum_post_likes FOR ALL USING (auth.uid() = user_id);

-- Create RLS policies for forum_comment_likes
CREATE POLICY "Comment likes are viewable by everyone" ON forum_comment_likes FOR SELECT USING (true);
CREATE POLICY "Users can manage their own comment likes" ON forum_comment_likes FOR ALL USING (auth.uid() = user_id);

-- Insert default categories
INSERT INTO forum_categories (name, slug, description, icon, color, sort_order) VALUES
('Thảo luận chung', 'thao-luan-chung', 'Chủ đề thảo luận chung về bóng đá', 'MessageCircle', '#3B82F6', 1),
('Tin tức bóng đá', 'tin-tuc-bong-da', 'Tin tức và cập nhật mới nhất về bóng đá', 'Newspaper', '#10B981', 2),
('Kinh nghiệm cá cược', 'kinh-nghiem-ca-cuoc', 'Chia sẻ kinh nghiệm và mẹo cá cược', 'Target', '#F59E0B', 3),
('Phân tích trận đấu', 'phan-tich-tran-dau', 'Phân tích chuyên sâu về các trận đấu', 'TrendingUp', '#8B5CF6', 4),
('Hỏi đáp', 'hoi-dap', 'Giải đáp thắc mắc về bóng đá và cá cược', 'HelpCircle', '#EF4444', 5)
ON CONFLICT (slug) DO NOTHING;