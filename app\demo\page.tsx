'use client';

import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { useRouter } from 'next/navigation';
import { EyeOutlined, DashboardOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

export default function DemoPage() {
  const router = useRouter();

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f5f5f5',
      padding: '48px 24px'
    }}>
      <div style={{ 
        maxWidth: '800px', 
        margin: '0 auto'
      }}>
        <Card style={{ 
          textAlign: 'center',
          borderRadius: '12px',
          boxShadow: '0 4px 16px rgba(0,0,0,0.1)'
        }}>
          <Title level={1} style={{ color: '#1890ff', marginBottom: '24px' }}>
            🏆 Sports Betting Admin Demo
          </Title>
          
          <Paragraph style={{ 
            fontSize: '18px', 
            color: '#666',
            marginBottom: '32px',
            maxWidth: '600px',
            margin: '0 auto 32px'
          }}>
            Ch<PERSON><PERSON> mừng bạn đến với hệ thống quản lý tin tức thể thao. 
            <PERSON><PERSON> thống hỗ trợ tạo, chỉnh sửa và hiển thị tin tức với rich text editor và SEO tối ưu.
          </Paragraph>

          <Space size="large" direction="vertical" style={{ width: '100%' }}>
            <Space size="middle" wrap>
              <Button 
                type="primary" 
                size="large"
                icon={<EyeOutlined />}
                onClick={() => router.push('/news')}
                style={{ 
                  borderRadius: '8px',
                  height: '48px',
                  fontSize: '16px',
                  fontWeight: '500'
                }}
              >
                Xem Tin Tức Public
              </Button>
              
              <Button 
                size="large"
                icon={<DashboardOutlined />}
                onClick={() => router.push('/auth/login')}
                style={{ 
                  borderRadius: '8px',
                  height: '48px',
                  fontSize: '16px',
                  fontWeight: '500'
                }}
              >
                Vào Dashboard Admin
              </Button>
            </Space>

            <div style={{ 
              marginTop: '32px',
              padding: '24px',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              textAlign: 'left'
            }}>
              <Title level={4} style={{ marginBottom: '16px' }}>✨ Tính năng chính:</Title>
              <ul style={{ 
                fontSize: '16px', 
                lineHeight: '1.8',
                color: '#666',
                paddingLeft: '20px'
              }}>
                <li>📝 Rich Text Editor với HTML content</li>
                <li>🔍 SEO tối ưu với meta tags</li>
                <li>📱 Responsive design</li>
                <li>🎨 UI/UX hiện đại với Ant Design</li>
                <li>🚀 Performance cao với Next.js</li>
                <li>🔐 Authentication & Authorization</li>
                <li>📊 Dashboard quản lý tin tức</li>
                <li>🌐 Trang public hiển thị tin tức</li>
              </ul>
            </div>

            <div style={{ 
              marginTop: '24px',
              padding: '16px',
              backgroundColor: '#e6f7ff',
              borderRadius: '8px',
              border: '1px solid #91d5ff'
            }}>
              <Paragraph style={{ 
                margin: 0,
                color: '#0050b3',
                fontWeight: '500'
              }}>
                💡 <strong>Tip:</strong> Nội dung từ Rich Text Editor sẽ được render dưới dạng HTML 
                với styling đầy đủ trên trang chi tiết tin tức.
              </Paragraph>
            </div>
          </Space>
        </Card>
      </div>
    </div>
  );
}