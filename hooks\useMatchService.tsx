"use client";

import { matchService } from "@/services/match.service";
import type {
  CreateMatchData,
  LiveMatchData,
  Match,
  MatchFilters,
  MatchStats,
  ScheduleFilters,
  UpdateMatchData,
} from "@/types/match.types";
import { useCallback, useEffect, useState } from "react";

export function useMatchService() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [schedule, setSchedule] = useState<Match[]>([]);
  const [stats, setStats] = useState<MatchStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    total_pages: 0,
  });
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const [lastStatsFetchTime, setLastStatsFetchTime] = useState<number>(0);
  const [lastScheduleFetchTime, setLastScheduleFetchTime] = useState<number>(0);

  /**
   * Load matches with filters
   */
  const loadMatches = useCallback(
    async (filters: MatchFilters = {}, forceRefresh = false) => {
      const now = Date.now();

      // Check if we should use cached data (cache for 30 seconds)
      if (!forceRefresh && now - lastFetchTime < 30000 && matches.length > 0) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await matchService.getMatches(filters);

        setMatches(response.matches);
        setPagination({
          page: response.page,
          limit: response.limit,
          total: response.total,
          total_pages: response.total_pages,
        });
        setLastFetchTime(now);
      } catch (err) {
        console.error("Error loading matches:", err);
        setError(err instanceof Error ? err.message : "Failed to load matches");
      } finally {
        setLoading(false);
      }
    },
    [matches.length, lastFetchTime]
  );

  /**
   * Load schedule with filters
   */
  const loadSchedule = useCallback(
    async (filters: ScheduleFilters = {}, forceRefresh = false) => {
      const now = Date.now();

      // Check if we should use cached data (cache for 60 seconds)
      if (
        !forceRefresh &&
        now - lastScheduleFetchTime < 60000 &&
        schedule.length > 0
      ) {
        return;
      }

      try {
        setError(null);
        const response = await matchService.getSchedule(filters);
        setSchedule(response.matches);
        setLastScheduleFetchTime(now);
      } catch (err) {
        console.error("Error loading schedule:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load schedule"
        );
      }
    },
    [schedule.length, lastScheduleFetchTime]
  );

  /**
   * Load match statistics
   */
  const loadStats = useCallback(
    async (forceRefresh = false) => {
      const now = Date.now();

      // Check if we should use cached data (cache for 60 seconds)
      if (!forceRefresh && now - lastStatsFetchTime < 60000 && stats) {
        return;
      }

      try {
        setError(null);
        const statsData = await matchService.getMatchStats();
        setStats(statsData);
        setLastStatsFetchTime(now);
      } catch (err) {
        console.error("Error loading match stats:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load match stats"
        );
      }
    },
    [stats, lastStatsFetchTime]
  );

  /**
   * Get match by ID
   */
  const getMatchById = useCallback(
    async (id: string): Promise<Match | null> => {
      try {
        setError(null);
        return await matchService.getMatchById(id);
      } catch (err) {
        console.error("Error getting match by ID:", err);
        setError(err instanceof Error ? err.message : "Failed to get match");
        return null;
      }
    },
    []
  );

  /**
   * Create match
   */
  const createMatch = useCallback(
    async (matchData: CreateMatchData): Promise<boolean> => {
      try {
        setError(null);
        await matchService.createMatch(matchData);

        // Refresh matches list
        await loadMatches({}, true);
        return true;
      } catch (err) {
        console.error("Error creating match:", err);
        setError(err instanceof Error ? err.message : "Failed to create match");
        return false;
      }
    },
    [loadMatches]
  );

  /**
   * Update match
   */
  const updateMatch = useCallback(
    async (id: string, updates: UpdateMatchData): Promise<boolean> => {
      try {
        setError(null);
        await matchService.updateMatch(id, updates);

        // Refresh matches list
        await loadMatches({}, true);
        return true;
      } catch (err) {
        console.error("Error updating match:", err);
        setError(err instanceof Error ? err.message : "Failed to update match");
        return false;
      }
    },
    [loadMatches]
  );

  /**
   * Delete match
   */
  const deleteMatch = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        setError(null);
        await matchService.deleteMatch(id);

        // Refresh matches list
        await loadMatches({}, true);
        return true;
      } catch (err) {
        console.error("Error deleting match:", err);
        setError(err instanceof Error ? err.message : "Failed to delete match");
        return false;
      }
    },
    [loadMatches]
  );

  /**
   * Get live match data
   */
  const getLiveMatchData = useCallback(
    async (matchId: string): Promise<LiveMatchData | null> => {
      try {
        setError(null);
        return await matchService.getLiveMatchData(matchId);
      } catch (err) {
        console.error("Error getting live match data:", err);
        setError(
          err instanceof Error ? err.message : "Failed to get live match data"
        );
        return null;
      }
    },
    []
  );

  /**
   * Search matches
   */
  const searchMatches = useCallback(
    async (searchTerm: string, filters: Omit<MatchFilters, "search"> = {}) => {
      await loadMatches({
        ...filters,
        search: searchTerm,
        page: 1, // Reset to first page when searching
      });
    },
    [loadMatches]
  );

  /**
   * Filter matches by status
   */
  const filterByStatus = useCallback(
    async (
      status: Match["status"] | undefined,
      filters: Omit<MatchFilters, "status"> = {}
    ) => {
      await loadMatches({
        ...filters,
        status,
        page: 1, // Reset to first page when filtering
      });
    },
    [loadMatches]
  );

  /**
   * Filter matches by competition
   */
  const filterByCompetition = useCallback(
    async (
      competition: string | undefined,
      filters: Omit<MatchFilters, "competition"> = {}
    ) => {
      await loadMatches({
        ...filters,
        competition,
        page: 1, // Reset to first page when filtering
      });
    },
    [loadMatches]
  );

  /**
   * Filter matches by date range
   */
  const filterByDateRange = useCallback(
    async (
      dateFrom: string | undefined,
      dateTo: string | undefined,
      filters: Omit<MatchFilters, "date_from" | "date_to"> = {}
    ) => {
      await loadMatches({
        ...filters,
        date_from: dateFrom,
        date_to: dateTo,
        page: 1, // Reset to first page when filtering
      });
    },
    [loadMatches]
  );

  /**
   * Sort matches
   */
  const sortMatches = useCallback(
    async (
      sortBy: MatchFilters["sort_by"],
      sortOrder: MatchFilters["sort_order"],
      filters: Omit<MatchFilters, "sort_by" | "sort_order"> = {}
    ) => {
      await loadMatches({
        ...filters,
        sort_by: sortBy,
        sort_order: sortOrder,
        page: 1, // Reset to first page when sorting
      });
    },
    [loadMatches]
  );

  /**
   * Change page
   */
  const changePage = useCallback(
    async (page: number, filters: MatchFilters = {}) => {
      await loadMatches({
        ...filters,
        page,
      });
    },
    [loadMatches]
  );

  /**
   * Refresh data
   */
  const refresh = useCallback(
    async (filters: MatchFilters = {}) => {
      await Promise.all([
        loadMatches(filters, true), // Force refresh
        loadStats(true), // Force refresh
      ]);
    },
    [loadMatches, loadStats]
  );

  /**
   * Refresh schedule
   */
  const refreshSchedule = useCallback(
    async (filters: ScheduleFilters = {}) => {
      await loadSchedule(filters, true); // Force refresh
    },
    [loadSchedule]
  );

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      await Promise.all([
        loadMatches({}, true), // Force refresh
        loadStats(true), // Force refresh
      ]);
    };
    loadInitialData();
  }, []); // Empty dependency array to run only once

  return {
    // State
    matches,
    schedule,
    stats,
    loading,
    error,
    pagination,

    // Actions
    loadMatches,
    loadSchedule,
    loadStats,
    getMatchById,
    createMatch,
    updateMatch,
    deleteMatch,
    getLiveMatchData,
    searchMatches,
    filterByStatus,
    filterByCompetition,
    filterByDateRange,
    sortMatches,
    changePage,
    refresh,
    refreshSchedule,

    // Utilities
    clearError: () => setError(null),
  };
}
