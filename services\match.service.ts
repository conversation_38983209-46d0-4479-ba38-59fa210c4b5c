import { createClient } from "@/lib/supabase/client";
import type {
  Match,
  MatchFilters,
  MatchResponse,
  MatchStats,
  CreateMatchData,
  UpdateMatchData,
  ScheduleFilters,
  ScheduleResponse,
  MatchEvent,
  MatchCommentary,
  LiveMatchData,
} from "@/types/match.types";

class MatchService {
  private supabase;

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }

  // Static method to create instance with default client for backward compatibility
  static createWithDefaultClient() {
    return new MatchService(createClient());
  }

  /**
   * Get all matches with filters
   */
  async getMatches(filters: MatchFilters = {}): Promise<MatchResponse> {
    try {
      const {
        search = "",
        status,
        competition,
        date_from,
        date_to,
        team,
        sort_by = "match_date",
        sort_order = "desc",
        page = 1,
        limit = 10,
      } = filters;

      let query = this.supabase.from("matches").select(`
                    *,
                    home_team:teams!home_team_id(name, logo_url),
                    away_team:teams!away_team_id(name, logo_url),
                    league:leagues!league_id(name, country, logo_url)
                `);

      // Apply search filter
      if (search) {
        query = query.or(
          `home_team.name.ilike.%${search}%,away_team.name.ilike.%${search}%,league.name.ilike.%${search}%`
        );
      }

      // Apply status filter
      if (status) {
        query = query.eq("status", status);
      }

      // Apply competition filter
      if (competition) {
        query = query.eq("league.name", competition);
      }

      // Apply date filters
      if (date_from) {
        query = query.gte("match_date", date_from);
      }
      if (date_to) {
        query = query.lte("match_date", date_to);
      }

      // Apply team filter
      if (team) {
        query = query.or(
          `home_team.name.ilike.%${team}%,away_team.name.ilike.%${team}%`
        );
      }

      // Get total count with the same filters
      let countQuery = this.supabase.from("matches").select(
        `
                    *,
                    home_team:teams!home_team_id(name),
                    away_team:teams!away_team_id(name),
                    league:leagues!league_id(name)
                `,
        { count: "exact", head: true }
      );

      // Apply same filters to count query
      if (search) {
        countQuery = countQuery.or(
          `home_team.name.ilike.%${search}%,away_team.name.ilike.%${search}%,league.name.ilike.%${search}%`
        );
      }
      if (status) {
        countQuery = countQuery.eq("status", status);
      }
      if (competition) {
        countQuery = countQuery.eq("league.name", competition);
      }
      if (date_from) {
        countQuery = countQuery.gte("match_date", date_from);
      }
      if (date_to) {
        countQuery = countQuery.lte("match_date", date_to);
      }
      if (team) {
        countQuery = countQuery.or(
          `home_team.name.ilike.%${team}%,away_team.name.ilike.%${team}%`
        );
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        console.error("Error getting match count:", countError);
        throw new Error(`Failed to get match count: ${countError.message}`);
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      // Apply sorting
      query = query.order(sort_by, { ascending: sort_order === "asc" });

      const { data: matches, error: matchesError } = await query;

      if (matchesError) {
        console.error("Error fetching matches:", matchesError);
        throw new Error(`Failed to fetch matches: ${matchesError.message}`);
      }

      // Transform data to include team and league names
      const transformedMatches = (matches || []).map((match: any) => ({
        ...match,
        home_team: match.home_team?.name || "N/A",
        away_team: match.away_team?.name || "N/A",
        home_team_logo: match.home_team?.logo_url,
        away_team_logo: match.away_team?.logo_url,
        competition: match.league?.name || "N/A",
        league_country: match.league?.country,
        league_logo: match.league?.logo_url,
        venue: match.stadium || "N/A",
      }));

      const total = count || 0;
      const total_pages = Math.ceil(total / limit);

      return {
        matches: transformedMatches,
        total,
        page,
        limit,
        total_pages,
      };
    } catch (error) {
      console.error("Error in getMatches:", error);
      throw error;
    }
  }

  /**
   * Get match by ID
   */
  async getMatchById(id: string): Promise<Match | null> {
    try {
      const { data: match, error } = await this.supabase
        .from("matches")
        .select(
          `
                    *,
                    home_team:teams!home_team_id(name, logo_url),
                    away_team:teams!away_team_id(name, logo_url),
                    league:leagues!league_id(name, country, logo_url)
                `
        )
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // Match not found
        }
        console.error("Error fetching match:", error);
        throw new Error(`Failed to fetch match: ${error.message}`);
      }

      // Transform data to include team and league names from joined data
      const transformedMatch = {
        ...match,
        home_team: match.home_team?.name || "N/A",
        away_team: match.away_team?.name || "N/A",
        home_team_logo: match.home_team?.logo_url,
        away_team_logo: match.away_team?.logo_url,
        competition: match.league?.name || "N/A",
        league_country: match.league?.country,
        league_logo: match.league?.logo_url,
        venue: match.stadium || "N/A",
      };

      return transformedMatch;
    } catch (error) {
      console.error("Error in getMatchById:", error);
      throw error;
    }
  }

  /**
   * Get match statistics
   */
  async getMatchStats(): Promise<MatchStats> {
    try {
      const [
        totalResult,
        scheduledResult,
        liveResult,
        finishedResult,
        cancelledResult,
        postponedResult,
        todayResult,
        weekResult,
        monthResult,
        goalsResult,
        attendanceResult,
      ] = await Promise.all([
        // Get total matches
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true }),

        // Get scheduled matches
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .eq("status", "scheduled"),

        // Get live matches
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .eq("status", "live"),

        // Get finished matches
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .eq("status", "finished"),

        // Get cancelled matches
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .eq("status", "cancelled"),

        // Get postponed matches
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .eq("status", "postponed"),

        // Get matches today
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .eq("match_date", new Date().toISOString().split("T")[0]),

        // Get matches this week
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .gte(
            "match_date",
            new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
              .toISOString()
              .split("T")[0]
          )
          .lte("match_date", new Date().toISOString().split("T")[0]),

        // Get matches this month
        this.supabase
          .from("matches")
          .select("*", { count: "exact", head: true })
          .gte(
            "match_date",
            new Date(new Date().getFullYear(), new Date().getMonth(), 1)
              .toISOString()
              .split("T")[0]
          )
          .lte("match_date", new Date().toISOString().split("T")[0]),

        // Get total goals
        this.supabase
          .from("matches")
          .select("home_score, away_score")
          .not("home_score", "is", null)
          .not("away_score", "is", null),

        // Skip attendance data - not needed
        this.supabase.from("matches").select("id").limit(1),
      ]);

      // Check for errors
      const errors = [
        totalResult.error,
        scheduledResult.error,
        liveResult.error,
        finishedResult.error,
        cancelledResult.error,
        postponedResult.error,
        todayResult.error,
        weekResult.error,
        monthResult.error,
        goalsResult.error,
        attendanceResult.error,
      ].filter(Boolean);

      if (errors.length > 0) {
        console.error("Error fetching match stats:", errors);
        throw new Error(`Failed to fetch match stats: ${errors[0]?.message}`);
      }

      // Calculate total goals
      let total_goals = 0;
      if (goalsResult.data) {
        total_goals = goalsResult.data.reduce((sum: number, match: any) => {
          return sum + (match.home_score || 0) + (match.away_score || 0);
        }, 0);
      }

      // Skip attendance calculation - not needed

      return {
        total_matches: totalResult.count || 0,
        scheduled_matches: scheduledResult.count || 0,
        live_matches: liveResult.count || 0,
        finished_matches: finishedResult.count || 0,
        cancelled_matches: cancelledResult.count || 0,
        postponed_matches: postponedResult.count || 0,
        matches_today: todayResult.count || 0,
        matches_this_week: weekResult.count || 0,
        matches_this_month: monthResult.count || 0,
        total_goals,
      };
    } catch (error) {
      console.error("Error in getMatchStats:", error);
      throw error;
    }
  }

  /**
   * Create a new match
   */
  async createMatch(matchData: CreateMatchData): Promise<Match> {
    try {
      const { data, error } = await this.supabase
        .from("matches")
        .insert({
          ...matchData,
          status: "scheduled",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error("Error creating match:", error);
        throw new Error(`Failed to create match: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error in createMatch:", error);
      throw error;
    }
  }

  /**
   * Update match
   */
  async updateMatch(id: string, updates: UpdateMatchData): Promise<Match> {
    try {
      const { data, error } = await this.supabase
        .from("matches")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        console.error("Error updating match:", error);
        throw new Error(`Failed to update match: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error in updateMatch:", error);
      throw error;
    }
  }

  /**
   * Delete match
   */
  async deleteMatch(id: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from("matches")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting match:", error);
        throw new Error(`Failed to delete match: ${error.message}`);
      }
    } catch (error) {
      console.error("Error in deleteMatch:", error);
      throw error;
    }
  }

  /**
   * Get schedule with filters
   */
  async getSchedule(filters: ScheduleFilters = {}): Promise<ScheduleResponse> {
    try {
      const {
        date_from,
        date_to,
        competition,
        sport_id,
        team,
        venue,
        status,
        view_type = "week",
      } = filters;

      let query = this.supabase
        .from("matches")
        .select(
          `
                    *,
                    home_team:teams!home_team_id(name, logo_url),
                    away_team:teams!away_team_id(name, logo_url),
                    league:leagues!league_id(name, country, logo_url)
                `
        )
        .order("match_date", { ascending: true });

      // Apply date filters
      if (date_from) {
        query = query.gte("match_date", date_from);
      }
      if (date_to) {
        query = query.lte("match_date", date_to);
      }

      // Apply other filters
      if (competition) {
        query = query.eq("league.name", competition);
      }
      if (sport_id) {
        // For now, we'll filter by league_id if it matches sport_id
        // This is a temporary solution until we have proper foreign key
        query = query.eq("league_id", sport_id);
      }
      if (team) {
        query = query.or(
          `home_team.name.ilike.%${team}%,away_team.name.ilike.%${team}%`
        );
      }
      if (venue) {
        query = query.ilike("stadium", `%${venue}%`);
      }
      if (status) {
        query = query.eq("status", status);
      }

      const { data: matches, error } = await query;

      if (error) {
        console.error("Error fetching schedule:", error);
        throw new Error(`Failed to fetch schedule: ${error.message}`);
      }

      // Transform data to include team and league names
      const transformedMatches = (matches || []).map((match: any) => ({
        ...match,
        home_team: match.home_team?.name || "N/A",
        away_team: match.away_team?.name || "N/A",
        home_team_logo: match.home_team?.logo_url,
        away_team_logo: match.away_team?.logo_url,
        competition: match.league?.name || "N/A",
        league_country: match.league?.country,
        league_logo: match.league?.logo_url,
        venue: match.stadium || "N/A",
      }));

      // Calculate date range
      const matchDates =
        transformedMatches?.map((m: any) => m.match_date).sort() || [];
      const date_range = {
        from: matchDates[0] || new Date().toISOString().split("T")[0],
        to:
          matchDates[matchDates.length - 1] ||
          new Date().toISOString().split("T")[0],
      };

      return {
        matches: transformedMatches,
        total: transformedMatches?.length || 0,
        date_range,
        view_type,
      };
    } catch (error) {
      console.error("Error in getSchedule:", error);
      throw error;
    }
  }

  /**
   * Get live match data
   */
  async getLiveMatchData(matchId: string): Promise<LiveMatchData | null> {
    try {
      const { data: match, error: matchError } = await this.supabase
        .from("matches")
        .select("*")
        .eq("id", matchId)
        .single();

      if (matchError) {
        console.error("Error fetching live match:", matchError);
        return null;
      }

      const { data: events, error: eventsError } = await this.supabase
        .from("match_events")
        .select("*")
        .eq("match_id", matchId)
        .order("minute", { ascending: true });

      if (eventsError) {
        console.error("Error fetching match events:", eventsError);
      }

      const { data: commentary, error: commentaryError } = await this.supabase
        .from("match_commentary")
        .select("*")
        .eq("match_id", matchId)
        .order("minute", { ascending: true });

      if (commentaryError) {
        console.error("Error fetching match commentary:", commentaryError);
      }

      return {
        match,
        events: events || [],
        commentary: commentary || [],
        last_updated: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error in getLiveMatchData:", error);
      return null;
    }
  }

  /**
   * Add match event
   */
  async addMatchEvent(
    eventData: Omit<MatchEvent, "id" | "created_at">
  ): Promise<MatchEvent> {
    try {
      const { data, error } = await this.supabase
        .from("match_events")
        .insert({
          ...eventData,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding match event:", error);
        throw new Error(`Failed to add match event: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error in addMatchEvent:", error);
      throw error;
    }
  }

  /**
   * Add match commentary
   */
  async addMatchCommentary(
    commentaryData: Omit<MatchCommentary, "id" | "created_at">
  ): Promise<MatchCommentary> {
    try {
      const { data, error } = await this.supabase
        .from("match_commentary")
        .insert({
          ...commentaryData,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding match commentary:", error);
        throw new Error(`Failed to add match commentary: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error in addMatchCommentary:", error);
      throw error;
    }
  }
}

export { MatchService };
export const matchService = MatchService.createWithDefaultClient();
