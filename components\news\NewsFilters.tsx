'use client';

import React from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Row,
  Col,
} from 'antd';
import { SearchOutlined, ClearOutlined } from '@ant-design/icons';
import { PostFilters } from '@/types/news.types';

const { Option } = Select;

interface NewsFiltersProps {
  filters: PostFilters;
  onFiltersChange: (filters: PostFilters) => void;
  onReset: () => void;
  loading?: boolean;
}

export const NewsFilters: React.FC<NewsFiltersProps> = ({
  filters,
  onFiltersChange,
  onReset,
  loading = false,
}) => {
  const [form] = Form.useForm();

  const handleValuesChange = (changedValues: any, allValues: any) => {
    const newFilters: PostFilters = {
      search: allValues.search || undefined,
      published: allValues.status === 'published' ? true : 
                allValues.status === 'draft' ? false : undefined,
      author_id: allValues.author_id || undefined,
      type: allValues.type || undefined,
    };
    onFiltersChange(newFilters);
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  return (
    <Card size="small" style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
        initialValues={{
          search: filters.search,
          status: filters.published === true ? 'published' : 
                 filters.published === false ? 'draft' : undefined,
          author_id: filters.author_id,
          type: filters.type,
        }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={6}>
            <Form.Item name="search" label="Tìm kiếm">
              <Input
                placeholder="Tìm theo tiêu đề, nội dung..."
                prefix={<SearchOutlined />}
                allowClear
              />
            </Form.Item>
          </Col>
          
          <Col xs={24} sm={12} md={4}>
            <Form.Item name="status" label="Trạng thái">
              <Select placeholder="Tất cả" allowClear>
                <Option value="published">Đã xuất bản</Option>
                <Option value="draft">Bản nháp</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col xs={24} sm={12} md={4}>
            <Form.Item name="type" label="Loại">
              <Select placeholder="Tất cả" allowClear>
                <Option value="article">Bài viết</Option>
                <Option value="page">Trang</Option>
                <Option value="category">Danh mục</Option>
                <Option value="tag">Thẻ</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col xs={24} sm={12} md={4}>
            <Form.Item name="author_id" label="Tác giả">
              <Select placeholder="Tất cả" allowClear>
                {/* TODO: Load authors from API */}
                <Option value="author1">Tác giả 1</Option>
                <Option value="author2">Tác giả 2</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col xs={24} sm={24} md={6}>
            <Form.Item label=" ">
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  Tìm kiếm
                </Button>
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleReset}
                >
                  Xóa bộ lọc
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default NewsFilters;