import type { User } from "@supabase/supabase-js";

export interface Profile {
  id: string;
  email: string;
  full_name: string;
  role: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  fullName: string;
  role: "admin" | "blv" | "member";
}

export interface AuthResponse {
  user: User | null;
  error: Error | null;
}

export interface ProfileResponse {
  profile: Profile | null;
  error: Error | null;
}
