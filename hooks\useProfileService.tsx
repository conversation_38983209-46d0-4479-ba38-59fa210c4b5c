"use client";

import { useState, useCallback } from "react";
import { profileService } from "@/services";
import type { Profile, UpdateProfileData } from "@/types";

export function useProfileService() {
  const [loading, setLoading] = useState(false);

  const getProfile = useCallback(async (userId: string) => {
    try {
      setLoading(true);
      const { profile, error } = await profileService.getProfile(userId);
      return { profile, error };
    } catch (error) {
      return {
        profile: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const updateProfile = useCallback(
    async (userId: string, updates: UpdateProfileData) => {
      try {
        setLoading(true);
        const { profile, error } = await profileService.updateProfile(
          userId,
          updates
        );
        return { profile, error };
      } catch (error) {
        return {
          profile: null,
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const getAllProfiles = useCallback(async () => {
    try {
      setLoading(true);
      const { profiles, error } = await profileService.getAllProfiles();
      return { profiles, error };
    } catch (error) {
      return {
        profiles: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const getProfilesByRole = useCallback(async (role: string) => {
    try {
      setLoading(true);
      const { profiles, error } = await profileService.getProfilesByRole(role);
      return { profiles, error };
    } catch (error) {
      return {
        profiles: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const searchProfiles = useCallback(async (query: string) => {
    try {
      setLoading(true);
      const { profiles, error } = await profileService.searchProfiles(query);
      return { profiles, error };
    } catch (error) {
      return {
        profiles: [],
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProfile = useCallback(async (userId: string) => {
    try {
      setLoading(true);
      const { error } = await profileService.deleteProfile(userId);
      return { error };
    } catch (error) {
      return {
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadAvatar = useCallback(async (userId: string, file: File) => {
    try {
      setLoading(true);
      const { url, error } = await profileService.uploadAvatar(userId, file);
      return { url, error };
    } catch (error) {
      return {
        url: null,
        error: error instanceof Error ? error : new Error("Unknown error"),
      };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    getProfile,
    updateProfile,
    getAllProfiles,
    getProfilesByRole,
    searchProfiles,
    deleteProfile,
    uploadAvatar,
  };
}
