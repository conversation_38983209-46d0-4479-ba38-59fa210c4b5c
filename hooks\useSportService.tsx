import { useState, useEffect, useCallback } from "react";
import { sportService, type Sport } from "@/services/sport.service";

export const useSportService = () => {
  const [sports, setSports] = useState<Sport[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load sports
  const loadSports = useCallback(
    async (forceRefresh = false) => {
      if (sports.length > 0 && !forceRefresh) {
        return; // Use cached data
      }

      setLoading(true);
      setError(null);

      try {
        const response = await sportService.getSports();

        if (response.error) {
          setError(response.error);
        } else {
          setSports(response.data);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [sports.length]
  );

  // Create sport
  const createSport = useCallback(
    async (
      sportData: Omit<Sport, "sport_id" | "created_at" | "updated_at">
    ) => {
      setLoading(true);
      setError(null);

      try {
        const response = await sportService.createSport(sportData);

        if (response.error) {
          setError(response.error);
          return { success: false, error: response.error };
        } else {
          // Add to local state
          setSports(prev => [...prev, response.data!]);
          return { success: true, data: response.data };
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Update sport
  const updateSport = useCallback(
    async (
      sportId: string,
      updates: Partial<Omit<Sport, "sport_id" | "created_at" | "updated_at">>
    ) => {
      setLoading(true);
      setError(null);

      try {
        const response = await sportService.updateSport(sportId, updates);

        if (response.error) {
          setError(response.error);
          return { success: false, error: response.error };
        } else {
          // Update local state
          setSports(prev =>
            prev.map(sport =>
              sport.sport_id === sportId ? response.data! : sport
            )
          );
          return { success: true, data: response.data };
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Delete sport
  const deleteSport = useCallback(async (sportId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await sportService.deleteSport(sportId);

      if (response.error) {
        setError(response.error);
        return { success: false, error: response.error };
      } else {
        // Remove from local state
        setSports(prev => prev.filter(sport => sport.sport_id !== sportId));
        return { success: true };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Load sports on mount
  useEffect(() => {
    loadSports();
  }, [loadSports]);

  return {
    sports,
    loading,
    error,
    loadSports,
    createSport,
    updateSport,
    deleteSport,
    clearError,
  };
};
