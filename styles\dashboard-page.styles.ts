import styled from "styled-components";
import { Layout, Menu } from "antd";

const { Header, Sider, Content } = Layout;

export const DashboardWrapper = styled(Layout)`
  min-height: 100vh;
`;

export const StyledHeader = styled(Header)`
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
`;

export const LogoSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const LogoIcon = styled.div`
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
`;

export const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const StyledSider = styled(Sider)`
  background: #fff;
  border-right: 1px solid #e8e8e8;

  .ant-layout-sider-trigger {
    background: #f5f5f5;
    color: #666;
  }
`;

export const StyledContent = styled(Content)`
  background: #f8fafc;
  padding: 0;
  overflow: hidden;
`;

export const MenuContainer = styled.div`
  padding: 16px 0;
`;

export const StyledMenu = styled(Menu)`
  border: none;

  .ant-menu-item {
    margin: 4px 12px;
    border-radius: 8px;
    height: 44px;
    line-height: 44px;

    &:hover {
      background: #f0f9ff;
      color: #1890ff;
    }

    &.ant-menu-item-selected {
      background: #eff6ff;
      color: #1890ff;

      &::after {
        display: none;
      }
    }
  }
`;

export const DevelopmentPageContainer = styled.div`
  padding: 24px;
  text-align: center;
  color: #666;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

export const DevelopmentMessageBox = styled.div`
  margin-top: 24px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  max-width: 400px;
`;
