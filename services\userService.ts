import { SupabaseClient } from '@supabase/supabase-js';
import { Profile } from '@/types/profile.types';

interface GetUsersParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  can_access_betting?: boolean;
}

interface GetUsersResponse {
  users: Profile[];
  total: number;
  page: number;
  limit: number;
}

export class UserService {
  constructor(private supabase: SupabaseClient) {
    if (!supabase) {
      throw new Error('Supabase client is required');
    }
  }

  private validatePagination(page: number, limit: number): void {
    if (page < 1) {
      throw new Error('Page must be greater than 0');
    }
    if (limit < 1 || limit > 100) {
      throw new Error('Limit must be between 1 and 100');
    }
  }

  private sanitizeSearchTerm(search: string): string {
    // Remove special characters that could cause issues with PostgreSQL
    return search.replace(/[%_\\]/g, '\\$&').trim();
  }

  private validateUserId(userId: string): void {
    if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
      throw new Error('User ID không hợp lệ');
    }
  }

  async getUsers(params: GetUsersParams = {}): Promise<GetUsersResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        role,
        can_access_betting
      } = params;

      // Validate parameters
      this.validatePagination(page, limit);

      let query = this.supabase
        .from('profiles')
        .select('*', { count: 'exact' });

      // Apply filters
      if (search) {
        const sanitizedSearch = this.sanitizeSearchTerm(search);
        if (sanitizedSearch) {
          query = query.or(`full_name.ilike.%${sanitizedSearch}%,email.ilike.%${sanitizedSearch}%`);
        }
      }

      if (role && ['admin', 'blv', 'member'].includes(role)) {
        query = query.eq('role', role);
      }

      if (can_access_betting !== undefined) {
        query = query.eq('can_access_betting', can_access_betting);
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      // Order by created_at desc
      query = query.order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        console.error('Database error in getUsers:', error);
        throw new Error(`Không thể tải danh sách users: ${error.message}`);
      }

      return {
        users: data || [],
        total: count || 0,
        page,
        limit,
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Có lỗi không xác định khi tải danh sách users');
    }
  }

  async updateBettingPermission(userId: string, canAccessBetting: boolean): Promise<boolean> {
    try {
      // Validate input
      this.validateUserId(userId);
      
      if (typeof canAccessBetting !== 'boolean') {
        throw new Error('canAccessBetting phải là boolean');
      }


      // First check if user exists
      const { data: existingUser, error: checkError } = await this.supabase
        .from('profiles')
        .select('id, can_access_betting')
        .eq('id', userId)
        .single();

      if (checkError) {
        throw new Error(`Không tìm thấy user: ${checkError.message}`);
      }

      if (!existingUser) {
        throw new Error('Không tìm thấy user');
      }

      // Perform update
      const { data, error, count } = await this.supabase
        .from('profiles')
        .update({ can_access_betting: canAccessBetting })
        .eq('id', userId)
        .select('id, can_access_betting');

      if (error) {
        throw new Error(`Không thể cập nhật quyền soi kèo: ${error.message}`);
      }


      // Verify the update
      if (!data || data.length === 0) {
        
        // Check if user exists
        const { data: existingUser } = await this.supabase
          .from('profiles')
          .select('id, can_access_betting')
          .eq('id', userId)
          .single();
          
        if (!existingUser) {
          throw new Error('User không tồn tại');
        }
        
        // Check if value is already the same
        if (existingUser.can_access_betting === canAccessBetting) {
          return true;
        }
        
        throw new Error('Không thể cập nhật dữ liệu - có thể do quyền truy cập');
      }

      const updatedUser = data[0];
      if (updatedUser.can_access_betting !== canAccessBetting) {
        throw new Error('Cập nhật không thành công - giá trị không đúng');
      }

      return true;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Có lỗi không xác định khi cập nhật quyền soi kèo');
    }
  }



  async getUserById(userId: string): Promise<Profile | null> {
    try {
      // Validate input
      this.validateUserId(userId);

      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Database error in getUserById:', error);
        
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          return null; // User not found
        }
        
        throw new Error(`Không thể tải thông tin user: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Có lỗi không xác định khi tải thông tin user');
    }
  }

  // Bulk operations
  async updateMultipleBettingPermissions(userIds: string[], canAccessBetting: boolean): Promise<boolean> {
    try {
      if (!Array.isArray(userIds) || userIds.length === 0) {
        throw new Error('Danh sách user IDs không hợp lệ');
      }

      // Validate all user IDs
      userIds.forEach(id => this.validateUserId(id));

      if (typeof canAccessBetting !== 'boolean') {
        throw new Error('canAccessBetting phải là boolean');
      }

      const { error } = await this.supabase
        .from('profiles')
        .update({ can_access_betting: canAccessBetting })
        .in('id', userIds);

      if (error) {
        console.error('Database error in updateMultipleBettingPermissions:', error);
        throw new Error(`Không thể cập nhật quyền soi kèo cho nhiều users: ${error.message}`);
      }

      return true;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Có lỗi không xác định khi cập nhật quyền soi kèo cho nhiều users');
    }
  }

  async getUserStats(): Promise<{
    totalUsers: number;
    bettingUsers: number;
    adminUsers: number;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select('role, can_access_betting');

      if (error) {
        console.error('Database error in getUserStats:', error);
        throw new Error(`Không thể tải thống kê users: ${error.message}`);
      }

      const stats = {
        totalUsers: data?.length || 0,
        bettingUsers: data?.filter(u => u.can_access_betting).length || 0,
        adminUsers: data?.filter(u => u.role === 'admin').length || 0,
      };

      return stats;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Có lỗi không xác định khi tải thống kê users');
    }
  }
}