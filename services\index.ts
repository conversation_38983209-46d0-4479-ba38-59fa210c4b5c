// Auth Service
export { authService } from "./auth.service";

// Chat Service
export { chatService } from "./chat.service";

// Profile Service
export { profileService } from "./profile.service";


// Match Service
export { matchService } from "./match.service";

// Sport Service
export { sportService } from "./sport.service";

// Betting Service
export { BettingService } from "./betting.service";

// News Service
export { NewsService } from "./news.service";

// Re-export types from types directory
export type {
  Profile as AuthProfile,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  ProfileResponse as AuthProfileResponse,
} from "@/types/auth.types";

export type {
  ChatRoom,
  ChatMessage,
  CreateChatData,
  ChatResponse,
  MessagesResponse,
} from "@/types/chat.types";

export type {
  Profile,
  UpdateProfileData,
  ProfileResponse,
  ProfilesResponse,
} from "@/types/profile.types";


export type {
  Match,
  MatchFilters,
  MatchResponse,
  MatchStats,
  CreateMatchData,
  UpdateMatchData,
  ScheduleFilters,
  ScheduleResponse,
  MatchEvent,
  MatchCommentary,
  LiveMatchData,
} from "@/types/match.types";

export type {
  BettingOdds,
  CreateBettingOddsData,
  UpdateBettingOddsData,
  BettingOddsNotification,
} from "@/types/betting.types";

export type {
  Post,
  PageSeo,
  PostWithSeo,
  PostWithAuthor,
  PublishedPostWithSeo,
  CreatePostRequest,
  UpdatePostRequest,
  CreatePageSeoRequest,
  UpdatePageSeoRequest,
  PostsResponse,
  PostResponse,
  PageSeoResponse,
  PostFilters,
  PaginationParams,
  PostsQueryParams,
  NewsContextType,
} from "@/types/news.types";
