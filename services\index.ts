// Auth Service
export { authService } from "./auth.service";

// Chat Service
export { chatService } from "./chat.service";

// Profile Service
export { profileService } from "./profile.service";

// Match Service
export { matchService } from "./match.service";

// Sport Service
export { sportService } from "./sport.service";

// Betting Service
export { BettingService } from "./betting.service";

// News Service
export { NewsService } from "./news.service";

// Forum Service
export { ForumService } from "./forum.service";

// Upload Service
export { uploadService } from "./upload.service";

// Re-export types from types directory
export type {
  Profile as AuthProfile,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  ProfileResponse as AuthProfileResponse,
} from "@/types/auth.types";

export type {
  ChatRoom,
  ChatMessage,
  CreateChatData,
  ChatResponse,
  MessagesResponse,
} from "@/types/chat.types";

export type {
  Profile,
  UpdateProfileData,
  ProfileResponse,
  ProfilesResponse,
} from "@/types/profile.types";

export type {
  Match,
  MatchFilters,
  MatchResponse,
  MatchStats,
  CreateMatchData,
  UpdateMatchData,
  ScheduleFilters,
  ScheduleResponse,
  MatchEvent,
  MatchCommentary,
  LiveMatchData,
} from "@/types/match.types";

export type {
  BettingOdds,
  CreateBettingOddsData,
  UpdateBettingOddsData,
  BettingOddsNotification,
} from "@/types/betting.types";

export type {
  Post,
  PostWithSeo,
  PostWithAuthor,
  PublishedPostWithSeo,
  CreatePostRequest,
  UpdatePostRequest,
  PostsResponse,
  PostResponse,
  PostFilters,
  PaginationParams,
  PostsQueryParams,
  NewsContextType,
  PostEditorProps,
  PostListProps,
  PostCardProps,
  PostValidationSchema,
  SeoValidationSchema,
  NewsError,
  ValidationError,
} from "@/types/news.types";

// Forum types
export type {
  ForumCategory,
  ForumPost,
  ForumComment,
  ForumPostLike,
  ForumCommentLike,
  CreateForumPostDto,
  UpdateForumPostDto,
  CreateForumCommentDto,
  UpdateForumCommentDto,
  ForumPostsResponse,
  ForumCommentsResponse,
  ForumPostFilters,
  ForumCommentFilters,
  PaginationOptions,
  ForumPostWithStats,
  ForumStats,
  UploadedFile,
  UploadResponse,
  PostStatus,
  CommentStatus,
} from "@/types/forum.types";
