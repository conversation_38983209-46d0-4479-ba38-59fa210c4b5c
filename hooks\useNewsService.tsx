'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { NewsService } from '@/services/news.service';
import { useSupabaseClient } from '@/contexts/SupabaseContext';
import {
  Post,
  PostWithSeo,
  PublishedPostWithSeo,
  CreatePostRequest,
  UpdatePostRequest,
  PostsQueryParams,
  PostFilters
} from '@/types/news.types';

interface UseNewsServiceReturn {
  // State
  posts: PostWithSeo[];
  loading: boolean;
  error: string | null;

  // Post operations
  createPost: (post: CreatePostRequest) => Promise<PostWithSeo | null>;
  updatePost: (id: string, post: UpdatePostRequest) => Promise<PostWithSeo | null>;
  deletePost: (id: string) => Promise<boolean>;
  publishPost: (id: string) => Promise<PostWithSeo | null>;
  unpublishPost: (id: string) => Promise<PostWithSeo | null>;

  // SEO operations removed - SEO fields now part of Post

  // Fetch operations
  fetchPosts: (params?: PostsQueryParams) => Promise<void>;
  fetchPost: (id: string) => Promise<PostWithSeo | null>;
  fetchPublishedPosts: () => Promise<PublishedPostWithSeo[]>;
  fetchPostBySlug: (slug: string) => Promise<PostWithSeo | null>;

  // Utility functions
  generateSlug: (title: string) => string;
  validateSlug: (slug: string, excludeId?: string) => Promise<boolean>;
  refreshPosts: () => Promise<void>;
}

export function useNewsService(initialParams?: PostsQueryParams): UseNewsServiceReturn {
  const supabaseClient = useSupabaseClient();
  const newsService = useMemo(() => new NewsService(supabaseClient), [supabaseClient]);
  
  const [posts, setPosts] = useState<PostWithSeo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Helper function to handle errors
  const handleError = useCallback((error: any, operation: string) => {
    const errorMessage = `Error ${operation}: ${error?.message || 'Unknown error'}`;
    setError(errorMessage);
    console.error(errorMessage, error);
  }, []);

  // Post operations
  const createPost = useCallback(async (postData: CreatePostRequest): Promise<PostWithSeo | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await newsService.createPost(postData);
      
      if (result) {
        setPosts(prev => [result, ...prev]);
      }
      
      return result;
    } catch (error) {
      handleError(error, 'creating post');
      return null;
    } finally {
      setLoading(false);
    }
  }, [newsService, handleError]);

  const updatePost = useCallback(async (id: string, postData: UpdatePostRequest): Promise<PostWithSeo | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await newsService.updatePost(id, postData);
      
      if (result) {
        setPosts(prev => prev.map(post => post.id === id ? result : post));
      }
      
      return result;
    } catch (error) {
      handleError(error, 'updating post');
      return null;
    } finally {
      setLoading(false);
    }
  }, [newsService, handleError]);

  const deletePost = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await newsService.deletePost(id);
      
      if (success) {
        setPosts(prev => prev.filter(post => post.id !== id));
      }
      
      return success;
    } catch (error) {
      handleError(error, 'deleting post');
      return false;
    } finally {
      setLoading(false);
    }
  }, [newsService, handleError]);

  const publishPost = useCallback(async (id: string): Promise<PostWithSeo | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await newsService.publishPost(id);
      
      if (result) {
        setPosts(prev => prev.map(post => post.id === id ? result : post));
      }
      
      return result;
    } catch (error) {
      handleError(error, 'publishing post');
      return null;
    } finally {
      setLoading(false);
    }
  }, [newsService, handleError]);

  const unpublishPost = useCallback(async (id: string): Promise<PostWithSeo | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await newsService.unpublishPost(id);
      
      if (result) {
        setPosts(prev => prev.map(post => post.id === id ? result : post));
      }
      
      return result;
    } catch (error) {
      handleError(error, 'unpublishing post');
      return null;
    } finally {
      setLoading(false);
    }
  }, [newsService, handleError]);

  // SEO operations removed - SEO fields now part of Post operations

  // Fetch operations
  const fetchPosts = useCallback(async (params?: PostsQueryParams): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await newsService.getPosts(params || initialParams || {});
      setPosts(result);
    } catch (error) {
      handleError(error, 'fetching posts');
    } finally {
      setLoading(false);
    }
  }, [newsService, initialParams, handleError]);

  const fetchPost = useCallback(async (id: string): Promise<PostWithSeo | null> => {
    try {
      setError(null);
      
      const result = await newsService.getPost(id);
      return result;
    } catch (error) {
      handleError(error, 'fetching post');
      return null;
    }
  }, [newsService, handleError]);

  const fetchPublishedPosts = useCallback(async (): Promise<PublishedPostWithSeo[]> => {
    try {
      setError(null);
      
      const result = await newsService.getPublishedPosts();
      return result;
    } catch (error) {
      handleError(error, 'fetching published posts');
      return [];
    }
  }, [newsService, handleError]);

  const fetchPostBySlug = useCallback(async (slug: string): Promise<PostWithSeo | null> => {
    try {
      setError(null);
      
      const result = await newsService.getPostBySlug(slug);
      return result;
    } catch (error) {
      handleError(error, 'fetching post by slug');
      return null;
    }
  }, [newsService, handleError]);

  // Utility functions
  const generateSlug = useCallback((title: string): string => {
    return newsService.generateSlug(title);
  }, [newsService]);

  const validateSlug = useCallback(async (slug: string, excludeId?: string): Promise<boolean> => {
    try {
      return await newsService.validateSlug(slug, excludeId);
    } catch (error) {
      handleError(error, 'validating slug');
      return false;
    }
  }, [newsService, handleError]);

  const refreshPosts = useCallback(async (): Promise<void> => {
    await fetchPosts(initialParams);
  }, [fetchPosts, initialParams]);

  // Load initial data
  useEffect(() => {
    if (initialParams) {
      fetchPosts(initialParams);
    }
  }, [fetchPosts, initialParams]);

  return {
    // State
    posts,
    loading,
    error,

    // Post operations
    createPost,
    updatePost,
    deletePost,
    publishPost,
    unpublishPost,

    // SEO operations removed - SEO fields now part of Post operations

    // Fetch operations
    fetchPosts,
    fetchPost,
    fetchPublishedPosts,
    fetchPostBySlug,

    // Utility functions
    generateSlug,
    validateSlug,
    refreshPosts
  };
}

export default useNewsService;