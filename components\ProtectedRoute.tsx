"use client";

import { useAuthService } from "@/hooks/useAuthService";
import { Spin } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, type ReactNode } from "react";

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  redirectTo?: string;
}

export default function ProtectedRoute({
  children,
  requiredRole,
  redirectTo = "/auth/login",
}: ProtectedRouteProps) {
  const { user, profile, loading } = useAuthService();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      // Nếu không có user, redirect đến trang login
      if (!user) {
        router.push(redirectTo);
        return;
      }

      // Nếu có yêu cầu role cụ thể và user không có role đó
      if (requiredRole && profile?.role !== requiredRole) {
        router.push("/unauthorized");
        return;
      }
    }
  }, [user, profile, loading, router, requiredRole, redirectTo]);

  // Hiển thị loading spinner khi đang kiểm tra authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" tip="Đang kiểm tra quyền truy cập...">
          <div className="w-20 h-20" />
        </Spin>
      </div>
    );
  }

  // Nếu không có user, không render gì (sẽ redirect)
  if (!user) {
    return null;
  }

  // Nếu có yêu cầu role cụ thể và user không có role đó
  if (requiredRole && profile?.role !== requiredRole) {
    return null;
  }

  // Render children nếu user đã authenticated và có quyền truy cập
  return <>{children}</>;
}