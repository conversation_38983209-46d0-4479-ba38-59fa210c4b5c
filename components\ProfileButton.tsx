"use client";

import React, { useState } from 'react';
import { Avatar, Button, Dropdown, Space, Typography } from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import { useProfile } from '@/hooks/useProfile';
import ProfileUpdateModal from './ProfileUpdateModal';
import { createClient } from '@/lib/supabase/client';
import type { MenuProps } from 'antd';

const { Text } = Typography;

interface ProfileButtonProps {
  showName?: boolean;
  size?: 'small' | 'middle' | 'large';
}

export default function ProfileButton({
  showName = true,
  size = 'middle'
}: ProfileButtonProps) {
  const { profile, loading, updateProfile } = useProfile();
  const [modalVisible, setModalVisible] = useState(false);
  const supabase = createClient();

  // Handle logout
  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Handle open profile modal
  const handleOpenProfile = () => {
    console.log('Opening profile modal...');
    setModalVisible(true);
  };

  // Handle close profile modal
  const handleCloseModal = () => {
    setModalVisible(false);
  };

  // Handle profile update success
  const handleProfileUpdateSuccess = (updatedProfile: any) => {
    updateProfile(updatedProfile);
  };

  // Dropdown menu items
  const menuItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: 'Hồ sơ',
      icon: <UserOutlined />,
      onClick: handleOpenProfile,
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: 'Đăng xuất',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
      danger: true,
    },
  ];

  if (loading) {
    return (
      <Button loading size={size}>
        Đang tải...
      </Button>
    );
  }

  if (!profile) {
    return (
      <Button 
        type="primary" 
        size={size}
        onClick={() => window.location.href = '/login'}
      >
        Đăng nhập
      </Button>
    );
  }

  return (
    <>
      <Dropdown
        menu={{ items: menuItems }}
        placement="bottomRight"
        trigger={['click']}
      >
        <Button
          type="text"
          style={{ 
            height: 'auto', 
            padding: '4px 8px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <Space align="center">
            <Avatar
              size={size === 'small' ? 24 : size === 'large' ? 40 : 32}
              src={profile.avatar_url}
              icon={<UserOutlined />}
            />
            {showName && (
              <div style={{ textAlign: 'left' }}>
                <Text strong style={{ fontSize: size === 'small' ? '12px' : '14px' }}>
                  {profile.full_name}
                </Text>
                {size !== 'small' && (
                  <div>
                    <Text 
                      type="secondary" 
                      style={{ fontSize: '12px' }}
                    >
                      {profile.email}
                    </Text>
                  </div>
                )}
              </div>
            )}
          </Space>
        </Button>
      </Dropdown>

      <ProfileUpdateModal
        visible={modalVisible}
        onCancel={handleCloseModal}
        onSuccess={handleProfileUpdateSuccess}
        currentProfile={profile}
      />
    </>
  );
}
