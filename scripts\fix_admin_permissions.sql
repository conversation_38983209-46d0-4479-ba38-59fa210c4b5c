-- ========================================
-- FIX ADMIN PERMISSIONS AND FOREIGN KEY
-- ========================================

-- 1. Fix foreign key constraint for messages table
-- Drop existing constraint and recreate to reference profiles(id)
ALTER TABLE public.messages DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;
ALTER TABLE public.messages ADD CONSTRAINT messages_sender_id_fkey 
FOREIGN KEY (sender_id) REFERENCES public.profiles(id) ON DELETE SET NULL;

-- 2. Add admin role to profiles table if not exists
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'member' CHECK (role IN ('admin', 'blv', 'member'));

-- 3. Update existing admin users
UPDATE public.profiles 
SET role = 'admin' 
WHERE email IN ('<EMAIL>', '<EMAIL>');

UPDATE public.profiles 
SET role = 'blv' 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

-- 4. Create admin-friendly policies for chat_rooms
-- Drop existing policies
DROP POLICY IF EXISTS "Users can view rooms they participate in" ON public.chat_rooms;
DROP POLICY IF EXISTS "Users can view rooms they participate in or admins can view all" ON public.chat_rooms;
DROP POLICY IF EXISTS "Users can create rooms" ON public.chat_rooms;
DROP POLICY IF EXISTS "Users can create rooms or admins can create any room" ON public.chat_rooms;
DROP POLICY IF EXISTS "Users can delete rooms they created" ON public.chat_rooms;
DROP POLICY IF EXISTS "Users can delete rooms they created or admins can delete any room" ON public.chat_rooms;

-- Create new policies with admin access (simplified to avoid recursion)
CREATE POLICY "Users can view rooms they participate in or admins can view all" ON public.chat_rooms FOR SELECT USING (
  type = 'general' OR 
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid() AND p.role = 'admin'
  )
);

CREATE POLICY "Users can create rooms or admins can create any room" ON public.chat_rooms FOR INSERT WITH CHECK (
  auth.uid() = created_by OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid() AND p.role = 'admin'
  )
);

CREATE POLICY "Users can delete rooms they created or admins can delete any room" ON public.chat_rooms 
FOR DELETE USING (
  auth.uid() = created_by OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid() AND p.role = 'admin'
  )
);

-- 5. Create admin-friendly policies for chat_room_members
-- Drop existing policies
DROP POLICY IF EXISTS "Users can view room members" ON public.chat_room_members;
DROP POLICY IF EXISTS "Users can view room members or admins can view all" ON public.chat_room_members;
DROP POLICY IF EXISTS "Users can join rooms" ON public.chat_room_members;
DROP POLICY IF EXISTS "Users can join rooms or admins can add anyone" ON public.chat_room_members;

-- Create simplified policies to avoid recursion
CREATE POLICY "Users can view room members or admins can view all" ON public.chat_room_members FOR SELECT USING (
  auth.uid() = user_id OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid() AND p.role = 'admin'
  )
);

CREATE POLICY "Users can join rooms or admins can add anyone" ON public.chat_room_members FOR INSERT WITH CHECK (
  auth.uid() = user_id OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid() AND p.role = 'admin'
  )
);

-- 6. Create admin-friendly policies for messages
-- Drop existing policies
DROP POLICY IF EXISTS "Users can view messages" ON public.messages;
DROP POLICY IF EXISTS "Users can view messages or admins can view all" ON public.messages;
DROP POLICY IF EXISTS "Users can send messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send messages or admins can send to any room" ON public.messages;

-- Create simplified policies to avoid recursion
CREATE POLICY "Users can view messages or admins can view all" ON public.messages FOR SELECT USING (
  sender_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid() AND p.role = 'admin'
  ) OR
  EXISTS (
    SELECT 1 FROM public.chat_rooms cr
    WHERE cr.id = messages.room_id AND (cr.type = 'general' OR cr.created_by = auth.uid())
  )
);

CREATE POLICY "Users can send messages or admins can send to any room" ON public.messages FOR INSERT WITH CHECK (
  sender_id = auth.uid() AND (
    EXISTS (
      SELECT 1 FROM public.profiles p
      WHERE p.id = auth.uid() AND p.role = 'admin'
    ) OR
    EXISTS (
      SELECT 1 FROM public.chat_rooms cr
      WHERE cr.id = messages.room_id AND (cr.type = 'general' OR cr.created_by = auth.uid())
    )
  )
);

-- 7. Create function to auto-add admin to all rooms
CREATE OR REPLACE FUNCTION public.auto_add_admin_to_room()
RETURNS TRIGGER AS $$
BEGIN
  -- Add all admins to new rooms automatically
  INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
  SELECT NEW.id, p.id, NOW()
  FROM public.profiles p
  WHERE p.role = 'admin'
  ON CONFLICT (room_id, user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create trigger to auto-add admins to new rooms
DROP TRIGGER IF EXISTS auto_add_admin_to_room_trigger ON public.chat_rooms;
CREATE TRIGGER auto_add_admin_to_room_trigger
  AFTER INSERT ON public.chat_rooms
  FOR EACH ROW EXECUTE FUNCTION public.auto_add_admin_to_room();

-- 9. Add existing admins to all existing rooms
INSERT INTO public.chat_room_members (room_id, user_id, joined_at)
SELECT cr.id, p.id, NOW()
FROM public.chat_rooms cr
CROSS JOIN public.profiles p
WHERE p.role = 'admin'
ON CONFLICT (room_id, user_id) DO NOTHING;

COMMIT;

-- Success message
SELECT 'Admin permissions and foreign key constraints have been successfully updated!' as status;