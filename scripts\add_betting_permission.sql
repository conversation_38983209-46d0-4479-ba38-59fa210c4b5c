-- Add betting permission column to profiles table
-- This allows admin to control which users can access betting features

-- Add can_access_betting column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS can_access_betting BOOLEAN DEFAULT false;

-- Update existing users to have betting access (optional - you can set this to false if you want to manually enable)
-- UPDATE public.profiles SET can_access_betting = true WHERE role IN ('admin', 'blv');

-- Create index for better performance on betting permission queries
CREATE INDEX IF NOT EXISTS idx_profiles_can_access_betting ON public.profiles(can_access_betting);