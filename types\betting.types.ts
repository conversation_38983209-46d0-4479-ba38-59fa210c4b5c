export interface ExpertInfo {
    id: string;
    name: string;
    email?: string;
    avatar_url?: string;
    role?: string;
}

export interface BettingOdds {
    id: string;
    expert: string | ExpertInfo;
    type?: string;
    tournament: string;
    team_name: string;
    closing_bet: string;
    saying: string;
    status: 'win' | 'lose' | null;
    created_at: string;
    updated_at: string;
    created_by: string;
    sent_at: string | null;
}

export interface CreateBettingOddsData {
    expert?: string | ExpertInfo; // Will be auto-populated from profile
    type?: string;
    tournament: string;
    team_name: string;
    closing_bet: string;
    saying: string;
    status?: 'win' | 'lose' | null;
}

export interface UpdateBettingOddsData {
    expert?: string | ExpertInfo;
    type?: string;
    tournament?: string;
    team_name?: string;
    closing_bet?: string;
    saying?: string;
    status?: 'win' | 'lose' | null;
}

export interface BettingOddsNotification {
    type: 'betting_odds_created' | 'betting_odds_updated';
    data: BettingOdds;
}
