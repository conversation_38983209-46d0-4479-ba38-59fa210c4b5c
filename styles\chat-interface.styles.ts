import styled from "styled-components";

export const ChatContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
`;

export const ChatHeader = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;

  .dark & {
    border-bottom-color: #303030;
    background: #141414;
  }
`;

export const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
`;

export const MessageBubble = styled.div<{ $isOwn: boolean }>`
  display: flex;
  flex-direction: ${({ $isOwn }) => ($isOwn ? "row-reverse" : "row")};
  align-items: flex-end;
  margin-bottom: 8px;
`;

export const MessageContent = styled.div<{ $isOwn: boolean }>`
  background-color: ${({ $isOwn }) => ($isOwn ? "#00a2ff" : "#f0f0f0")};
  color: ${({ $isOwn }) => ($isOwn ? "#fff" : "#000")};
  padding: 8px 12px;
  border-radius: 16px;
  max-width: 70%;
`;

export const MessageTime = styled.div<{ $isOwn: boolean }>`
  font-size: 12px;
  color: #888;
  margin-top: 4px;
  margin-left: ${({ $isOwn }) => ($isOwn ? "0" : "8px")};
  margin-right: ${({ $isOwn }) => ($isOwn ? "8px" : "0")};
`;

export const InputContainer = styled.div`
  display: flex;
  padding: 16px;
  border-top: 1px solid #ddd;
`;

export const MessageInput = styled.div`
  display: flex;
  gap: 8px;
  align-items: flex-end;
  flex: 1;
`;

export const FileUploadContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const HighlightedText = styled.span`
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;

  .dark & {
    background-color: #664d03;
    color: #fff3cd;
  }
`;
