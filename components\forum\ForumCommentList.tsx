"use client";

import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Space,
  Typography,
  Card,
  Divider,
  message,
  Empty,
} from "antd";
import { MessageOutlined, ReloadOutlined } from "@ant-design/icons";
import { useForumService } from "@/hooks/useForumService";
import ForumComment from "./ForumComment";
import type {
  ForumComment as ForumCommentType,
  ForumCommentFilters,
} from "@/types/forum.types";

const { Title, Text } = Typography;
const { TextArea } = Input;

interface ForumCommentListProps {
  postId: string;
  currentUserId?: string;
  showCommentForm?: boolean;
  maxLevel?: number;
}

export default function ForumCommentList({
  postId,
  currentUserId,
  showCommentForm = true,
  maxLevel = 3,
}: ForumCommentListProps) {
  const { comments, loading, error, fetchComments, createComment } =
    useForumService();

  const [commentContent, setCommentContent] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);

  useEffect(() => {
    loadComments();
  }, [postId]);

  const loadComments = async () => {
    const filters: ForumCommentFilters = {
      post_id: postId,
      status: "approved", // Only show approved comments by default
    };

    await fetchComments(filters);
  };

  const handleSubmitComment = async () => {
    if (!commentContent.trim()) return;

    setSubmitting(true);
    try {
      const commentData = {
        content: commentContent,
        post_id: postId,
        parent_id: replyingTo || undefined,
      };

      const newComment = await createComment(commentData);
      if (newComment) {
        setCommentContent("");
        setReplyingTo(null);
        loadComments(); // Reload comments to show the new one
        message.success("Bình luận thành công!");
      } else {
        message.error("Không thể tạo bình luận!");
      }
    } catch (error) {
      message.error("Có lỗi xảy ra khi tạo bình luận!");
    } finally {
      setSubmitting(false);
    }
  };

  const handleReply = async (comment: ForumCommentType, content: string) => {
    try {
      const commentData = {
        content,
        post_id: postId,
        parent_id: comment.id,
      };

      const newComment = await createComment(commentData);
      if (newComment) {
        loadComments();
        message.success("Phản hồi thành công!");
        return newComment;
      }
      return null;
    } catch (error) {
      message.error("Không thể phản hồi bình luận!");
      return null;
    }
  };

  const handleUpdate = async (commentId: string, content: string) => {
    try {
      // This would need an updateComment function in the service
      // For now, we'll just reload the comments
      loadComments();
    } catch (error) {
      message.error("Không thể cập nhật bình luận!");
    }
  };

  const handleDelete = async (commentId: string) => {
    try {
      // This would need a deleteComment function in the service
      // For now, we'll just reload the comments
      loadComments();
    } catch (error) {
      message.error("Không thể xóa bình luận!");
    }
  };

  const organizeComments = (comments: ForumCommentType[]) => {
    const commentMap = new Map<
      string,
      ForumCommentType & { replies: ForumCommentType[] }
    >();
    const rootComments: (ForumCommentType & { replies: ForumCommentType[] })[] =
      [];

    // Initialize all comments with empty replies array
    comments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
    });

    // Organize comments into tree structure
    comments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!;

      if (comment.parent_id) {
        const parent = commentMap.get(comment.parent_id);
        if (parent) {
          parent.replies.push(commentWithReplies);
        }
      } else {
        rootComments.push(commentWithReplies);
      }
    });

    return rootComments;
  };

  const renderComment = (
    comment: ForumCommentType & { replies?: ForumCommentType[] },
    level = 1
  ) => {
    return (
      <div key={comment.id}>
        <ForumComment
          comment={comment}
          postId={postId}
          currentUserId={currentUserId}
          level={level}
          maxLevel={maxLevel}
          onReply={handleReply}
          onUpdate={handleUpdate}
          onDelete={handleDelete}
        />

        {/* Render nested replies */}
        {comment.replies &&
          comment.replies.map(reply => renderComment(reply, level + 1))}
      </div>
    );
  };

  const organizedComments = organizeComments(comments);

  return (
    <Card>
      <div style={{ marginBottom: "16px" }}>
        <Title level={4}>
          <MessageOutlined style={{ marginRight: 8 }} />
          Bình luận ({comments.length})
        </Title>
      </div>

      {/* Comment form */}
      {showCommentForm && (
        <>
          <div style={{ marginBottom: "16px" }}>
            <TextArea
              placeholder="Viết bình luận..."
              value={commentContent}
              onChange={e => setCommentContent(e.target.value)}
              rows={3}
              maxLength={2000}
              disabled={submitting}
            />
            <div style={{ marginTop: "8px", textAlign: "right" }}>
              <Space>
                <Button
                  onClick={() => {
                    setCommentContent("");
                    setReplyingTo(null);
                  }}
                  disabled={submitting}
                >
                  Hủy
                </Button>
                <Button
                  type="primary"
                  onClick={handleSubmitComment}
                  loading={submitting}
                  disabled={!commentContent.trim()}
                >
                  {replyingTo ? "Phản hồi" : "Bình luận"}
                </Button>
              </Space>
            </div>
          </div>
          <Divider style={{ margin: "16px 0" }} />
        </>
      )}

      {/* Comments list */}
      {organizedComments.length > 0 ? (
        <div>{organizedComments.map(comment => renderComment(comment))}</div>
      ) : (
        <Empty
          description={
            showCommentForm
              ? "Chưa có bình luận nào. Hãy là người đầu tiên bình luận!"
              : "Chưa có bình luận nào."
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}

      {/* Load more button could be added here */}
      {comments.length > 0 && (
        <div style={{ textAlign: "center", marginTop: "16px" }}>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadComments}
            loading={loading}
          >
            Tải lại bình luận
          </Button>
        </div>
      )}

      {/* Error display */}
      {error && (
        <div style={{ marginTop: "16px", color: "red", textAlign: "center" }}>
          Lỗi: {error}
        </div>
      )}
    </Card>
  );
}
