import type React from "react";
import type { Metadata } from "next";
import { GeistS<PERSON> } from "geist/font/sans";
import { Geist<PERSON><PERSON> } from "geist/font/mono";
import { Analytics } from "@vercel/analytics/next";
import "./globals.css";
import { Suspense } from "react";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { SupabaseProvider } from "@/contexts/SupabaseContext";
import StyledComponentsRegistry from "@/lib/styled-components-registry";
import { ThemeProviderWrapper } from "@/lib/theme-provider-wrapper";
import { AntdProvider } from "@/lib/antd-provider";
// Suppress Ant Design compatibility warnings
import "@/lib/suppress-antd-warnings";

export const metadata: Metadata = {
  title: "Dashboard Chat - Hệ thống chat cho BLV và Admin",
  description: "Hệ thống chat realtime cho Bình luận viên và Quản trị viên",
  generator: "tenan-org",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <body
        className={`font-sans ${GeistSans.variable} ${GeistMono.variable}`}
        suppressHydrationWarning
      >
        <Suspense fallback={null}>
          <StyledComponentsRegistry>
            <ThemeProviderWrapper
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <AntdProvider>
                <SupabaseProvider>
                  <NotificationProvider>{children}</NotificationProvider>
                </SupabaseProvider>
              </AntdProvider>
            </ThemeProviderWrapper>
          </StyledComponentsRegistry>
        </Suspense>
        <Analytics />
      </body>
    </html>
  );
}
