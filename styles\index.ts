// Dashboard styles - only export what's needed
export {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Styled<PERSON>ead<PERSON> as DashboardStyledHeader,
  LogoSection as DashboardLogoSection,
  LogoIcon as DashboardLogoIcon,
  UserSection,
  StyledSider as DashboardStyledSider,
  StyledContent as DashboardStyledContent,
  MenuContainer as DashboardMenuContainer,
  StyledMenu,
  DevelopmentPageContainer,
  DevelopmentMessageBox,
} from "./dashboard-page.styles";

// Chat Container styles - only export what's needed
export {
  ChatWrapper,
  ChatListPanel,
  ChatListHeader,
  ChatTitleContainer,
  SearchContainer,
  ChatListContent,
  SectionHeader,
  ChatItem as ChatContainerChatItem,
  ChatInfo,
  OnlineIndicator,
  ConversationPanel,
  ConversationHeader,
  HeaderLeft,
  UserInfo,
  UserDetails,
  HeaderRight,
  MessagesContainer as ChatContainerMessagesContainer,
  MessageBubble as ChatContainerMessageBubble,
  MessageContent as ChatContainerMessageContent,
  <PERSON>Status,
  <PERSON>ickIcon,
  InputContainer as ChatContainerInputContainer,
  MessageInput,
  SendButton,
  ScrollToBottomButton,
  ChatPreview,
  AvatarContainer,
  GroupAvatarContainer,
} from "./chat-container.styles";

// Auth pages styles - only export what's needed
export {
  LoginContainer,
  LoginCard,
  LogoSection as AuthLogoSection,
  LogoIcon as AuthLogoIcon,
  FormSection,
  InputGroup,
  Label,
  RegisterContainer,
  RegisterCard as AuthRegisterCard,
  RegisterFormSection,
  FormRow,
  GoogleButton as AuthGoogleButton,
  AuthButton,
  DividerText,
  LoginLink,
  JoinContainer,
  JoinCard,
  JoinHeader,
  JoinIcon,
  JoinContent,
  JoinButton,
  BackButton,
  VerifyContainer,
  VerifyCard,
  VerifyIcon,
  VerifyContent,
  VerifyButton,
} from "./auth-pages.styles";

export {
  SidebarContainer as ChatSidebarContainer,
  SearchContainer as ChatSidebarSearchContainer,
  ChatListContainer as ChatSidebarChatListContainer,
  ChatItem as ChatSidebarChatItem,
  ButtonStyled as ChatSidebarButtonStyled,
} from "./chat-sidebar.styles";

export {
  TypingContainer,
  TypingBubble,
  DotsContainer,
  Dot,
  TypingText,
} from "./typing-indicator.styles";

export {
  StatusDot,
  StatusContainer,
  StatusText,
} from "./online-status-indicator.styles";

export {
  StatusContainer as MessageStatusContainer,
  StatusIcon,
} from "./message-status-indicator.styles";

export {
  SearchContainer as MessageSearchContainer,
  SearchInput as MessageSearchInput,
  SearchStats as MessageSearchStats,
} from "./message-search.styles";
