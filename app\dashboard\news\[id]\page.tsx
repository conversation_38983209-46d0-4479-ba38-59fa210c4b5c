'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Card,
  Button,
  Space,
  Spin,
  Tag,
  Typography,
  Row,
  Col,
  Image,
  Divider,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  CalendarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useNewsService } from '@/hooks/useNewsService';
import { PostWithSeo } from '@/types/news.types';
import dayjs from 'dayjs';

const { Title, Paragraph, Text } = Typography;

export default function NewsDetailPage() {
  const router = useRouter();
  const params = useParams();
  const newsService = useNewsService();
  const [loading, setLoading] = useState(true);
  const [post, setPost] = useState<PostWithSeo | null>(null);

  const postId = params.id as string;

  useEffect(() => {
    if (postId) {
      fetchPost();
    }
  }, [postId]);

  const fetchPost = async () => {
    try {
      setLoading(true);
      const postData = await newsService.fetchPost(postId);
      if (postData) {
        setPost(postData);
      } else {
        router.push('/dashboard/news');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      router.push('/dashboard/news');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>Đang tải bài viết...</div>
      </div>
    );
  }

  if (!post) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <div>Không tìm thấy bài viết</div>
      </div>
    );
  }

  const isPublished = post.published_at !== null;

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Quay lại
          </Button>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => router.push(`/dashboard/news/edit/${post.id}`)}
          >
            Chỉnh sửa
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          <Card>
            <div style={{ marginBottom: '16px' }}>
              <Tag color={isPublished ? 'green' : 'orange'}>
                {isPublished ? 'Đã xuất bản' : 'Bản nháp'}
              </Tag>
            </div>

            <Title level={2} style={{ marginBottom: '16px' }}>
              {post.title}
            </Title>

            {post.thumbnail && (
              <div style={{ marginBottom: '24px', textAlign: 'center' }}>
                <Image
                  src={post.thumbnail}
                  alt={post.title}
                  style={{ maxWidth: '100%', maxHeight: '400px' }}
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
              </div>
            )}

            {post.description && (
              <div style={{ marginBottom: '16px' }}>
                <Text strong>Mô tả:</Text>
                <Paragraph style={{ fontSize: '14px', color: '#666', marginTop: '8px' }}>
                  {post.description}
                </Paragraph>
                <Divider />
              </div>
            )}

            <div 
              className="news-content"
              style={{ 
                fontSize: '16px', 
                lineHeight: '1.8',
                color: '#333'
              }}
              dangerouslySetInnerHTML={{ __html: post.content }}
            />

            <style jsx>{`
              .news-content :global(h1) {
                font-size: 28px;
                font-weight: bold;
                margin: 24px 0 16px 0;
                color: #262626;
              }
              .news-content :global(h2) {
                font-size: 24px;
                font-weight: bold;
                margin: 20px 0 12px 0;
                color: #262626;
              }
              .news-content :global(h3) {
                font-size: 20px;
                font-weight: bold;
                margin: 16px 0 8px 0;
                color: #262626;
              }
              .news-content :global(h4),
              .news-content :global(h5),
              .news-content :global(h6) {
                font-size: 16px;
                font-weight: bold;
                margin: 12px 0 8px 0;
                color: #262626;
              }
              .news-content :global(p) {
                margin: 12px 0;
                line-height: 1.8;
              }
              .news-content :global(ul),
              .news-content :global(ol) {
                margin: 12px 0;
                padding-left: 24px;
              }
              .news-content :global(li) {
                margin: 4px 0;
                line-height: 1.6;
              }
              .news-content :global(blockquote) {
                margin: 16px 0;
                padding: 12px 16px;
                border-left: 4px solid #1890ff;
                background-color: #f6f8fa;
                font-style: italic;
              }
              .news-content :global(a) {
                color: #1890ff;
                text-decoration: none;
              }
              .news-content :global(a:hover) {
                text-decoration: underline;
              }
              .news-content :global(img) {
                max-width: 100%;
                height: auto;
                margin: 16px 0;
                border-radius: 6px;
              }
              .news-content :global(code) {
                background-color: #f5f5f5;
                padding: 2px 6px;
                border-radius: 3px;
                font-family: 'Monaco', 'Consolas', monospace;
                font-size: 14px;
              }
              .news-content :global(pre) {
                background-color: #f5f5f5;
                padding: 12px;
                border-radius: 6px;
                overflow-x: auto;
                margin: 16px 0;
              }
              .news-content :global(pre code) {
                background: none;
                padding: 0;
              }
              .news-content :global(table) {
                width: 100%;
                border-collapse: collapse;
                margin: 16px 0;
              }
              .news-content :global(th),
              .news-content :global(td) {
                border: 1px solid #d9d9d9;
                padding: 8px 12px;
                text-align: left;
              }
              .news-content :global(th) {
                background-color: #fafafa;
                font-weight: bold;
              }
              .news-content :global(strong) {
                font-weight: bold;
              }
              .news-content :global(em) {
                font-style: italic;
              }
            `}</style>
          </Card>
        </Col>

        <Col span={8}>
          <Card title="Thông tin bài viết" style={{ marginBottom: '24px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <UserOutlined style={{ marginRight: '8px' }} />
                <Text strong>Tác giả:</Text>
                <div style={{ marginLeft: '24px' }}>{post.author_id}</div>
              </div>

              <div>
                <CalendarOutlined style={{ marginRight: '8px' }} />
                <Text strong>Ngày tạo:</Text>
                <div style={{ marginLeft: '24px' }}>
                  {dayjs(post.created_at).format('DD/MM/YYYY HH:mm')}
                </div>
              </div>

              <div>
                <CalendarOutlined style={{ marginRight: '8px' }} />
                <Text strong>Ngày cập nhật:</Text>
                <div style={{ marginLeft: '24px' }}>
                  {dayjs(post.updated_at).format('DD/MM/YYYY HH:mm')}
                </div>
              </div>

              {post.published_at && (
                <div>
                  <CalendarOutlined style={{ marginRight: '8px' }} />
                  <Text strong>Ngày xuất bản:</Text>
                  <div style={{ marginLeft: '24px' }}>
                    {dayjs(post.published_at).format('DD/MM/YYYY HH:mm')}
                  </div>
                </div>
              )}
            </Space>
          </Card>

          {(post.slug || post.meta_title || post.meta_description || post.meta_keywords) && (
            <Card title="Thông tin SEO">
              <Space direction="vertical" style={{ width: '100%' }}>
                {post.slug && (
                  <div>
                    <Text strong>Slug:</Text>
                    <div style={{ marginTop: '4px' }}>
                      <Text code>{post.slug}</Text>
                    </div>
                  </div>
                )}

                {post.meta_title && (
                  <div>
                    <Text strong>Meta Title:</Text>
                    <div style={{ marginTop: '4px' }}>{post.meta_title}</div>
                  </div>
                )}

                {post.meta_description && (
                  <div>
                    <Text strong>Meta Description:</Text>
                    <div style={{ marginTop: '4px' }}>{post.meta_description}</div>
                  </div>
                )}

                {post.meta_keywords && (
                  <div>
                    <Text strong>Meta Keywords:</Text>
                    <div style={{ marginTop: '4px' }}>{post.meta_keywords}</div>
                  </div>
                )}
              </Space>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
}