"use client";

import { useState, useEffect } from 'react';
import { profileService } from '@/services/profile.service';
import type { Profile } from '@/types/profile.types';
import { createClient } from '@/lib/supabase/client';

export function useProfile() {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const supabase = createClient();

  // Load current user profile
  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        throw new Error(authError.message);
      }

      if (!user) {
        throw new Error('Người dùng chưa đăng nhập');
      }

      // Get profile data
      const { profile: profileData, error: profileError } = await profileService.getProfile(user.id);
      
      if (profileError) {
        throw profileError;
      }

      setProfile(profileData);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      console.error('Load profile error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Update profile data
  const updateProfile = (updatedProfile: Profile) => {
    setProfile(updatedProfile);
  };

  // Reload profile
  const reloadProfile = () => {
    loadProfile();
  };

  useEffect(() => {
    loadProfile();
  }, []);

  return {
    profile,
    loading,
    error,
    updateProfile,
    reloadProfile,
  };
}
