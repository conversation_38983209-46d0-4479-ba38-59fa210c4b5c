"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Typography,
  Select,
  Input,
  Space,
  Tag,
  Avatar,
  Button,
  Empty,
  Spin,
  Badge,
  Divider,
  Pagination,
} from "antd";
import {
  PushpinOutlined,
  EyeOutlined,
  CommentOutlined,
  LikeOutlined,
  ClockCircleOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useForumService } from "@/hooks/useForumService";
import type {
  ForumPost,
  ForumCategory,
  ForumPostFilters,
} from "@/types/forum.types";
import dayjs from "dayjs";

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;

export default function ForumPage() {
  const router = useRouter();
  const {
    posts,
    categories,
    loading,
    error,
    fetchPosts,
    fetchCategories,
    togglePostLike,
  } = useForumService();

  const [filters, setFilters] = useState<ForumPostFilters>({
    status: "approved",
    sort_by: "is_pinned",
    sort_order: "desc", // Pinned posts first
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    fetchCategories();
    loadPosts();
  }, [filters, pagination.current]);

  const loadPosts = async () => {
    const response = await fetchPosts(filters, {
      offset: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize,
    });

    if (response) {
      setPagination(prev => ({ ...prev, total: response.count }));
    }
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleCategoryFilter = (categoryId: string | undefined) => {
    setFilters(prev => ({ ...prev, category_id: categoryId }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleSortChange = (sortValue: string) => {
    const [sort_by, sort_order] = sortValue.split("_");
    setFilters(prev => ({
      ...prev,
      sort_by: sort_by as any,
      sort_order: sort_order as any,
    }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleLike = async (postId: string) => {
    // This would need current user ID in a real implementation
    await togglePostLike(postId, "current-user-id");
    loadPosts();
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }));
  };

  const getPostCard = (post: ForumPost) => {
    const isLongContent = post.content.length > 200;
    const displayContent = isLongContent
      ? `${post.content.substring(0, 200)}...`
      : post.content;

    return (
      <Card
        key={post.id}
        hoverable
        style={{
          marginBottom: "16px",
          border: post.is_pinned ? "2px solid #1890ff" : undefined,
        }}
        bodyStyle={{ padding: "16px" }}
        onClick={() => router.push(`/forum/${post.slug}`)}
      >
        <div style={{ display: "flex", gap: "12px" }}>
          {/* Author Avatar */}
          <Avatar src={post.author?.avatar_url} size="large">
            {post.author?.full_name?.[0]?.toUpperCase() || "U"}
          </Avatar>

          <div style={{ flex: 1 }}>
            {/* Header */}
            <div style={{ marginBottom: "8px" }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Space>
                    <Text strong style={{ fontSize: "16px" }}>
                      {post.title}
                    </Text>
                    {post.is_pinned && (
                      <Badge
                        count="Ghim"
                        style={{ backgroundColor: "#1890ff" }}
                      />
                    )}
                    {post.is_featured && (
                      <Badge
                        count="Nổi bật"
                        style={{ backgroundColor: "#faad14" }}
                      />
                    )}
                  </Space>
                </Col>
                <Col>
                  <Text type="secondary" style={{ fontSize: "12px" }}>
                    {dayjs(post.created_at).format("DD/MM/YYYY")}
                  </Text>
                </Col>
              </Row>
            </div>

            {/* Category */}
            {post.category && (
              <div style={{ marginBottom: "8px" }}>
                <Tag color={post.category.color}>
                  {post.category.icon && (
                    <span style={{ marginRight: 4 }}>{post.category.icon}</span>
                  )}
                  {post.category.name}
                </Tag>
              </div>
            )}

            {/* Content */}
            <Paragraph
              ellipsis={{
                rows: 3,
                tooltip: isLongContent ? post.content : false,
              }}
              style={{
                marginBottom: "12px",
                fontSize: "14px",
                lineHeight: 1.6,
              }}
            >
              {displayContent}
            </Paragraph>

            {/* Tags */}
            {post.tags && post.tags.length > 0 && (
              <div style={{ marginBottom: "12px" }}>
                <Space size={[0, 4]} wrap>
                  {post.tags.slice(0, 3).map(tag => (
                    <Tag key={tag}>#{tag}</Tag>
                  ))}
                  {post.tags.length > 3 && (
                    <Text type="secondary">+{post.tags.length - 3} tags</Text>
                  )}
                </Space>
              </div>
            )}

            {/* Stats and Actions */}
            <Row justify="space-between" align="middle">
              <Col>
                <Space size="large">
                  <Space size="small">
                    <EyeOutlined style={{ color: "#666" }} />
                    <Text type="secondary">{post.view_count}</Text>
                  </Space>
                  <Space size="small">
                    <CommentOutlined style={{ color: "#666" }} />
                    <Text type="secondary">{post.comment_count}</Text>
                  </Space>
                  <Space size="small">
                    <LikeOutlined style={{ color: "#666" }} />
                    <Text type="secondary">{post.like_count}</Text>
                  </Space>
                </Space>
              </Col>
              <Col>
                <Button
                  type="text"
                  size="small"
                  onClick={e => {
                    e.stopPropagation();
                    handleLike(post.id);
                  }}
                >
                  Thích
                </Button>
              </Col>
            </Row>
          </div>
        </div>
      </Card>
    );
  };

  const pinnedPosts = posts.filter(post => post.is_pinned);
  const regularPosts = posts.filter(post => !post.is_pinned);

  return (
    <div style={{ padding: "24px", maxWidth: "1200px", margin: "0 auto" }}>
      {/* Header */}
      <div style={{ marginBottom: "32px" }}>
        <Title level={2} style={{ marginBottom: "8px" }}>
          Diễn đàn Cộng đồng
        </Title>
        <Text type="secondary">
          Chia sẻ kinh nghiệm, thảo luận về bóng đá và cá cược
        </Text>
      </div>

      {/* Filters */}
      <Card style={{ marginBottom: "24px" }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="Tìm kiếm bài viết..."
              allowClear
              onSearch={handleSearch}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              placeholder="Chọn danh mục"
              allowClear
              style={{ width: "100%" }}
              onChange={handleCategoryFilter}
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>
                  <Space>
                    <div
                      style={{
                        width: 12,
                        height: 12,
                        backgroundColor: category.color || "#1890ff",
                        borderRadius: "50%",
                      }}
                    />
                    {category.name}
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Select
              placeholder="Sắp xếp theo"
              style={{ width: "100%" }}
              onChange={handleSortChange}
              defaultValue="is_pinned_desc"
            >
              <Option value="is_pinned_desc">Bài viết ghim trước</Option>
              <Option value="created_at_desc">Mới nhất trước</Option>
              <Option value="created_at_asc">Cũ nhất trước</Option>
              <Option value="view_count_desc">Xem nhiều nhất</Option>
              <Option value="like_count_desc">Thích nhiều nhất</Option>
              <Option value="comment_count_desc">Bình luận nhiều nhất</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Button
              type="primary"
              icon={<FilterOutlined />}
              onClick={() => router.push("/forum/create")}
              block
            >
              Tạo bài viết
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Pinned Posts Section */}
      {pinnedPosts.length > 0 && (
        <div style={{ marginBottom: "32px" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: "16px",
            }}
          >
            <PushpinOutlined style={{ color: "#1890ff", marginRight: 8 }} />
            <Title level={4} style={{ margin: 0 }}>
              Bài viết đã ghim
            </Title>
          </div>
          <Row gutter={[16, 16]}>
            {pinnedPosts.map(post => (
              <Col xs={24} md={12} key={post.id}>
                {getPostCard(post)}
              </Col>
            ))}
          </Row>
          <Divider style={{ margin: "24px 0" }} />
        </div>
      )}

      {/* Regular Posts */}
      <div>
        <Title level={4} style={{ marginBottom: "16px" }}>
          Tất cả bài viết
        </Title>

        {loading ? (
          <div style={{ textAlign: "center", padding: "40px" }}>
            <Spin size="large" />
            <div style={{ marginTop: "16px" }}>
              <Text type="secondary">Đang tải bài viết...</Text>
            </div>
          </div>
        ) : regularPosts.length > 0 ? (
          <div>{regularPosts.map(post => getPostCard(post))}</div>
        ) : (
          <Empty
            description={
              filters.search || filters.category_id
                ? "Không tìm thấy bài viết nào phù hợp với bộ lọc"
                : "Chưa có bài viết nào. Hãy là người đầu tiên tạo bài viết!"
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            {!filters.search && !filters.category_id && (
              <Button
                type="primary"
                onClick={() => router.push("/forum/create")}
              >
                Tạo bài viết đầu tiên
              </Button>
            )}
          </Empty>
        )}
      </div>

      {/* Pagination */}
      {pagination.total > pagination.pageSize && (
        <div style={{ textAlign: "center", marginTop: "32px" }}>
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) =>
              `${range[0]}-${range[1]} của ${total} bài viết`
            }
          />
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div style={{ marginTop: "16px", color: "red", textAlign: "center" }}>
          Lỗi: {error}
        </div>
      )}
    </div>
  );
}
