"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSupabaseClient } from '@/contexts/SupabaseContext';
import { useTabVisibility } from './useTabVisibility';

export interface ChatMessage {
  id: string;
  content: string;
  user: {
    name: string;
    avatar_url?: string;
  };
  createdAt: string;
  user_id: string;
}

interface UseRealtimeChatProps {
  roomName: string;
  username: string;
  userId: string;
  messages?: ChatMessage[];
  onMessage?: (messages: ChatMessage[]) => void;
  onReconnect?: () => void;
}

export function useRealtimeChat({
  roomName,
  username,
  userId,
  messages: initialMessages = [],
  onMessage,
  onReconnect
}: UseRealtimeChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true for initial load
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const supabase = useSupabaseClient();
  const channelRef = useRef<any>(null);
  const messagesRef = useRef<ChatMessage[]>(messages);
  const { isVisible, isOnline } = useTabVisibility();
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update ref when messages change
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  // Set loading false if we have initial messages or no room
  useEffect(() => {
    if (initialMessages.length > 0 || !roomName) {
      setIsLoading(false);
    }
  }, [initialMessages.length, roomName]);

  // Load initial messages from database
  const loadMessages = useCallback(async () => {
    if (!roomName) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          id,
          content,
          created_at,
          sender_id,
          profiles:sender_id (
            full_name,
            avatar_url
          )
        `)
        .eq('room_id', roomName)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const formattedMessages: ChatMessage[] = (data || []).map((msg: any) => ({
        id: msg.id,
        content: msg.content,
        createdAt: msg.created_at,
        user_id: msg.sender_id,
        user: {
          name: msg.profiles?.full_name || 'Unknown User',
          avatar_url: msg.profiles?.avatar_url
        }
      }));

      setMessages(formattedMessages);
      if (onMessage) {
        onMessage(formattedMessages);
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Error loading messages:', err);
    } finally {
      setIsLoading(false);
    }
  }, [roomName, onMessage]); // Remove supabase from dependencies

  // Send a new message
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || !roomName || !userId) return;

    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          content: content.trim(),
          room_id: roomName,
          sender_id: userId
        });

      if (error) throw error;
    } catch (err: any) {
      setError(err.message);
      console.error('Error sending message:', err);
    }
  }, [roomName, userId]); // Remove supabase from dependencies

  // Subscribe to real-time messages
  const subscribeToMessages = useCallback(() => {
    if (!roomName || !isOnline) return;

    // Clean up existing subscription
    if (channelRef.current) {
      channelRef.current.unsubscribe();
    }

    const channel = supabase
      .channel(`messages:${roomName}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `room_id=eq.${roomName}`
        },
        async (payload) => {
          const newMessage = payload.new;
          
          // Fetch user profile for the new message
          const { data: profileData } = await supabase
            .from('profiles')
            .select('full_name, avatar_url')
            .eq('id', newMessage.sender_id)
            .single();

          const formattedMessage: ChatMessage = {
            id: newMessage.id,
            content: newMessage.content,
            createdAt: newMessage.created_at,
            user_id: newMessage.sender_id,
            user: {
              name: profileData?.full_name || 'Unknown User',
              avatar_url: profileData?.avatar_url
            }
          };

          setMessages(prev => {
            const updated = [...prev, formattedMessage];
            if (onMessage) {
              onMessage(updated);
            }
            return updated;
          });
        }
      )
      .subscribe((status) => {
        console.log(`Realtime subscription status for ${roomName}:`, status);
        
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
          setError(null);
        } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
          setIsConnected(false);
          setError('Connection lost. Trying to reconnect...');
          
          // Auto-reconnect after a delay (only reconnect, don't reload messages)
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }
          reconnectTimeoutRef.current = setTimeout(() => {
            if (isVisible && isOnline) {
              // Only reconnect subscription, don't reload messages
              subscribeToMessages();
            }
          }, 3000);
        }
      });

    channelRef.current = channel;
  }, [roomName, onMessage]); // Remove isOnline, isVisible to prevent constant reconnections

  // Initial subscription and when dependencies change
  useEffect(() => {
    if (!roomName) return;

    subscribeToMessages();
    loadMessages();

    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [roomName]); // Remove subscribeToMessages and loadMessages from dependencies

  // Handle tab visibility and online status changes
  useEffect(() => {
    // Only reconnect if we were previously connected but lost connection
    // and now tab is visible and online
    if (isVisible && isOnline && !isConnected && roomName && channelRef.current) {
      console.log('Tab became visible and online, reconnecting...');
      // Call onReconnect to reload room data
      if (onReconnect) {
        onReconnect();
      }
    }
  }, [isVisible, isOnline, isConnected, roomName, onReconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
      }
    };
  }, []);

  return {
    messages,
    sendMessage,
    isLoading,
    isConnected,
    error,
    loadMessages
  };
}