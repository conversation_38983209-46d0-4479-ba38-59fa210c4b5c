// ========================================
// FORUM SERVICE - Database Operations
// ========================================

import { createClient } from "@/lib/supabase/client";
import type {
  ForumCategory,
  ForumPost,
  ForumComment,
  CreateForumPostDto,
  UpdateForumPostDto,
  CreateForumCommentDto,
  UpdateForumCommentDto,
  ForumPostFilters,
  ForumCommentFilters,
  PaginationOptions,
  ForumPostsResponse,
  ForumCommentsResponse,
  ForumStats,
  PostStatus,
  CommentStatus,
} from "../types/forum.types";

class ForumService {
  private supabase;

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }

  // Static method to create instance with default client for backward compatibility
  static createWithDefaultClient() {
    return new ForumService(createClient());
  }

  // ========================================
  // CATEGORY OPERATIONS
  // ========================================

  /**
   * Get all active forum categories
   */
  async getCategories(): Promise<ForumCategory[]> {
    try {
      const { data, error } = await this.supabase
        .from("forum_categories")
        .select("*")
        .eq("is_active", true)
        .order("sort_order", { ascending: true });

      if (error) {
        console.error("Error fetching categories:", error);
        return [];
      }

      return data as ForumCategory[];
    } catch (error) {
      console.error("Error in getCategories:", error);
      return [];
    }
  }

  /**
   * Get category by ID
   */
  async getCategory(id: string): Promise<ForumCategory | null> {
    try {
      const { data, error } = await this.supabase
        .from("forum_categories")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        console.error("Error fetching category:", error);
        return null;
      }

      return data as ForumCategory;
    } catch (error) {
      console.error("Error in getCategory:", error);
      return null;
    }
  }

  // ========================================
  // POST OPERATIONS
  // ========================================

  /**
   * Create a new forum post
   */
  async createPost(postData: CreateForumPostDto): Promise<ForumPost | null> {
    try {
      const { data, error } = await this.supabase
        .from("forum_posts")
        .insert([postData])
        .select(
          `
          *,
          category:forum_categories(*),
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .single();

      if (error) {
        console.error("Error creating post:", error);
        return null;
      }

      return data as ForumPost;
    } catch (error) {
      console.error("Error in createPost:", error);
      return null;
    }
  }

  /**
   * Update a forum post
   */
  async updatePost(postData: UpdateForumPostDto): Promise<ForumPost | null> {
    try {
      const { data, error } = await this.supabase
        .from("forum_posts")
        .update({ ...postData, updated_at: new Date().toISOString() })
        .eq("id", postData.id)
        .select(
          `
          *,
          category:forum_categories(*),
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .single();

      if (error) {
        console.error("Error updating post:", error);
        return null;
      }

      return data as ForumPost;
    } catch (error) {
      console.error("Error in updatePost:", error);
      return null;
    }
  }

  /**
   * Delete a forum post
   */
  async deletePost(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from("forum_posts")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting post:", error);
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error in deletePost:", error);
      return false;
    }
  }

  /**
   * Get forum posts with filters and pagination
   */
  async getPosts(
    filters: ForumPostFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<ForumPostsResponse> {
    try {
      let query = this.supabase.from("forum_posts").select(
        `
          *,
          category:forum_categories(*),
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `,
        { count: "exact" }
      );

      // Apply filters
      if (filters.category_id) {
        query = query.eq("category_id", filters.category_id);
      }

      if (filters.status) {
        query = query.eq("status", filters.status);
      }

      if (filters.author_id) {
        query = query.eq("author_id", filters.author_id);
      }

      if (filters.is_pinned !== undefined) {
        query = query.eq("is_pinned", filters.is_pinned);
      }

      if (filters.is_featured !== undefined) {
        query = query.eq("is_featured", filters.is_featured);
      }

      if (filters.search) {
        query = query.or(
          `title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`
        );
      }

      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps("tags", filters.tags);
      }

      // Apply sorting
      const sortBy = filters.sort_by || "created_at";
      const sortOrder = filters.sort_order || "desc";

      // Special sorting for pinned posts - show pinned posts first
      if (sortBy === "created_at" && sortOrder === "desc") {
        query = query
          .order("is_pinned", { ascending: false })
          .order("created_at", { ascending: false });
      } else {
        query = query.order(sortBy, { ascending: sortOrder === "asc" });
      }

      // Apply pagination
      if (pagination.limit) {
        query = query.limit(pagination.limit);
      }
      if (pagination.offset) {
        query = query.range(
          pagination.offset,
          pagination.offset + (pagination.limit || 10) - 1
        );
      }

      const { data, error, count } = await query;

      if (error) {
        console.error("Error fetching posts:", error);
        return { data: [], count: 0, has_more: false };
      }

      const hasMore = pagination.limit
        ? (count || 0) > (pagination.offset || 0) + (pagination.limit || 0)
        : false;

      return {
        data: data as ForumPost[],
        count: count || 0,
        has_more: hasMore,
      };
    } catch (error) {
      console.error("Error in getPosts:", error);
      return { data: [], count: 0, has_more: false };
    }
  }

  /**
   * Get a single post by ID
   */
  async getPost(id: string): Promise<ForumPost | null> {
    try {
      const { data, error } = await this.supabase
        .from("forum_posts")
        .select(
          `
          *,
          category:forum_categories(*),
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        console.error("Error fetching post:", error);
        return null;
      }

      // Increment view count
      await this.incrementViewCount(id);

      return data as ForumPost;
    } catch (error) {
      console.error("Error in getPost:", error);
      return null;
    }
  }

  /**
   * Get a post by slug
   */
  async getPostBySlug(slug: string): Promise<ForumPost | null> {
    try {
      const { data, error } = await this.supabase
        .from("forum_posts")
        .select(
          `
          *,
          category:forum_categories(*),
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .eq("slug", slug)
        .single();

      if (error) {
        console.error("Error fetching post by slug:", error);
        return null;
      }

      // Increment view count
      await this.incrementViewCount(data.id);

      return data as ForumPost;
    } catch (error) {
      console.error("Error in getPostBySlug:", error);
      return null;
    }
  }

  /**
   * Increment post view count
   */
  async incrementViewCount(postId: string): Promise<void> {
    try {
      await this.supabase
        .from("forum_posts")
        .update({ view_count: this.supabase.raw("view_count + 1") })
        .eq("id", postId);
    } catch (error) {
      console.error("Error incrementing view count:", error);
    }
  }

  /**
   * Pin/Unpin a post
   */
  async togglePinPost(
    postId: string,
    isPinned: boolean
  ): Promise<ForumPost | null> {
    return this.updatePost({ id: postId, is_pinned: isPinned });
  }

  /**
   * Approve a post
   */
  async approvePost(postId: string): Promise<ForumPost | null> {
    return this.updatePost({
      id: postId,
      status: "approved" as PostStatus,
      published_at: new Date().toISOString(),
    });
  }

  /**
   * Reject a post
   */
  async rejectPost(postId: string): Promise<ForumPost | null> {
    return this.updatePost({ id: postId, status: "rejected" as PostStatus });
  }

  // ========================================
  // COMMENT OPERATIONS
  // ========================================

  /**
   * Create a new comment
   */
  async createComment(
    commentData: CreateForumCommentDto
  ): Promise<ForumComment | null> {
    try {
      // Validate parent comment level if provided
      if (commentData.parent_id) {
        const parentComment = await this.getComment(commentData.parent_id);
        if (parentComment && parentComment.level >= 3) {
          throw new Error("Cannot reply to a level 3 comment");
        }
      }

      const commentLevel = commentData.parent_id ? 2 : 1;

      const { data, error } = await this.supabase
        .from("forum_comments")
        .insert([
          {
            ...commentData,
            level: commentLevel,
          },
        ])
        .select(
          `
          *,
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .single();

      if (error) {
        console.error("Error creating comment:", error);
        return null;
      }

      return data as ForumComment;
    } catch (error) {
      console.error("Error in createComment:", error);
      return null;
    }
  }

  /**
   * Update a comment
   */
  async updateComment(
    commentData: UpdateForumCommentDto
  ): Promise<ForumComment | null> {
    try {
      const { data, error } = await this.supabase
        .from("forum_comments")
        .update({
          ...commentData,
          is_edited: true,
          edited_at: new Date().toISOString(),
        })
        .eq("id", commentData.id)
        .select(
          `
          *,
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .single();

      if (error) {
        console.error("Error updating comment:", error);
        return null;
      }

      return data as ForumComment;
    } catch (error) {
      console.error("Error in updateComment:", error);
      return null;
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from("forum_comments")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting comment:", error);
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error in deleteComment:", error);
      return false;
    }
  }

  /**
   * Get comments for a post with nested structure
   */
  async getComments(
    filters: ForumCommentFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<ForumCommentsResponse> {
    try {
      let query = this.supabase.from("forum_comments").select(
        `
          *,
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `,
        { count: "exact" }
      );

      // Apply filters
      if (filters.post_id) {
        query = query.eq("post_id", filters.post_id);
      }

      if (filters.parent_id !== undefined) {
        if (filters.parent_id) {
          query = query.eq("parent_id", filters.parent_id);
        } else {
          query = query.is("parent_id", null);
        }
      }

      if (filters.level) {
        query = query.eq("level", filters.level);
      }

      if (filters.status) {
        query = query.eq("status", filters.status);
      }

      if (filters.author_id) {
        query = query.eq("author_id", filters.author_id);
      }

      // Apply sorting
      query = query.order("created_at", { ascending: true });

      // Apply pagination
      if (pagination.limit) {
        query = query.limit(pagination.limit);
      }
      if (pagination.offset) {
        query = query.range(
          pagination.offset,
          pagination.offset + (pagination.limit || 10) - 1
        );
      }

      const { data, error, count } = await query;

      if (error) {
        console.error("Error fetching comments:", error);
        return { data: [], count: 0, has_more: false };
      }

      const hasMore = pagination.limit
        ? (count || 0) > (pagination.offset || 0) + (pagination.limit || 0)
        : false;

      return {
        data: data as ForumComment[],
        count: count || 0,
        has_more: hasMore,
      };
    } catch (error) {
      console.error("Error in getComments:", error);
      return { data: [], count: 0, has_more: false };
    }
  }

  /**
   * Get a single comment by ID
   */
  async getComment(id: string): Promise<ForumComment | null> {
    try {
      const { data, error } = await this.supabase
        .from("forum_comments")
        .select(
          `
          *,
          author:profiles(
            id,
            full_name,
            avatar_url
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        console.error("Error fetching comment:", error);
        return null;
      }

      return data as ForumComment;
    } catch (error) {
      console.error("Error in getComment:", error);
      return null;
    }
  }

  /**
   * Approve a comment
   */
  async approveComment(commentId: string): Promise<ForumComment | null> {
    return this.updateComment({ id: commentId, content: "" }); // This will be handled by a proper update
  }

  // ========================================
  // LIKE OPERATIONS
  // ========================================

  /**
   * Like/Unlike a post
   */
  async togglePostLike(postId: string, userId: string): Promise<boolean> {
    try {
      // Check if like already exists
      const { data: existingLike } = await this.supabase
        .from("forum_post_likes")
        .select("id")
        .eq("post_id", postId)
        .eq("user_id", userId)
        .single();

      if (existingLike) {
        // Unlike the post
        const { error } = await this.supabase
          .from("forum_post_likes")
          .delete()
          .eq("id", existingLike.id);

        if (error) {
          console.error("Error unliking post:", error);
          return false;
        }

        // Decrement like count
        await this.supabase
          .from("forum_posts")
          .update({ like_count: this.supabase.raw("like_count - 1") })
          .eq("id", postId);

        return false; // Unliked
      } else {
        // Like the post
        const { error } = await this.supabase
          .from("forum_post_likes")
          .insert([{ post_id: postId, user_id: userId }]);

        if (error) {
          console.error("Error liking post:", error);
          return false;
        }

        // Increment like count
        await this.supabase
          .from("forum_posts")
          .update({ like_count: this.supabase.raw("like_count + 1") })
          .eq("id", postId);

        return true; // Liked
      }
    } catch (error) {
      console.error("Error in togglePostLike:", error);
      return false;
    }
  }

  /**
   * Like/Unlike a comment
   */
  async toggleCommentLike(commentId: string, userId: string): Promise<boolean> {
    try {
      // Check if like already exists
      const { data: existingLike } = await this.supabase
        .from("forum_comment_likes")
        .select("id")
        .eq("comment_id", commentId)
        .eq("user_id", userId)
        .single();

      if (existingLike) {
        // Unlike the comment
        const { error } = await this.supabase
          .from("forum_comment_likes")
          .delete()
          .eq("id", existingLike.id);

        if (error) {
          console.error("Error unliking comment:", error);
          return false;
        }

        // Decrement like count
        await this.supabase
          .from("forum_comments")
          .update({ like_count: this.supabase.raw("like_count - 1") })
          .eq("id", commentId);

        return false; // Unliked
      } else {
        // Like the comment
        const { error } = await this.supabase
          .from("forum_comment_likes")
          .insert([{ comment_id: commentId, user_id: userId }]);

        if (error) {
          console.error("Error liking comment:", error);
          return false;
        }

        // Increment like count
        await this.supabase
          .from("forum_comments")
          .update({ like_count: this.supabase.raw("like_count + 1") })
          .eq("id", commentId);

        return true; // Liked
      }
    } catch (error) {
      console.error("Error in toggleCommentLike:", error);
      return false;
    }
  }

  // ========================================
  // UTILITY FUNCTIONS
  // ========================================

  /**
   * Generate a URL-friendly slug from title
   */
  generateSlug(title: string): string {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, "") // Remove special characters
      .replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
  }

  /**
   * Check if a slug is available
   */
  async validateSlug(slug: string, excludeId?: string): Promise<boolean> {
    try {
      let query = this.supabase
        .from("forum_posts")
        .select("id")
        .eq("slug", slug);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error validating slug:", error);
        return false;
      }

      return data.length === 0;
    } catch (error) {
      console.error("Error in validateSlug:", error);
      return false;
    }
  }

  /**
   * Generate a unique slug from title
   */
  async generateUniqueSlug(title: string, excludeId?: string): Promise<string> {
    let baseSlug = this.generateSlug(title);
    let slug = baseSlug;
    let counter = 1;

    while (!(await this.validateSlug(slug, excludeId))) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Get forum statistics
   */
  async getForumStats(): Promise<ForumStats> {
    try {
      // Get total counts
      const [postsResult, commentsResult, likesResult] = await Promise.all([
        this.supabase
          .from("forum_posts")
          .select("id", { count: "exact", head: true }),
        this.supabase
          .from("forum_comments")
          .select("id", { count: "exact", head: true }),
        this.supabase
          .from("forum_post_likes")
          .select("id", { count: "exact", head: true }),
      ]);

      // Get posts and comments from today
      const today = new Date().toISOString().split("T")[0];
      const [
        postsTodayResult,
        commentsTodayResult,
        pendingPostsResult,
        pendingCommentsResult,
      ] = await Promise.all([
        this.supabase
          .from("forum_posts")
          .select("id", { count: "exact", head: true })
          .gte("created_at", today),
        this.supabase
          .from("forum_comments")
          .select("id", { count: "exact", head: true })
          .gte("created_at", today),
        this.supabase
          .from("forum_posts")
          .select("id", { count: "exact", head: true })
          .eq("status", "pending"),
        this.supabase
          .from("forum_comments")
          .select("id", { count: "exact", head: true })
          .eq("status", "pending"),
      ]);

      // Get popular categories
      const { data: categoryData } = await this.supabase
        .from("forum_posts")
        .select(
          `
          category_id,
          category:forum_categories(name)
        `
        )
        .not("category_id", "is", null);

      const categoryStats = categoryData?.reduce((acc: any, post: any) => {
        const categoryId = post.category_id;
        if (!acc[categoryId]) {
          acc[categoryId] = { category: post.category, post_count: 0 };
        }
        acc[categoryId].post_count++;
        return acc;
      }, {});

      const popularCategories = Object.values(categoryStats || {})
        .sort((a: any, b: any) => b.post_count - a.post_count)
        .slice(0, 5);

      return {
        total_posts: postsResult.count || 0,
        total_comments: commentsResult.count || 0,
        total_likes: likesResult.count || 0,
        posts_today: postsTodayResult.count || 0,
        comments_today: commentsTodayResult.count || 0,
        pending_posts: pendingPostsResult.count || 0,
        pending_comments: pendingCommentsResult.count || 0,
        popular_categories: popularCategories as Array<{
          category: ForumCategory;
          post_count: number;
        }>,
      };
    } catch (error) {
      console.error("Error in getForumStats:", error);
      return {
        total_posts: 0,
        total_comments: 0,
        total_likes: 0,
        posts_today: 0,
        comments_today: 0,
        pending_posts: 0,
        pending_comments: 0,
        popular_categories: [],
      };
    }
  }
}

// Export default instance
export const forumService = ForumService.createWithDefaultClient();
export default forumService;
export { ForumService };
