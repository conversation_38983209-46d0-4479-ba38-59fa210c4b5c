'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  message,
  Switch,
  Divider,
  Row,
  Col,
  Select,
} from 'antd';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import RichTextEditor from '@/components/RichTextEditor';
import { useNewsService } from '@/hooks/useNewsService';
import { CreatePostRequest } from '@/types/news.types';
import { Category } from '@/types/category.types';
import { useAuth } from '@/hooks/useAuth';
import { fetchCategories } from '@/lib/api/categories';
import { createClient } from '@/lib/supabase/client';

const { TextArea } = Input;

export default function CreateNewsPage() {
  const [form] = Form.useForm();
  const router = useRouter();
  const { user } = useAuth();
  const newsService = useNewsService();
  const [loading, setLoading] = useState(false);
  const [publishNow, setPublishNow] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [content, setContent] = useState('');
  const supabase = createClient();

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    loadCategories();
  }, []);

  const handleSubmit = async (values: any) => {
    if (!user?.id) {
      message.error('Không thể xác định người dùng');
      return;
    }

    // Validate content (markdown format)
    const contentText = content.replace(/[#*_`~\[\]()]/g, '').trim();
    if (!content || content.trim() === '' || contentText.length < 50) {
      message.error('Nội dung bài viết phải có ít nhất 50 ký tự!');
      return;
    }

    setLoading(true);
    try {
      // Tạo post request
      const postData: CreatePostRequest = {
        title: values.title,
        description: values.description || null,
        content: content, // Sử dụng content từ ReactQuill
        thumbnail: values.thumbnail || null,
        author_id: user.id,
        published_at: publishNow ? new Date().toISOString() : null,
        // SEO fields
        slug: values.slug || null,
        meta_title: values.meta_title || null,
        meta_description: values.meta_description || null,
        meta_keywords: values.meta_keywords || null,
      };

      // Tạo bài viết
      const newPost = await newsService.createPost(postData);
      if (!newPost) {
        throw new Error('Không thể tạo bài viết');
      }

      // Thêm danh mục cho bài viết nếu có
      if (values.categories && values.categories.length > 0) {
        // Tạo các bản ghi trong bảng post_categories
        const categoryPromises = values.categories.map((categoryId: string) => 
          supabase.from('post_categories').insert({
            post_id: newPost.id,
            category_id: categoryId
          })
        );
        await Promise.all(categoryPromises);
      }

      // SEO fields are now included in the post creation above
      console.log('SEO fields included in post creation');

      message.success('Tạo bài viết thành công!');
      router.push('/dashboard/news');
    } catch (error) {
      console.error('Error creating post:', error);
      message.error('Có lỗi xảy ra khi tạo bài viết');
    } finally {
      setLoading(false);
    }
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    const slug = generateSlug(title);
    form.setFieldsValue({ slug, meta_title: title });
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.back()}
          >
            Quay lại
          </Button>
          <h2 style={{ margin: 0 }}>Tạo bài viết mới</h2>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          publishNow: false,
        }}
      >
        <Row gutter={24}>
          <Col span={16}>
            <Card title="Nội dung bài viết" style={{ marginBottom: '24px' }}>
              <Form.Item
                name="title"
                label="Tiêu đề"
                rules={[
                  { required: true, message: 'Vui lòng nhập tiêu đề' },
                  { min: 5, message: 'Tiêu đề phải có ít nhất 5 ký tự' },
                ]}
              >
                <Input
                  placeholder="Nhập tiêu đề bài viết"
                  onChange={handleTitleChange}
                />
              </Form.Item>

              <Form.Item
                name="description"
                label="Mô tả ngắn"
                rules={[
                  { max: 500, message: 'Mô tả không được quá 500 ký tự' },
                ]}
              >
                <TextArea
                  rows={3}
                  placeholder="Nhập mô tả ngắn cho bài viết (tối đa 500 ký tự)"
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              <Form.Item
                name="thumbnail"
                label="Ảnh đại diện"
                rules={[
                  { type: 'url', message: 'Vui lòng nhập URL hợp lệ' },
                ]}
              >
                <Input
                  placeholder="Nhập URL ảnh đại diện (ví dụ: https://example.com/image.jpg)"
                />
              </Form.Item>

              <Form.Item
                name="categories"
                label="Danh mục"
              >
                <Select
                  mode="multiple"
                  placeholder="Chọn danh mục"
                  options={categories.map(cat => ({
                    label: cat.name,
                    value: cat.id
                  }))}
                />
              </Form.Item>

              <Form.Item
                label="Nội dung"
                required
              >
                <RichTextEditor
                  value={content}
                  onChange={setContent}
                  placeholder="Nhập nội dung bài viết..."
                  height="300px"
                />
              </Form.Item>
            </Card>
          </Col>

          <Col span={8}>
            <Card title="Cài đặt xuất bản" style={{ marginBottom: '24px' }}>
              <Form.Item
                name="publishNow"
                valuePropName="checked"
              >
                <Switch
                  checked={publishNow}
                  onChange={setPublishNow}
                  checkedChildren="Xuất bản ngay"
                  unCheckedChildren="Lưu nháp"
                />
              </Form.Item>
            </Card>

            <Card title="SEO Settings">
              <Form.Item
                name="slug"
                label="Slug (URL)"
                rules={[
                  { pattern: /^[a-z0-9-]+$/, message: 'Slug chỉ chứa chữ thường, số và dấu gạch ngang' },
                ]}
              >
                <Input placeholder="auto-generated-from-title" />
              </Form.Item>

              <Form.Item
                name="meta_title"
                label="Meta Title"
                rules={[
                  { max: 60, message: 'Meta title không được quá 60 ký tự' },
                ]}
              >
                <Input placeholder="Tiêu đề SEO" />
              </Form.Item>

              <Form.Item
                name="meta_description"
                label="Meta Description"
                rules={[
                  { max: 160, message: 'Meta description không được quá 160 ký tự' },
                ]}
              >
                <TextArea
                  rows={3}
                  placeholder="Mô tả ngắn cho SEO"
                />
              </Form.Item>

              <Form.Item
                name="meta_keywords"
                label="Meta Keywords"
              >
                <Input placeholder="keyword1, keyword2, keyword3" />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        <Divider />

        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={() => router.back()}>
              Hủy
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              {publishNow ? 'Tạo và xuất bản' : 'Lưu nháp'}
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
}