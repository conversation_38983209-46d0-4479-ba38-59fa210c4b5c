export type PostStatus = "pending" | "approved" | "rejected" | "draft";
export type CommentStatus = "pending" | "approved" | "rejected" | "spam";

export interface ForumCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ForumPost {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  category_id?: string;
  category?: ForumCategory;
  author_id: string;
  author?: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  };
  status: PostStatus;
  is_pinned: boolean;
  is_featured: boolean;
  view_count: number;
  like_count: number;
  comment_count: number;
  image_urls?: string[];
  video_urls?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ForumComment {
  id: string;
  content: string;
  post_id: string;
  author_id: string;
  author?: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  };
  parent_id?: string;
  parent?: ForumComment;
  level: number; // 1, 2, or 3
  status: CommentStatus;
  like_count: number;
  is_edited: boolean;
  edited_at?: string;
  created_at: string;
  updated_at: string;
  // Nested replies for frontend display
  replies?: ForumComment[];
}

export interface ForumPostLike {
  id: string;
  post_id: string;
  user_id: string;
  created_at: string;
}

export interface ForumCommentLike {
  id: string;
  comment_id: string;
  user_id: string;
  created_at: string;
}

// DTOs for creating/updating
export interface CreateForumPostDto {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  category_id?: string;
  author_id: string;
  image_urls?: string[];
  video_urls?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  status?: PostStatus;
}

export interface UpdateForumPostDto extends Partial<CreateForumPostDto> {
  id: string;
  is_pinned?: boolean;
  is_featured?: boolean;
  status?: PostStatus;
  published_at?: string;
}

export interface CreateForumCommentDto {
  content: string;
  post_id: string;
  parent_id?: string; // for nested comments
}

export interface UpdateForumCommentDto {
  id: string;
  content: string;
  status?: CommentStatus;
}

// API Response types
export interface ForumPostsResponse {
  data: ForumPost[];
  count: number;
  has_more: boolean;
}

export interface ForumCommentsResponse {
  data: ForumComment[];
  count: number;
  has_more: boolean;
}

// Filter and pagination types
export interface ForumPostFilters {
  category_id?: string;
  status?: PostStatus;
  author_id?: string;
  is_pinned?: boolean;
  is_featured?: boolean;
  tags?: string[];
  search?: string;
  sort_by?:
    | "created_at"
    | "view_count"
    | "like_count"
    | "comment_count"
    | "is_pinned";
  sort_order?: "asc" | "desc";
}

export interface ForumCommentFilters {
  post_id?: string;
  parent_id?: string;
  level?: number;
  status?: CommentStatus;
  author_id?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  offset?: number;
}

// Admin specific types
export interface ForumPostWithStats extends ForumPost {
  recent_comments?: ForumComment[];
  top_liked_comments?: ForumComment[];
  engagement_rate?: number;
}

export interface ForumStats {
  total_posts: number;
  total_comments: number;
  total_likes: number;
  posts_today: number;
  comments_today: number;
  pending_posts: number;
  pending_comments: number;
  popular_categories: Array<{
    category: ForumCategory;
    post_count: number;
  }>;
}

// Upload types
export interface UploadedFile {
  url: string;
  filename: string;
  size: number;
  type: string;
}

export interface UploadResponse {
  success: boolean;
  data?: UploadedFile;
  error?: string;
}
