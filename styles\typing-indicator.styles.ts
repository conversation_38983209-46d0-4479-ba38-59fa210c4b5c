import styled, { keyframes } from "styled-components";

const bounce = keyframes`
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
`;

export const TypingContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 20px;
  background: #f5f5f5;
  border-top: 1px solid #f0f0f0;

  .dark & {
    background: #1a1a1a;
    border-top-color: #303030;
  }
`;

export const TypingBubble = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .dark & {
    background: #262626;
  }
`;

export const DotsContainer = styled.div`
  display: flex;
  gap: 2px;
`;

export const Dot = styled.div<{ $delay: number }>`
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: ${bounce} 1.4s infinite ease-in-out;
  animation-delay: ${props => props.$delay}s;
`;

export const TypingText = styled.span`
  font-size: 13px;
  color: #666;
  font-style: italic;

  .dark & {
    color: #999;
  }
`;
