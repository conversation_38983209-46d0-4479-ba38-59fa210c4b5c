"use client";

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Upload,
  Button,
  Avatar,
  message,
  Space,
} from 'antd';
import {
  UserOutlined,
  UploadOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { profileService } from '@/services/profile.service';
import type { Profile, UpdateProfileData } from '@/types/profile.types';
import type { UploadFile, RcFile } from 'antd/es/upload/interface';

interface ProfileUpdateModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (updatedProfile: Profile) => void;
  currentProfile: Profile | null;
}

interface FormData {
  full_name: string;
}

export default function ProfileUpdateModal({
  visible,
  onCancel,
  onSuccess,
  currentProfile,
}: ProfileUpdateModalProps) {
  const [form] = Form.useForm<FormData>();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // Reset form when modal opens/closes or profile changes
  useEffect(() => {
    if (visible && currentProfile) {
      form.setFieldsValue({
        full_name: currentProfile.full_name,
      });
      setAvatarUrl(currentProfile.avatar_url || '');
      setFileList([]);
    } else if (!visible) {
      form.resetFields();
      setAvatarUrl('');
      setFileList([]);
    }
  }, [visible, currentProfile, form]);

  // Handle avatar upload
  const handleAvatarUpload = async (file: RcFile): Promise<boolean> => {
    if (!currentProfile) {
      message.error('Không tìm thấy thông tin profile');
      return false;
    }

    // Validate file type
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('Chỉ được upload file ảnh!');
      return false;
    }

    // Validate file size (max 5MB)
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('Kích thước ảnh phải nhỏ hơn 5MB!');
      return false;
    }

    setUploading(true);
    try {
      const { url, error } = await profileService.uploadAvatarToTenan(
        currentProfile.id,
        file
      );

      if (error) {
        message.error(`Lỗi upload ảnh: ${error.message}`);
        return false;
      }

      if (url) {
        setAvatarUrl(url);
        message.success('Upload ảnh thành công!');
        return true;
      }

      return false;
    } catch (error) {
      message.error('Có lỗi xảy ra khi upload ảnh');
      return false;
    } finally {
      setUploading(false);
    }
  };

  // Handle form submit
  const handleSubmit = async (values: FormData) => {
    if (!currentProfile) {
      message.error('Không tìm thấy thông tin profile');
      return;
    }

    setLoading(true);
    try {
      const updateData: UpdateProfileData = {
        full_name: values.full_name,
      };

      // Only update avatar if a new one was uploaded
      if (avatarUrl && avatarUrl !== currentProfile.avatar_url) {
        updateData.avatar_url = avatarUrl;
      }

      const { profile, error } = await profileService.updateProfile(
        currentProfile.id,
        updateData
      );

      if (error) {
        message.error(`Lỗi cập nhật profile: ${error.message}`);
        return;
      }

      if (profile) {
        message.success('Cập nhật thông tin thành công!');
        onSuccess(profile);
        onCancel();
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi cập nhật thông tin');
      console.error('Update profile error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle modal cancel
  const handleCancel = () => {
    if (loading || uploading) {
      return;
    }
    onCancel();
  };

  return (
    <Modal
      title="Cập nhật thông tin cá nhân"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={500}
      maskClosable={!loading && !uploading}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        disabled={loading || uploading}
      >
        {/* Avatar Section */}
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Avatar
            size={100}
            src={avatarUrl || currentProfile?.avatar_url}
            icon={<UserOutlined />}
            style={{ marginBottom: 16 }}
          />
          <div>
            <Upload
              accept="image/*"
              showUploadList={false}
              beforeUpload={handleAvatarUpload}
              disabled={uploading || loading}
            >
              <Button
                icon={uploading ? <LoadingOutlined /> : <UploadOutlined />}
                loading={uploading}
                disabled={loading}
              >
                {uploading ? 'Đang upload...' : 'Thay đổi ảnh đại diện'}
              </Button>
            </Upload>
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
            Chỉ chấp nhận file ảnh, tối đa 5MB
          </div>
        </div>

        {/* Full Name Field */}
        <Form.Item
          name="full_name"
          label="Tên hiển thị"
          rules={[
            { required: true, message: 'Vui lòng nhập tên hiển thị!' },
            { max: 100, message: 'Tên hiển thị không được quá 100 ký tự!' },
            { min: 2, message: 'Tên hiển thị phải có ít nhất 2 ký tự!' },
          ]}
        >
          <Input
            placeholder="Nhập tên hiển thị của bạn"
            disabled={loading || uploading}
          />
        </Form.Item>

        {/* Action Buttons */}
        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button
              onClick={handleCancel}
              disabled={loading || uploading}
            >
              Hủy
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={uploading}
            >
              Cập nhật
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}
