"use client";

import { Typography } from "antd";
import styled from "styled-components";

const { Text } = Typography;

const StatusContainer = styled.div`
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background: transparent;
`;

const StatusText = styled(Text)`
  font-size: 11px;
  color: #999;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const TypingDots = styled.div`
  display: inline-flex;
  gap: 2px;
  
  span {
    width: 3px;
    height: 3px;
    background-color: #999;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
  
  @keyframes typing {
    0%, 80%, 100% {
      opacity: 0.3;
      transform: scale(0.8);
    }
    40% {
      opacity: 1;
      transform: scale(1);
    }
  }
`;

interface SendingStatusIndicatorProps {
  isVisible: boolean;
  message?: string;
}

export default function SendingStatusIndicator({ 
  isVisible, 
  message = "Đang gửi..." 
}: SendingStatusIndicatorProps) {
  if (!isVisible) return null;

  return (
    <StatusContainer>
      <StatusText>
        {message}
        <TypingDots>
          <span></span>
          <span></span>
          <span></span>
        </TypingDots>
      </StatusText>
    </StatusContainer>
  );
}