"use client";

import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  message,
  Popconfirm,
  Typography,
  Card,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useNewsService } from '@/hooks/useNewsService';
import type { PostWithSeo, PostFilters } from '@/types/news.types';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

export default function NewsListPage() {
  const router = useRouter();
  const {
    posts,
    loading,
    error,
    fetchPosts,
    deletePost,
    publishPost,
    unpublishPost,
  } = useNewsService();

  const [filters, setFilters] = useState<PostFilters>({
    published: undefined,
    search: '',
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  useEffect(() => {
    fetchPosts(filters);
  }, [fetchPosts, filters]);

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  const handleStatusFilter = (status: string | undefined) => {
    const published = status === 'published' ? true : status === 'draft' ? false : undefined;
    setFilters(prev => ({ ...prev, published }));
  };

  const handleDateRangeFilter = (dates: any) => {
    if (dates && dates.length === 2) {
      setFilters(prev => ({
        ...prev,
        startDate: dates[0].toISOString(),
        endDate: dates[1].toISOString(),
      }));
    } else {
      setFilters(prev => ({
        ...prev,
        startDate: undefined,
        endDate: undefined,
      }));
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const success = await deletePost(id);
      if (success) {
        message.success('Xóa bài viết thành công!');
        fetchPosts(filters);
      } else {
        message.error('Không thể xóa bài viết!');
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi xóa bài viết!');
    }
  };

  const handlePublish = async (id: string, isPublished: boolean) => {
    try {
      const result = isPublished ? await unpublishPost(id) : await publishPost(id);
      if (result) {
        message.success(`${isPublished ? 'Hủy xuất bản' : 'Xuất bản'} bài viết thành công!`);
        fetchPosts(filters);
      } else {
        message.error(`Không thể ${isPublished ? 'hủy xuất bản' : 'xuất bản'} bài viết!`);
      }
    } catch (error) {
      message.error('Có lỗi xảy ra!');
    }
  };

  const handleRefresh = () => {
    fetchPosts(filters);
  };

  const columns = [
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 300,
      ellipsis: true,
      render: (text: string, record: PostWithSeo) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>Slug: {record.seo?.slug || 'Chưa có'}</div>
        </div>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: PostWithSeo) => {
        const isPublished = record.published_at !== null;
        const config = isPublished
          ? { color: 'green', text: 'Đã xuất bản' }
          : { color: 'orange', text: 'Bản nháp' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: 'Tác giả',
      dataIndex: 'author_id',
      key: 'author_id',
      width: 150,
      render: (authorId: string) => authorId || 'Không xác định',
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm'),
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm'),
    },
    {
      title: 'SEO',
      key: 'seo',
      width: 80,
      render: (record: PostWithSeo) => (
        <Tag color={record.seo ? 'green' : 'red'}>
          {record.seo ? 'Có' : 'Không'}
        </Tag>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 200,
      render: (record: PostWithSeo) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => router.push(`/dashboard/news/${record.id}`)}
            title="Xem chi tiết"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => router.push(`/dashboard/news/edit/${record.id}`)}
            title="Chỉnh sửa"
          />
          <Button
            type="text"
            onClick={() => handlePublish(record.id, record.published_at !== null)}
            title={record.published_at !== null ? 'Hủy xuất bản' : 'Xuất bản'}
          >
            {record.published_at !== null ? 'Hủy XB' : 'Xuất bản'}
          </Button>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa bài viết này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Xóa"
            cancelText="Hủy"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              title="Xóa"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>Quản lý Tin tức</Title>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => router.push('/dashboard/news/create')}
              >
                Tạo bài viết mới
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                Làm mới
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Filters */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Search
              placeholder="Tìm kiếm theo tiêu đề, nội dung..."
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Trạng thái"
              allowClear
              style={{ width: '100%' }}
              onChange={handleStatusFilter}
            >
              <Option value="published">Đã xuất bản</Option>
              <Option value="draft">Bản nháp</Option>
              <Option value="archived">Lưu trữ</Option>
            </Select>
          </Col>
          <Col span={8}>
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              style={{ width: '100%' }}
              onChange={handleDateRangeFilter}
            />
          </Col>
        </Row>

        {/* Bulk Actions */}
        {selectedRowKeys.length > 0 && (
          <Row style={{ marginBottom: 16 }}>
            <Col>
              <Space>
                <span>Đã chọn {selectedRowKeys.length} bài viết</span>
                <Button size="small">Xuất bản hàng loạt</Button>
                <Button size="small">Hủy xuất bản hàng loạt</Button>
                <Button size="small" danger>Xóa hàng loạt</Button>
              </Space>
            </Col>
          </Row>
        )}

        {/* Error Display */}
        {error && (
          <div style={{ marginBottom: 16, color: 'red' }}>
            Lỗi: {error}
          </div>
        )}

        {/* Table */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={posts}
          rowKey="id"
          loading={loading}
          pagination={{
            total: posts.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} bài viết`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
}